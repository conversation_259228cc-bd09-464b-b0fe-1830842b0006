# JWT Authentication Integration with License System

This document outlines the approach for integrating JWT authentication between the Core backend and the License system.

## Overview

The Core backend uses JWT tokens issued by the License system for authentication. The tokens are verified using JSON Web Key Set (JWKS) to ensure they are valid and have not been tampered with.

## JWT Authentication Flow

1. User logs in to the License system
2. License system issues a JWT token
3. Client includes the JWT token in the `Authorization` header when making requests to the Core backend
4. Core backend verifies the token using JWKS
5. If valid, Core backend finds or creates a local user based on the JWT payload
6. Request proceeds with the authenticated user

## Implementation Details

### 1. Environment Variables

The following environment variables are required for JWT authentication:

```
LICENSE_API_URL=http://localhost:3000/api
LICENSE_JWKS_URL=http://localhost:3000/api/auth/jwks
LICENSE_JWT_ISSUER=https://your-license-app.vercel.app
LICENSE_JWT_AUDIENCE=https://your-license-app.vercel.app
```

### 2. JWT Auth Service

The `JwtAuthService` handles JWT verification and user synchronization:

- `verifyToken`: Verifies a JWT token using JWKS
- `findOrCreateLocalUser`: Finds or creates a local user based on the JWT payload

### 3. JWT Auth Middleware

The `JwtAuthMiddleware` intercepts requests and:

1. Extracts the JWT token from the `Authorization` header
2. Verifies the token using the `JwtAuthService`
3. Finds or creates a local user based on the JWT payload
4. Attaches the user to the request context

## JWT Payload

The JWT payload is expected to contain the following claims:

- `sub`: The License user ID (required)
- `email`: The user's email address
- `name`: The user's full name
- `given_name`: The user's first name
- `family_name`: The user's last name
- `iss`: The issuer of the token (must match `LICENSE_JWT_ISSUER`)
- `aud`: The audience of the token (must match `LICENSE_JWT_AUDIENCE`)
- `exp`: The expiration time of the token
- `iat`: The time the token was issued

## User Synchronization

When a user authenticates with a JWT token, the Core backend:

1. Extracts the License user ID from the `sub` claim
2. Checks if a local user already exists with this external ID
3. If found, updates the user's information if needed
4. If not found, creates a new user with the information from the JWT payload
5. Creates an external reference to link the Core user with the License user

## Security Considerations

1. **JWKS Caching**: The JWKS is cached to improve performance and reduce the number of requests to the License system.
2. **Token Validation**: Tokens are validated for issuer, audience, and expiration.
3. **User Synchronization**: User information is synchronized from the JWT payload to ensure it's up to date.

## Testing

To test the JWT authentication flow:

1. Obtain a JWT token from the License system
2. Make a request to a protected endpoint with the token in the `Authorization` header
3. Verify that the request is authenticated and the user is created or updated in the Core backend

## Troubleshooting

If authentication fails, check:

1. The JWT token is valid and not expired
2. The `LICENSE_JWKS_URL` is correct and accessible
3. The `LICENSE_JWT_ISSUER` and `LICENSE_JWT_AUDIENCE` match the values in the token
4. The `sub` claim is present in the token
