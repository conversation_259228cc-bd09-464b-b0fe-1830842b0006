import User from '#models/user'
import type { HttpContext } from '@adonisjs/core/http'
import type { NextFn } from '@adonisjs/core/types/http'
import { JwtAuthService as JwtAuthServiceImpl } from '#services/jwt_auth_service'

/**
 * Type for authentication guards
 */
type AuthGuards = 'keyAuth' | 'jwtAuth'

/**
 * Interface for the Key Auth service
 */
export interface KeyAuthService {
  getUser(): any
  isAuthenticated(): boolean
  isUser(userId: number): boolean
}

/**
 * Extend the HttpContext interface to include the keyAuth property
 */
declare module '@adonisjs/core/http' {
  interface HttpContext {
    keyAuth: KeyAuthService
  }
}

export default class SaasAuthMiddleware {
  async handle(ctx: HttpContext, next: NextFn, options: { guard: AuthGuards }) {
    let localUser: User | null = null

    if (options.guard === 'keyAuth') {
      // Get the authorization header
      const apiKey = ctx.request.header('X-Invois-Key')
      const userId = ctx.request.header('X-User-Id')

      if (!apiKey) {
        return ctx.response.unauthorized({ error: 'Missing API key' })
      }

      if (!userId) {
        return ctx.response.unauthorized({ error: 'Missing user ID' })
      }

      try {
        // Validate the API key
        if (apiKey !== process.env.LICENSE_INCOMING_API_KEY) {
          return ctx.response.unauthorized({ error: 'Invalid API key' })
        }

        // Validate the user
        localUser = await User.findBy('externalId', userId)
      } catch (error) {
        console.error('API Key validation error:', error)
        return ctx.response.unauthorized({ error: 'API Key validation error' })
      }
    } else if (options.guard === 'jwtAuth') {
      // Get the authorization header
      const authHeader = ctx.request.header('Authorization')
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return ctx.response.unauthorized({ error: 'Missing or invalid authorization header' })
      }

      const token = authHeader.substring(7) // Remove 'Bearer ' prefix

      try {
        // Verify the token using JWKS
        const verification = await JwtAuthServiceImpl.verifyToken(token)

        if (!verification.valid) {
          // Check if it's an expired token
          if (verification.error?.code === 'ERR_JWT_EXPIRED') {
            return ctx.response.unauthorized({
              error: 'Token expired',
              code: 'ERR_JWT_EXPIRED',
              message: verification.error.message,
            })
          }

          // Handle other validation errors
          return ctx.response.unauthorized({
            error: 'Invalid token',
            code: verification.error?.code,
            message: verification.error?.message,
          })
        }

        if (!verification.payload) {
          return ctx.response.unauthorized({ error: 'Invalid token payload' })
        }

        // Find or create the core user from the JWT payload
        localUser = await JwtAuthServiceImpl.findOrCreateLocalUser(verification.payload)
      } catch (error) {
        console.error('JWT validation error:', error)
        return ctx.response.unauthorized({ error: 'Invalid or expired token' })
      }
    }

    if (!localUser) {
      return ctx.response.unauthorized({ error: 'User not found' })
    }

    // Set the authenticated user in the request
    ;(ctx as any).user = localUser

    // Create a custom auth object that mimics the auth interface
    // This is a workaround since we can't directly set ctx.auth.user
    const customAuth = {
      user: localUser,
      isAuthenticated: true,
      isGuest: false,
      getUserOrFail: () => localUser,
    }

    // Replace the auth object with our custom one
    // This is a hack, but it's the only way to make ctx.auth.user work
    // without going through the normal authentication flow
    Object.defineProperty(ctx, 'auth', {
      value: {
        ...ctx.auth,
        ...customAuth,
        use: () => customAuth,
      },
      writable: true,
      configurable: true,
    })

    ctx.keyAuth = {
      getUser: () => localUser,
      isAuthenticated: () => true,
      isUser: (id: number) => localUser.id === id,
    }

    return next()
  }
}
