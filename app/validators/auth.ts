import vine from '@vinejs/vine'

const companyLoginOrCreateObject = vine.object({
  client_id: vine.string().trim(),
  client_secret: vine.string().trim(),
  name: vine
    .string()
    .unique(async (db, value) => {
      const company = await db.from('companies').where('name', value).first()
      return !company
    })
    .optional(),
  tin_code: vine
    .string()
    .unique(async (db, value) => {
      const company = await db.from('companies').where('tin_code', value).first()
      return !company
    })
    .optional(),
  msic_code: vine.string().optional(),
  country: vine.string().optional(),
  state: vine.string().optional(),
  zip_code: vine.string().optional(),
  address: vine.string().optional(),
  phone: vine.string().optional(),
  email: vine.string().optional(),
  registration_number: vine.string().optional(),
  registration_type: vine.string().optional(),
})

export const companyLoginOrCreateSchema = vine.compile(companyLoginOrCreateObject)

export const shopifyInstallSchema = vine.compile(
  vine.object({
    shop: vine.string().trim(), // Shopify shop domain
    access_token: vine.string().trim(), // Shopify app bridge access token
  })
)
