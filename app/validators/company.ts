import { stateCodesService, taxTypesService } from '#services/lhdn_service'
import { RegistrationType } from '#types/einvoice'
import vine from '@vinejs/vine'

const companyStoreObject = vine.object({
  name: vine.string().trim(),
  tin_code: vine.string().trim(),
  client_id: vine.string().trim(),
  client_secret: vine.string().trim(),
  scope: vine
    .string()
    .trim()
    .parse((value) => {
      if (!value) {
        return 'InvoicingAPI'
      }

      return value
    }),
  msic_code: vine.string().optional(),
  country: vine.string().optional(),
  state: vine.enum(stateCodesService.getAllStates().map((state) => state.State)),
  city: vine.string().optional(),
  zip_code: vine.string().optional(),
  address: vine.string().optional(),
  phone: vine.string().optional(),
  email: vine.string().optional(),
  registration_number: vine.string().optional(),
  registration_type: vine.string().optional(),
  business_activity_description: vine.string().optional(),
  applicable_tax_types: vine.array(vine.enum(taxTypesService.getAllCodes())).optional(),
  sales_tax_rates: vine.array(vine.string()).optional(),
  service_tax_rates: vine.array(vine.string()).optional(),
})

export const companyStoreSchema = vine.compile(companyStoreObject)

const companyUpdateObject = vine.object({
  name: vine.string().trim().optional(),
  tin_code: vine.string().trim().optional(),
  client_id: vine.string().trim().optional(),
  client_secret: vine.string().trim().optional(),
  scope: vine.string().trim().optional(),
  msic_code: vine.string().optional(),
  country: vine.string().optional(),
  state: vine.enum(stateCodesService.getAllStates().map((state) => state.State)).optional(),
  city: vine.string().optional(),
  zip_code: vine.string().optional(),
  address: vine.string().optional(),
  phone: vine.string().optional(),
  email: vine.string().optional(),
  registration_number: vine.string().optional(),
  registration_type: vine.string().optional(),
  business_activity_description: vine.string().optional(),
  applicable_tax_types: vine.array(vine.enum(taxTypesService.getAllCodes())).optional(),
  sales_tax_rates: vine.array(vine.string()).optional(),
  service_tax_rates: vine.array(vine.string()).optional(),
})

export const companyUpdateSchema = vine.compile(companyUpdateObject)

export const tinValidationSchema = vine.compile(
  vine.object({
    tin: vine.string().trim(),
    registrationType: vine.enum(Object.values(RegistrationType)),
    registrationNumber: vine.string().trim(),
  })
)
