import vine from '@vinejs/vine'
import {
  classificationCodesService,
  countryCodesService,
  paymentMethodsService,
  stateCodesService,
  taxTypesService,
  unitTypesService,
} from '#services/lhdn_service'
import { AdditionalDocumentReferenceType, RegistrationType } from '#types/einvoice'

const COUNTRY_STATES = stateCodesService.getAllStates().map((c) => c.Code)
const COUNTRY_NAMES = countryCodesService.getAllCountryCodes().map((c) => c.Code)
const PAYMENT_MODE = paymentMethodsService.getAllMethodsCode()

export const updateOrderSchema = vine.compile(
  vine.object({
    isReady: vine.boolean().optional(), // to determine if the invoice (order row) created is ready for submission
    isConsolidate: vine.boolean().optional(), // to determine if buyer info should be hardcoded with consolidate value or user supplied value

    // Buyer
    buyer: vine
      .object({
        name: vine.string(),
        tin: vine.string(),
        registrationType: vine.enum(RegistrationType).optional(),
        registrationNumber: vine.string().optional(),
        sstRegistrationNumber: vine.string(),
        email: vine.string().email().optional(),
        //@ts-ignore
        address: vine.object({
          addressLine0: vine.string(),
          addressLine1: vine.string().optional(),
          addressLine2: vine.string().optional(),
          postalZone: vine.string().optional(),
          cityName: vine.string(),
          state: vine.string(),
          country: vine.string(),
        }),
        contactNumber: vine.string(),
      })
      .optional()
      .requiredWhen('isConsolidate', '=', false), // required if not consolidate

    // Invoice Specification
    invoiceCode: vine.string().optional(),
    invoiceDateTime: vine
      .object({
        date: vine.date({ formats: { utc: true } }),
        time: vine.date({ formats: { utc: true } }),
      })
      .optional(),

    // Line item
    lineItems: vine
      .array(
        vine.object({
          id: vine.string(),
          classifications: vine.array(
            vine.enum((field) => {
              if (field.data.isConsolidate) {
                return [classificationCodesService.getCodeByNumber('004')!.Code] as const
              }

              return classificationCodesService.getAllCodes().map((c) => c.Code)
            })
          ),
          description: vine.string().trim(),
          unit: vine.object({
            price: vine.number(),
            count: vine.number(),
            code: vine.enum(unitTypesService.getAllTypes().map((c) => c.Code)).optional(),
          }),
          taxDetails: vine
            .array(
              vine.object({
                taxType: vine.enum(taxTypesService.getAllTypes().map((c) => c.Code)),
                taxRate: vine.object({
                  ratePerUnit: vine.number().optional().requiredIfMissing('percentage'),
                  percentage: vine.number().optional().requiredIfMissing('ratePerUnit'),
                }),
              })
            )
            .distinct('taxType'),
          taxExemption: vine
            .object({
              taxableAmount: vine.number(),
              reason: vine.string(),
            })
            .optional(),
          allowanceCharges: vine
            .array(
              vine.object({
                amount: vine.number().optional().requiredIfMissing('rate'),
                rate: vine.number().max(100).optional().requiredIfMissing('amount'),
                reason: vine.string().trim(),
                isCharge: vine.boolean(),
              })
            )
            .optional(),
          tarriffCode: vine.string().optional(),
          originCountry: vine.enum(COUNTRY_NAMES),
        })
      )
      .optional(),

    invoiceLevelAllowanceCharge: vine
      .object({
        discount: vine
          .object({
            amount: vine.number(),
            reason: vine.string(),
          })
          .optional()
          .requiredIfMissing('fee'),

        fee: vine
          .object({
            amount: vine.number(),
            reason: vine.string(),
          })
          .optional()
          .requiredIfMissing('discount'),
      })
      .optional(),

    foreignCurrency: vine
      .object({
        currencyCode: vine.string(),
        currencyExchangeRate: vine.number(),
      })
      .optional(),

    billingPeriod: vine
      .object({
        frequency: vine.string(),
        startDate: vine.date({ formats: { utc: true } }),
        endDate: vine.date({ formats: { utc: true } }),
      })
      .optional(),

    payment: vine
      .object({
        mode: vine.enum(PAYMENT_MODE),
        terms: vine.string(),
      })
      .optional(),

    prePayment: vine
      .object({
        amount: vine.number(),
        date: vine.date({ formats: { utc: true } }),
        time: vine.date({ formats: { utc: true } }),
        referenceNumber: vine.string(),
      })
      .optional(),

    billingReferenceNumber: vine.string().optional(),

    additionalDocumentReference: vine
      .array(
        vine.object({
          id: vine.string().trim(),
          type: vine.enum(Object.values(AdditionalDocumentReferenceType)).optional(),
          description: vine.string().trim().optional(),
        })
      )
      .optional(),

    externalId: vine.string().optional(),

    deliveryDetails: vine
      .object({
        recipientName: vine.string().trim().optional(),
        // @ts-ignore
        recipientAddress: vine
          .object({
            addressLine0: vine.string().trim(),
            addressLine1: vine.string().trim().optional(),
            addressLine2: vine.string().trim().optional(),
            cityName: vine.string().trim(),
            state: vine.enum(COUNTRY_STATES),
            country: vine.enum(COUNTRY_NAMES),
            postalZone: vine.string().trim().optional(),
          })
          .optional(),
        recipientTin: vine.string().minLength(13).maxLength(14).trim().optional(),
        recipientRegistration: vine
          .object({
            type: vine.enum(Object.values(RegistrationType)),
            number: vine.string().trim(),
          })
          .optional(),
        shipmentDetails: vine
          .object({
            shipperInternalTrackingId: vine.string().trim(),
            amount: vine.number(),
            currencyCode: vine.string().trim(),
            allowanceChargeReason: vine.string().trim(),
          })
          .optional(),
      })
      .optional(),
  })
)

export const cancelOrderSchema = vine.compile(
  vine.object({
    reason: vine.string(),
  })
)
