import {
  classificationCodesService,
  countryCodesService,
  msicsService,
  paymentMethodsService,
  stateCodesService,
  taxTypesService,
  unitTypesService,
} from '#services/lhdn_service'
import { AdditionalDocumentReferenceType, RegistrationType } from '#types/einvoice'
import vine from '@vinejs/vine'

// Notes:
// 1. String max length is 255 because max db length is 255

const COUNTRY_NAMES = countryCodesService.getAllCountryCodes().map((c) => c.Code)
const COUNTRY_STATES = stateCodesService.getAllStates().map((c) => c.Code)
const MSIC_CODES = msicsService.getAllCodes().map((c) => c.Code)
const PAYMENT_MODE = paymentMethodsService.getAllMethodsCode()

// Shopify E-Invoice Schema
export const shopifyEinvoiceSchema = vine.compile(
  vine.array(
    vine.object({
      // pending implementation
      id: vine.string(),
      name: vine.string(),
      taxesIncluded: vine.boolean(),
      taxLines: vine.array(
        vine.object({
          priceSet: vine.object({
            shopMoney: vine.object({
              amount: vine.number(),
              currencyCode: vine.string(),
            }),
          }),
          rate: vine.number(),
          title: vine.string(),
        })
      ),
      createdAt: vine.date({ formats: { utc: true } }),
      lineItems: vine.object({
        nodes: vine.array(
          vine.object({
            id: vine.string(),
            quantity: vine.number(),
            discountedUnitPriceSet: vine.object({
              shopMoney: vine.object({
                amount: vine.number(),
                currencyCode: vine.string(),
              }),
            }),
            originalUnitPriceSet: vine.object({
              shopMoney: vine.object({
                amount: vine.number(),
                currencyCode: vine.string(),
              }),
            }),
            product: vine.object({
              descriptionHtml: vine.string().optional(),
            }),
          })
        ),
      }),
    })
  )
)

export const selfServeEinvoicePortalSchema = vine.compile(
  vine.object({
    // Customer info
    businessRegistration: vine.object({
      regType: vine.enum(Object.values(RegistrationType)),
      newNumber: vine.string().minLength(12).maxLength(20),
      oldNumber: vine.string().maxLength(255).optional(), // TODO: To confirm if needed, apparently invoice v1 does not need this.
    }),
    tin: vine.string().minLength(13).maxLength(14),
    name: vine.string().maxLength(255),
    email: vine.string().maxLength(255).optional(),
    contactNumber: vine.string().maxLength(20),
    msic: vine.enum(MSIC_CODES),
    sstRegistrationNumber: vine.string().maxLength(35).optional(),
    //@ts-ignore
    address: vine.object({
      addressLine0: vine.string().maxLength(150),
      addressLine1: vine.string().maxLength(150).optional(),
      addressLine2: vine.string().maxLength(150).optional(),
      cityName: vine.string().maxLength(150),
      state: vine.enum(COUNTRY_STATES),
      country: vine.enum(COUNTRY_NAMES),
      postalZone: vine.string().maxLength(150),
    }),

    // Invoice info
    invoiceCodeNumber: vine.string().maxLength(255),
    invoiceAmount: vine.number(),
  })
)

export const syncInvoiceStatusScehma = vine.compile(
  vine.object({
    syncDocuments: vine.array(
      vine.object({
        documentNumber: vine.string().trim(),
        status: vine.enum(['Submitted', 'Cancelled', 'Valid', 'Invalid', 'NotSubmitted']),
      })
    ),
  })
)

export const sellerReviewSelfServeRequestSchema = vine.compile(
  vine.object({
    // Flags
    isReady: vine.boolean(),
    isConsolidate: vine.boolean(),
    status: vine.enum(['Approve', 'Reject']),

    // Einvoice v1 fields
    invoiceLevelAllowanceCharge: vine
      .object({
        discount: vine
          .object({
            amount: vine.number(),
            reason: vine.string(),
          })
          .optional()
          .requiredIfMissing('fee'),

        fee: vine
          .object({
            amount: vine.number(),
            reason: vine.string(),
          })
          .optional()
          .requiredIfMissing('discount'),
      })
      .optional(),

    foreignCurrency: vine
      .object({
        currencyCode: vine.string().trim(),
        currencyExchangeRate: vine.number(),
      })
      .optional(),

    billingPeriod: vine
      .object({
        frequency: vine.string().trim(),
        startDate: vine.date({ formats: { utc: true } }),
        endDate: vine.date({ formats: { utc: true } }),
      })
      .optional(),

    payment: vine
      .object({
        mode: vine.enum(PAYMENT_MODE),
        terms: vine.string().trim().optional(),
      })
      .optional(),

    prePayment: vine
      .object({
        amount: vine.number(),
        date: vine.date({ formats: { utc: true } }),
        time: vine.date({ formats: { utc: true } }),
        referenceNumber: vine.string().trim(),
      })
      .optional(),

    billingReferenceNumber: vine.string().trim().optional(),

    additionalDocumentReference: vine
      .array(
        vine.object({
          id: vine.string().trim(),
          type: vine.enum(Object.values(AdditionalDocumentReferenceType)),
          description: vine.string().trim().optional(),
        })
      )
      .optional(),

    deliveryDetails: vine
      .object({
        recipientName: vine.string().trim().optional(),
        // @ts-ignore
        recipientAddress: vine
          .object({
            addressLine0: vine.string().trim(),
            addressLine1: vine.string().trim().optional(),
            addressLine2: vine.string().trim().optional(),
            cityName: vine.string().trim(),
            state: vine.enum(COUNTRY_STATES),
            country: vine.enum(COUNTRY_NAMES),
            postalZone: vine.string().trim().optional(),
          })
          .optional(),
        recipientTin: vine.string().minLength(13).maxLength(14).trim().optional(),
        recipientRegistration: vine
          .object({
            type: vine.enum(Object.values(RegistrationType)),
            number: vine.string().trim(),
          })
          .optional(),
        shipmentDetails: vine
          .object({
            shipperInternalTrackingId: vine.string().trim(),
            amount: vine.number(),
            currencyCode: vine.string().trim(),
            allowanceChargeReason: vine.string().trim(),
          })
          .optional(),
      })
      .optional(),

    // Compulsory
    invoiceDateTime: vine.date({ formats: { utc: true } }),

    lineItems: vine.array(
      vine.object({
        id: vine.string(),
        classifications: vine.array(
          vine.enum((field) => {
            if (field.data.isConsolidate) {
              return [classificationCodesService.getCodeByNumber('004')!.Code] as const
            }

            return classificationCodesService.getAllCodes().map((c) => c.Code)
          })
        ),
        description: vine.string().trim(),
        unit: vine.object({
          price: vine.number(),
          count: vine.number(),
          code: vine.enum(unitTypesService.getAllTypes().map((c) => c.Code)).optional(),
        }),
        taxDetails: vine
          .array(
            vine.object({
              taxType: vine.enum(taxTypesService.getAllTypes().map((c) => c.Code)),
              taxRate: vine.object({
                ratePerUnit: vine.number().optional().requiredIfMissing('percentage'),
                percentage: vine
                  .number()
                  .optional()
                  .requiredIfMissing('ratePerUnit')
                  .requiredWhen('taxType', '=', taxTypesService.getByCode('06')!.Code),
              }),
            })
          )
          .distinct('taxType'),
        taxExemption: vine
          .object({
            taxableAmount: vine.number(),
            reason: vine.string(),
          })
          .optional(),
        allowanceCharges: vine
          .array(
            vine.object({
              amount: vine.number().optional().requiredIfMissing('rate'),
              rate: vine.number().max(100).optional().requiredIfMissing('amount'),
              reason: vine.string().trim(),
              isCharge: vine.boolean(),
            })
          )
          .optional(),
        tarriffCode: vine.string().optional(),
        originCountry: vine.enum(COUNTRY_NAMES),
      })
    ),
  })
)

export const manualInvoice = vine.object({
  isSelfBill: vine.boolean().optional(),

  isReady: vine.boolean(), // to determine if the invoice (order row) created is ready for submission
  isConsolidate: vine.boolean().optional(), // to determine if buyer info should be hardcoded with consolidate value or user supplied value

  // Buyer
  buyer: vine
    .object({
      name: vine.string(),
      tin: vine.string(),
      registrationType: vine.enum(RegistrationType).optional(),
      registrationNumber: vine.string().optional(),
      sstRegistrationNumber: vine.string(),
      email: vine.string().email().optional(),
      //@ts-ignore
      address: vine.object({
        addressLine0: vine.string(),
        addressLine1: vine.string().optional(),
        addressLine2: vine.string().optional(),
        postalZone: vine.string().optional(),
        cityName: vine.string(),
        state: vine.string(),
        country: vine.string(),
      }),
      contactNumber: vine.string(),
    })
    .optional()
    .requiredWhen('isConsolidate', '=', false), // required if not consolidate

  // Invoice Specification
  invoiceCode: vine.string(),
  invoiceDateTime: vine.object({
    date: vine.date({ formats: { utc: true } }),
    time: vine.date({ formats: { utc: true } }),
  }),

  // Line item
  lineItems: vine.array(
    vine.object({
      id: vine.string(),
      classifications: vine.array(
        vine.enum((field) => {
          if (field.data.isConsolidate) {
            return [classificationCodesService.getCodeByNumber('004')!.Code] as const
          }

          return classificationCodesService.getAllCodes().map((c) => c.Code)
        })
      ),
      description: vine.string().trim(),
      unit: vine.object({
        price: vine.number(),
        count: vine.number(),
        code: vine.enum(unitTypesService.getAllTypes().map((c) => c.Code)).optional(),
      }),
      taxDetails: vine
        .array(
          vine.object({
            taxType: vine.enum(taxTypesService.getAllTypes().map((c) => c.Code)),
            taxRate: vine.object({
              ratePerUnit: vine.number().optional().requiredIfMissing('percentage'),
              percentage: vine.number().optional().requiredIfMissing('ratePerUnit'),
            }),
          })
        )
        .distinct('taxType'),
      taxExemption: vine
        .object({
          taxableAmount: vine.number(),
          reason: vine.string(),
        })
        .optional(),
      allowanceCharges: vine
        .array(
          vine.object({
            amount: vine.number().optional().requiredIfMissing('rate'),
            rate: vine.number().max(100).optional().requiredIfMissing('amount'),
            reason: vine.string().trim(),
            isCharge: vine.boolean(),
          })
        )
        .optional(),
      tarriffCode: vine.string().optional(),
      originCountry: vine.enum(COUNTRY_NAMES),
    })
  ),

  // Optionals
  invoiceLevelAllowanceCharge: vine
    .object({
      discount: vine
        .object({
          amount: vine.number(),
          reason: vine.string(),
        })
        .optional()
        .requiredIfMissing('fee'),

      fee: vine
        .object({
          amount: vine.number(),
          reason: vine.string(),
        })
        .optional()
        .requiredIfMissing('discount'),
    })
    .optional(),

  foreignCurrency: vine
    .object({
      currencyCode: vine.string(),
      currencyExchangeRate: vine.number(),
    })
    .optional(),

  billingPeriod: vine
    .object({
      frequency: vine.string(),
      startDate: vine.date({ formats: { utc: true } }),
      endDate: vine.date({ formats: { utc: true } }),
    })
    .optional(),

  payment: vine
    .object({
      mode: vine.enum(PAYMENT_MODE),
      terms: vine.string(),
    })
    .optional(),

  prePayment: vine
    .object({
      amount: vine.number(),
      date: vine.date({ formats: { utc: true } }),
      time: vine.date({ formats: { utc: true } }),
      referenceNumber: vine.string(),
    })
    .optional(),

  billingReferenceNumber: vine.string().optional(),

  additionalDocumentReference: vine
    .array(
      vine.object({
        id: vine.string().trim(),
        type: vine.enum(Object.values(AdditionalDocumentReferenceType)).optional(),
        description: vine.string().trim().optional(),
      })
    )
    .optional(),
})

export const manualInvoiceSingleSchema = vine.compile(manualInvoice)

export const manualInvoiceMultiSchema = vine.compile(
  vine.array(manualInvoice).distinct(['invoiceCode'])
)
