import type { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, hasMany } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany } from '@adonisjs/lucid/types/relations'
import SubmittedDocument from './submitted_document.js'
import User from './user.js'
import Company from './company.js'

export class DocumentSubmission extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare userId: number

  @column()
  declare companyId: number

  @column()
  declare submissionUid: string
  // SubmissionUid empty means submission failed.

  @column()
  declare totalDocuments: number

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User> // submitted by which user

  @belongsTo(() => Company)
  declare company: BelongsTo<typeof Company> // this submitted docs belong to which company

  @hasMany(() => SubmittedDocument, {
    serializeAs: 'submitted_documents',
    foreignKey: 'documentSubmissionId',
    localKey: 'id',
  })
  declare submittedDocuments: HasMany<typeof SubmittedDocument>
}
