import type { DateTime } from 'luxon'
import hash from '@adonisjs/core/services/hash'
import { compose } from '@adonisjs/core/helpers'
import { BaseModel, column, hasMany } from '@adonisjs/lucid/orm'
import { withAuthFinder } from '@adonisjs/auth/mixins/lucid'
import { DbAccessTokensProvider } from '@adonisjs/auth/access_tokens'
import { DocumentSubmission } from './document_submission.js'
import type { HasMany } from '@adonisjs/lucid/types/relations'
import Company from './company.js'
import { randomBytes } from 'node:crypto'
import ShopifyStore from './shopify_store.js'
import Order from './order.js'
import SubmittedDocument from './submitted_document.js'

const AuthFinder = withAuthFinder(() => hash.use('scrypt'), {
  uids: ['email'],
  passwordColumnName: 'password',
})

/**
 * User model that supports multiple authentication environments:
 * - Shopify authentication for Shopify app users
 * - License authentication via NextJS/better-auth for SaaS users
 * - Legacy API key authentication for existing integrations
 */
export default class User extends compose(BaseModel, AuthFinder) {
  @column({ isPrimary: true })
  declare id: number

  @column()
  declare fullName: string | null

  @column()
  declare email: string

  @column({ serializeAs: null })
  declare password: string | null

  @column()
  declare apiKey: string | null

  @column()
  declare authType: 'password' | 'license' | 'shopify' | 'api' | null

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime | null

  static accessTokens = DbAccessTokensProvider.forModel(User)

  /**
   * External ID from the License auth service (UUID)
   */
  @column()
  declare externalId: string | null

  /**
   * Generate a unique API key for the user
   */
  static generateApiKey(): string {
    return randomBytes(32).toString('hex')
  }

  // Relationships
  @hasMany(() => DocumentSubmission, {
    serializeAs: 'document_submissions',
    foreignKey: 'userId',
    localKey: 'id',
  })
  declare documentSubmissions: HasMany<typeof DocumentSubmission>

  @hasMany(() => Company, {
    foreignKey: 'userId',
    localKey: 'id',
  })
  declare companies: HasMany<typeof Company>

  @hasMany(() => ShopifyStore, {
    serializeAs: 'shopify_stores',
    foreignKey: 'userId',
    localKey: 'id',
  })
  declare shopifyStores: HasMany<typeof ShopifyStore>

  @hasMany(() => Order, {
    foreignKey: 'userId',
    localKey: 'id',
  })
  declare orders: HasMany<typeof Order>

  @hasMany(() => SubmittedDocument, {
    serializeAs: 'submitted_documents',
    foreignKey: 'userId',
    localKey: 'id',
  })
  declare submittedDocuments: HasMany<typeof SubmittedDocument>
  // Relationships
}
