import type { DateTime } from 'luxon'
import { BaseModel, belongsTo, column, computed, hasMany } from '@adonisjs/lucid/orm'
import type { BelongsTo, HasMany } from '@adonisjs/lucid/types/relations'
import Company from './company.js'
import SubmittedDocument from './submitted_document.js'
import type {
  AdditionalDocumentReference,
  BillingPeriod,
  Buyer,
  DeliveryDetails,
  ForeignCurrency,
  InvoiceDateTime,
  InvoiceLevelAllowanceCharge,
  InvoiceLevelLineItemTaxes,
  LegalMonetaryTotal,
  LineItem,
  Payment,
  Prepayment,
  Supplier,
} from '#types/einvoice'
import User from './user.js'

export default class Order extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  // Foreign Keys
  @column()
  declare companyId: number // belongs to which company

  @column()
  declare userId: number // belongs to which user (to search in db easier, rather than using companyId and check if companyId belongs to user.)
  // Foreign Keys

  @column()
  declare isSubmittedToLhdn: boolean // status valid and submitted cannot change..
  // if true, cannot be deleted/edited, if false can be edited/deleted
  // seller can edit with credit note after submitted to LHDN(not implemented yet)

  @column()
  declare invoiceCode: string // INVOICE CODE CANNOT BE REPEATED

  @column({
    prepare: (value: Buyer) => JSON.stringify(value),
  })
  declare buyer: Buyer

  @column({
    prepare: (value: Supplier) => JSON.stringify(value),
  })
  declare supplier: Supplier

  @column({
    prepare: (value: DeliveryDetails) => JSON.stringify(value),
  })
  declare deliveryDetails?: DeliveryDetails

  @column({
    prepare: (value: InvoiceDateTime) => JSON.stringify(value),
  })
  declare invoiceDateTime: InvoiceDateTime

  @column({
    prepare: (value: ForeignCurrency) => JSON.stringify(value),
  })
  declare foreignCurrency?: ForeignCurrency

  @column({
    prepare: (value: BillingPeriod) => JSON.stringify(value),
  })
  declare billingPeriod?: BillingPeriod

  @column({
    prepare: (value: LineItem[]) => JSON.stringify(value),
  })
  declare lineItems: LineItem[]

  @column({
    prepare: (value: Payment) => JSON.stringify(value),
  })
  declare payment?: Payment

  @column({
    prepare: (value: Prepayment) => JSON.stringify(value),
  })
  declare prePayment?: Prepayment

  @column()
  declare billingReferenceNumber?: string

  @column({
    prepare: (value: LegalMonetaryTotal) => JSON.stringify(value),
  })
  declare legalMonetaryTotal: LegalMonetaryTotal

  @column({
    prepare: (value: InvoiceLevelLineItemTaxes) => JSON.stringify(value),
  })
  declare invoiceLevelLineItemTaxes: InvoiceLevelLineItemTaxes

  @column({
    prepare: (value: InvoiceLevelAllowanceCharge) => JSON.stringify(value),
  })
  declare invoiceLevelAllowanceCharge?: InvoiceLevelAllowanceCharge

  @column({
    prepare: (value: AdditionalDocumentReference[]) => JSON.stringify(value),
  })
  declare additionalDocumentReference?: AdditionalDocumentReference[]

  // determine if this order is a consolidated order
  // whether an einvoice submitted as consolidated invoice to LHDN can be reverted is not mentioned in official FAQ
  // hence, consolidated order should only be automatically submitted at the end of the month, since customer could request the e-invoice within the period of each month
  // hence make sure each consolidate e-invoice will only be submitted within first week of next month
  // isConsolidate should be set to false when buyer info is present.
  @column()
  declare isConsolidate: boolean

  @column()
  declare isReady: boolean // is this order ready for submission

  @column()
  declare externalId: string
  // eg. shopify order unique id gid://shopify.com/123

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Computed
  @computed({ serializeAs: 'status' })
  public get status():
    | 'Draft' // custom status when is not ready
    | 'Pending' // custom status for ready but not submitted
    | 'Submitted' // When is ready and is submitted, this is also MyInvois API status
    | 'Cancelled'
    | 'Valid'
    | 'Invalid' {
    // sort & find latest submitted document

    const allSubmittedDocuments = this.submittedDocuments
    const latestSubmittedDocument =
      allSubmittedDocuments && allSubmittedDocuments.length > 0
        ? allSubmittedDocuments[0]
        : undefined

    return this.isReady && ((!this.isSubmittedToLhdn && !latestSubmittedDocument) || (latestSubmittedDocument?.status == 'Cancelled'))
      ? 'Pending'
      : !this.isReady
        ? 'Draft'
        : latestSubmittedDocument?.status || 'Submitted'
  }

  // Add the computed values here such as taxtotal
  // Computed

  // Relationship
  @belongsTo(() => Company)
  declare company: BelongsTo<typeof Company>

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @hasMany(() => SubmittedDocument, {
    foreignKey: 'orderId',
    localKey: 'id',
    serializeAs: 'submitted_documents',
  })
  declare submittedDocuments: HasMany<typeof SubmittedDocument>
  // Relationship
}
