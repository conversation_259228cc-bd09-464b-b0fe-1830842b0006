import type { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'
import { DocumentSubmission } from './document_submission.js'
import Order from './order.js'
import Company from './company.js'
import User from './user.js'

export default class SubmittedDocument extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  // Foreign Keys
  @column()
  declare documentSubmissionId: number

  @column()
  declare orderId: number

  @column()
  declare companyId: number

  @column()
  declare userId: number // easier to preload and search from db (instead of preload company and then use company to find the user)
  // Foreign Keys

  @column()
  declare code: string // Code for document could be invoice code, credit note code, debit note code etc.

  @column()
  declare uuid: string | null

  @column()
  declare status: 'Submitted' | 'Cancelled' | 'Valid' | 'Invalid'
  // LHDN only has Submitted, Cancelled, Valid, Invalid

  @column()
  declare failReason: string

  @column({
    prepare: (value: any) => JSON.stringify(value),
  })
  declare failDetails: any[]

  @column({
    prepare: (value: Object) => JSON.stringify(value),
  })
  declare documentDetails: Object // This is UBL format snapshot

  @column()
  declare type: 'Invoice' // | 'Credit_Note' | 'Debit_Note'

  @column()
  declare cancelReason: string

  @column()
  declare longId: string
  // eg. LIJAF97HJJKH 8298KHADH0990 8570FDKK9S2LSIU HB377373
  // Unique long temporary Id that can be used to query document data anonymously. The long id will be returned only for valid documents @ https://sdk.myinvois.hasil.gov.my/einvoicingapi/07-get-document/

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime

  // Relationships
  @belongsTo(() => DocumentSubmission)
  declare submission: BelongsTo<typeof DocumentSubmission>

  @belongsTo(() => Order)
  declare order: BelongsTo<typeof Order>

  @belongsTo(() => Company)
  declare company: BelongsTo<typeof Company>

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>
  // Relationships
}
