import type { DateTime } from 'luxon'
import { BaseModel, column, belongsTo } from '@adonisjs/lucid/orm'
import User from './user.js'
import type { BelongsTo } from '@adonisjs/lucid/types/relations'

export default class ShopifyStore extends BaseModel {
  @column({ isPrimary: true })
  declare id: number

  // Store identification
  @column()
  declare shopDomain: string

  @column()
  declare shopName: string | null

  @column()
  declare accessToken: string | null

  // Webhook settings
  @column()
  declare webhookTopic: string | null

  @column()
  declare webhookAddress: string | null

  // Relationship to users
  @column()
  declare userId: number

  @belongsTo(() => User)
  declare user: BelongsTo<typeof User>

  @column.dateTime({ autoCreate: true })
  declare createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  declare updatedAt: DateTime
}
