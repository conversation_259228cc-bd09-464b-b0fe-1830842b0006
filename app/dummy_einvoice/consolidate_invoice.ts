export const consolidatedEInvoiceV1 = {
  "_D": "urn:oasis:names:specification:ubl:schema:xsd:Invoice-2",
  "_A": "urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2",
  "_B": "urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2",
  "Invoice": [
    {
      "ID": [
        {
          "_": "JSON-INV12345"
        }
      ],
      "IssueDate": [
        {
          "_": "2024-07-23"
        }
      ],
      "IssueTime": [
        {
          "_": "00:30:00Z"
        }
      ],
      "InvoiceTypeCode": [
        {
          "_": "01",
          "listVersionID": "1.0"
        }
      ],
      "DocumentCurrencyCode": [
        {
          "_": "MYR"
        }
      ],
      "TaxCurrencyCode": [
        {
          "_": "MYR"
        }
      ],
      "AccountingSupplierParty": [
        {
          "Party": [
            {
              "IndustryClassificationCode": [
                {
                  "_": "46510",
                  "name": "Wholesale of computer hardware, software and peripherals"
                }
              ],
              "PartyIdentification": [
                {
                  "ID": [
                    {
                      "_": "IG40125832070",
                      "schemeID": "TIN"
                    }
                  ]
                },
                {
                  "ID": [
                    {
                      "_": "************",
                      "schemeID": "NRIC"
                    }
                  ]
                },
                {
                  "ID": [
                    {
                      "_": "NA",
                      "schemeID": "SST"
                    }
                  ]
                },
                {
                  "ID": [
                    {
                      "_": "NA",
                      "schemeID": "TTX"
                    }
                  ]
                }
              ],
              "PostalAddress": [
                {
                  "CityName": [
                    {
                      "_": "Kuala Lumpur"
                    }
                  ],
                  "PostalZone": [
                    {
                      "_": "50480"
                    }
                  ],
                  "CountrySubentityCode": [
                    {
                      "_": "10"
                    }
                  ],
                  "AddressLine": [
                    {
                      "Line": [
                        {
                          "_": "Lot 66"
                        }
                      ]
                    },
                    {
                      "Line": [
                        {
                          "_": "Bangunan Merdeka"
                        }
                      ]
                    },
                    {
                      "Line": [
                        {
                          "_": "Persiaran Jaya"
                        }
                      ]
                    }
                  ],
                  "Country": [
                    {
                      "IdentificationCode": [
                        {
                          "_": "MYS",
                          "listID": "ISO3166-1",
                          "listAgencyID": "6"
                        }
                      ]
                    }
                  ]
                }
              ],
              "PartyLegalEntity": [
                {
                  "RegistrationName": [
                    {
                      "_": "Heng's Freelancer"
                    }
                  ]
                }
              ],
              "Contact": [
                {
                  "Telephone": [
                    {
                      "_": "+***********"
                    }
                  ],
                  "ElectronicMail": [
                    {
                      "_": "<EMAIL>"
                    }
                  ]
                }
              ]
            }
          ]
        }
      ],
      "AccountingCustomerParty": [
        {
          "Party": [
            {
              "PostalAddress": [
                {
                  "CityName": [
                    {
                      "_": ""
                    }
                  ],
                  "PostalZone": [
                    {
                      "_": ""
                    }
                  ],
                  "CountrySubentityCode": [
                    {
                      "_": ""
                    }
                  ],
                  "AddressLine": [
                    {
                      "Line": [
                        {
                          "_": "NA"
                        }
                      ]
                    },
                    {
                      "Line": [
                        {
                          "_": ""
                        }
                      ]
                    },
                    {
                      "Line": [
                        {
                          "_": ""
                        }
                      ]
                    }
                  ],
                  "Country": [
                    {
                      "IdentificationCode": [
                        {
                          "_": "",
                          "listID": "ISO3166-1",
                          "listAgencyID": "6"
                        }
                      ]
                    }
                  ]
                }
              ],
              "PartyLegalEntity": [
                {
                  "RegistrationName": [
                    {
                      "_": "Consolidated Buyer's"
                    }
                  ]
                }
              ],
              "PartyIdentification": [
                {
                  "ID": [
                    {
                      "_": "EI00000000010",
                      "schemeID": "TIN"
                    }
                  ]
                },
                {
                  "ID": [
                    {
                      "_": "NA",
                      "schemeID": "BRN"
                    }
                  ]
                },
                {
                  "ID": [
                    {
                      "_": "NA",
                      "schemeID": "SST"
                    }
                  ]
                },
                {
                  "ID": [
                    {
                      "_": "NA",
                      "schemeID": "TTX"
                    }
                  ]
                }
              ],
              "Contact": [
                {
                  "Telephone": [
                    {
                      "_": "NA"
                    }
                  ],
                  "ElectronicMail": [
                    {
                      "_": "NA"
                    }
                  ]
                }
              ]
            }
          ]
        }
      ],
      "TaxTotal": [
        {
          "TaxAmount": [
            {
              "_": 3000,
              "currencyID": "MYR"
            }
          ],
          "TaxSubtotal": [
            {
              "TaxableAmount": [
                {
                  "_": 30000,
                  "currencyID": "MYR"
                }
              ],
              "TaxAmount": [
                {
                  "_": 3000,
                  "currencyID": "MYR"
                }
              ],
              "TaxCategory": [
                {
                  "ID": [
                    {
                      "_": "01"
                    }
                  ],
                  "TaxScheme": [
                    {
                      "ID": [
                        {
                          "_": "OTH",
                          "schemeID": "UN/ECE 5153",
                          "schemeAgencyID": "6"
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        }
      ],
      "LegalMonetaryTotal": [
        {
          "LineExtensionAmount": [
            {
              "_": 30000,
              "currencyID": "MYR"
            }
          ],
          "TaxExclusiveAmount": [
            {
              "_": 30000,
              "currencyID": "MYR"
            }
          ],
          "TaxInclusiveAmount": [
            {
              "_": 33000,
              "currencyID": "MYR"
            }
          ],
          "PayableAmount": [
            {
              "_": 33000,
              "currencyID": "MYR"
            }
          ]
        }
      ],
      "InvoiceLine": [
        {
          "ID": [
            {
              "_": "1"
            }
          ],
          "InvoicedQuantity": [
            {
              "_": 1,
              "unitCode": "C62"
            }
          ],
          "LineExtensionAmount": [
            {
              "_": 10000,
              "currencyID": "MYR"
            }
          ],
          "TaxTotal": [
            {
              "TaxAmount": [
                {
                  "_": 1000,
                  "currencyID": "MYR"
                }
              ],
              "TaxSubtotal": [
                {
                  "TaxableAmount": [
                    {
                      "_": 10000,
                      "currencyID": "MYR"
                    }
                  ],
                  "TaxAmount": [
                    {
                      "_": 1000,
                      "currencyID": "MYR"
                    }
                  ],
                  "Percent": [
                    {
                      "_": 10
                    }
                  ],
                  "TaxCategory": [
                    {
                      "ID": [
                        {
                          "_": "01"
                        }
                      ],
                      "TaxScheme": [
                        {
                          "ID": [
                            {
                              "_": "OTH",
                              "schemeID": "UN/ECE 5153",
                              "schemeAgencyID": "6"
                            }
                          ]
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          ],
          "Item": [
            {
              "CommodityClassification": [
                {
                  "ItemClassificationCode": [
                    {
                      "_": "004",
                      "listID": "CLASS"
                    }
                  ]
                }
              ],
              "Description": [
                {
                  "_": "Receipt 001 - 100"
                }
              ],
              "OriginCountry": [
                {
                  "IdentificationCode": [
                    {
                      "_": "MYS"
                    }
                  ]
                }
              ]
            }
          ],
          "Price": [
            {
              "PriceAmount": [
                {
                  "_": 10000,
                  "currencyID": "MYR"
                }
              ]
            }
          ],
          "ItemPriceExtension": [
            {
              "Amount": [
                {
                  "_": 10000,
                  "currencyID": "MYR"
                }
              ]
            }
          ]
        },
        {
          "ID": [
            {
              "_": "2"
            }
          ],
          "InvoicedQuantity": [
            {
              "_": 1,
              "unitCode": "C62"
            }
          ],
          "LineExtensionAmount": [
            {
              "_": 20000,
              "currencyID": "MYR"
            }
          ],
          "TaxTotal": [
            {
              "TaxAmount": [
                {
                  "_": 2000,
                  "currencyID": "MYR"
                }
              ],
              "TaxSubtotal": [
                {
                  "TaxableAmount": [
                    {
                      "_": 20000,
                      "currencyID": "MYR"
                    }
                  ],
                  "TaxAmount": [
                    {
                      "_": 2000,
                      "currencyID": "MYR"
                    }
                  ],
                  "Percent": [
                    {
                      "_": 10
                    }
                  ],
                  "TaxCategory": [
                    {
                      "ID": [
                        {
                          "_": "01"
                        }
                      ],
                      "TaxScheme": [
                        {
                          "ID": [
                            {
                              "_": "OTH",
                              "schemeID": "UN/ECE 5153",
                              "schemeAgencyID": "6"
                            }
                          ]
                        }
                      ]
                    }
                  ]
                }
              ]
            }
          ],
          "Item": [
            {
              "CommodityClassification": [
                {
                  "ItemClassificationCode": [
                    {
                      "_": "004",
                      "listID": "CLASS"
                    }
                  ]
                }
              ],
              "Description": [
                {
                  "_": "Receipt 101 - 200"
                }
              ],
              "OriginCountry": [
                {
                  "IdentificationCode": [
                    {
                      "_": "MYS"
                    }
                  ]
                }
              ]
            }
          ],
          "Price": [
            {
              "PriceAmount": [
                {
                  "_": 20000,
                  "currencyID": "MYR"
                }
              ]
            }
          ],
          "ItemPriceExtension": [
            {
              "Amount": [
                {
                  "_": 20000,
                  "currencyID": "MYR"
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
