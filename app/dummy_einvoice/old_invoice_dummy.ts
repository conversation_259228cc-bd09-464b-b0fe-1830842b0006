export const oldInvoiceDummy = {
  _D: 'urn:oasis:names:specification:ubl:schema:xsd:Invoice-2',
  _A: 'urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2',
  _B: 'urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2',
  Invoice: [
    {
      ID: [
        {
          _: 'INV-2025-004',
        },
      ],
      IssueDate: [
        {
          _: '2025-05-08',
        },
      ],
      IssueTime: [
        {
          _: '08:53:03Z',
        },
      ],
      InvoiceTypeCode: [
        {
          _: '01',
          listVersionID: '1.0',
        },
      ],
      DocumentCurrencyCode: [
        {
          _: 'MYR',
        },
      ],
      TaxCurrencyCode: [
        {
          _: 'MYR',
        },
      ],
      InvoicePeriod: [
        {
          StartDate: [
            {
              _: '2025-03-01',
            },
          ],
          EndDate: [
            {
              _: '2025-03-31',
            },
          ],
          Description: [
            {
              _: 'Monthly',
            },
          ],
        },
      ],
      BillingReference: [
        {
          AdditionalDocumentReference: [
            {
              ID: [
                {
                  _: 'PO-2025-001',
                },
              ],
            },
          ],
        },
      ],
      AccountingSupplierParty: [
        {
          AdditionalAccountID: [
            {
              _: 'TTX12345',
              schemeAgencyName: 'Heng CH',
            },
          ],
          Party: [
            {
              IndustryClassificationCode: [
                {
                  _: '46491',
                  name: 'Wholesale of household goods',
                },
              ],
              PartyIdentification: [
                {
                  ID: [
                    {
                      _: 'IG40125832070',
                      schemeID: 'TIN',
                    },
                  ],
                },
                {
                  ID: [
                    {
                      _: '************',
                      schemeID: 'NRIC',
                    },
                  ],
                },
                {
                  ID: [
                    {
                      _: 'W10-1234567',
                      schemeID: 'SST',
                    },
                  ],
                },
                {
                  ID: [
                    {
                      _: 'NA',
                      schemeID: 'TTX',
                    },
                  ],
                },
              ],
              PostalAddress: [
                {
                  CityName: [
                    {
                      _: 'Kuala Lumpur',
                    },
                  ],
                  PostalZone: [
                    {
                      _: '55100',
                    },
                  ],
                  CountrySubentityCode: [
                    {
                      _: '14',
                    },
                  ],
                  AddressLine: [
                    {
                      Line: [
                        {
                          _: '123 Jalan Bukit Bintang',
                        },
                      ],
                    },
                    {
                      Line: [
                        {
                          _: 'Level 5, Menara ABC',
                        },
                      ],
                    },
                    {
                      Line: [
                        {
                          _: 'Bukit Bintang',
                        },
                      ],
                    },
                  ],
                  Country: [
                    {
                      IdentificationCode: [
                        {
                          _: 'MYS',
                          listID: 'ISO3166-1',
                          listAgencyID: '6',
                        },
                      ],
                    },
                  ],
                },
              ],
              PartyLegalEntity: [
                {
                  RegistrationName: [
                    {
                      _: 'Heng CH',
                    },
                  ],
                },
              ],
              Contact: [
                {
                  Telephone: [
                    {
                      _: '**********',
                    },
                  ],
                  ElectronicMail: [
                    {
                      _: '<EMAIL>',
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
      AccountingCustomerParty: [
        {
          Party: [
            {
              PostalAddress: [
                {
                  CityName: [
                    {
                      _: 'Kuala Lumpur',
                    },
                  ],
                  PostalZone: [
                    {
                      _: '50450',
                    },
                  ],
                  CountrySubentityCode: [
                    {
                      _: '14',
                    },
                  ],
                  AddressLine: [
                    {
                      Line: [
                        {
                          _: '456 Jalan Ampang',
                        },
                      ],
                    },
                    {
                      Line: [
                        {
                          _: 'Tower 2, XYZ Plaza',
                        },
                      ],
                    },
                    {
                      Line: [
                        {
                          _: 'Ampang',
                        },
                      ],
                    },
                  ],
                  Country: [
                    {
                      IdentificationCode: [
                        {
                          _: 'MYS',
                          listID: 'ISO3166-1',
                          listAgencyID: '6',
                        },
                      ],
                    },
                  ],
                },
              ],
              PartyLegalEntity: [
                {
                  RegistrationName: [
                    {
                      _: 'Gaincue Sdn Bhd',
                    },
                  ],
                },
              ],
              PartyIdentification: [
                {
                  ID: [
                    {
                      _: 'IG40023601100',
                      schemeID: 'TIN',
                    },
                  ],
                },
                {
                  ID: [
                    {
                      _: '930723075219',
                      schemeID: 'NRIC',
                    },
                  ],
                },
                {
                  ID: [
                    {
                      _: 'W10-7654321',
                      schemeID: 'SST',
                    },
                  ],
                },
                {
                  ID: [
                    {
                      _: 'NA',
                      schemeID: 'TTX',
                    },
                  ],
                },
              ],
              Contact: [
                {
                  Telephone: [
                    {
                      _: '**********',
                    },
                  ],
                  ElectronicMail: [
                    {
                      _: '<EMAIL>',
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
      PaymentMeans: [
        {
          PaymentMeansCode: [
            {
              _: '01',
            },
          ],
          PayeeFinancialAccount: [
            {
              ID: [
                {
                  _: '**********',
                },
              ],
            },
          ],
        },
      ],
      PaymentTerms: [
        {
          Note: [
            {
              _: '30 days from invoice date',
            },
          ],
        },
      ],
      PrepaidPayment: [
        {
          ID: [
            {
              _: 'PRE-2025-001',
            },
          ],
          PaidAmount: [
            {
              _: 500,
              currencyID: 'MYR',
            },
          ],
          PaidDate: [
            {
              _: '2025-03-15',
            },
          ],
          PaidTime: [
            {
              _: '14:30:00Z',
            },
          ],
        },
      ],
      AllowanceCharge: [
        {
          ChargeIndicator: [
            {
              _: false,
            },
          ],
          AllowanceChargeReason: [
            {
              _: 'Loyalty Discount',
            },
          ],
          Amount: [
            {
              _: 20,
              currencyID: 'MYR',
            },
          ],
        },
      ],
      TaxTotal: [
        {
          TaxAmount: [
            {
              _: 60,
              currencyID: 'MYR',
            },
          ],
          TaxSubtotal: [
            {
              TaxableAmount: [
                {
                  _: 0,
                  currencyID: 'MYR',
                },
              ],
              TaxAmount: [
                {
                  _: 0,
                  currencyID: 'MYR',
                },
              ],
              TaxCategory: [
                {
                  ID: [
                    {
                      _: 'E',
                    },
                  ],
                  TaxExemptionReason: [
                    {
                      _: 'Partial exemption for export items',
                    },
                  ],
                  TaxScheme: [
                    {
                      ID: [
                        {
                          _: 'OTH',
                          schemeID: 'UN/ECE 5153',
                          schemeAgencyID: '6',
                        },
                      ],
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
      LegalMonetaryTotal: [
        {
          LineExtensionAmount: [
            {
              _: 1000,
              currencyID: 'MYR',
            },
          ],
          TaxExclusiveAmount: [
            {
              _: 1000,
              currencyID: 'MYR',
            },
          ],
          TaxInclusiveAmount: [
            {
              _: 1060,
              currencyID: 'MYR',
            },
          ],
          AllowanceTotalAmount: [
            {
              _: 50,
              currencyID: 'MYR',
            },
          ],
          ChargeTotalAmount: [
            {
              _: 0,
              currencyID: 'MYR',
            },
          ],
          PayableRoundingAmount: [
            {
              _: 0,
              currencyID: 'MYR',
            },
          ],
          PayableAmount: [
            {
              _: 1060,
              currencyID: 'MYR',
            },
          ],
        },
      ],
      InvoiceLine: [
        {
          ID: [
            {
              _: '1',
            },
          ],
          InvoicedQuantity: [
            {
              _: 1,
              unitCode: 'C62',
            },
          ],
          LineExtensionAmount: [
            {
              _: 500,
              currencyID: 'MYR',
            },
          ],
          TaxTotal: [
            {
              TaxAmount: [
                {
                  _: 30,
                  currencyID: 'MYR',
                },
              ],
              TaxSubtotal: [
                {
                  TaxableAmount: [
                    {
                      _: 500,
                      currencyID: 'MYR',
                    },
                  ],
                  TaxAmount: [
                    {
                      _: 30,
                      currencyID: 'MYR',
                    },
                  ],
                  Percent: [
                    {
                      _: 6,
                    },
                  ],
                  TaxCategory: [
                    {
                      ID: [
                        {
                          _: '01',
                        },
                      ],
                      TaxScheme: [
                        {
                          ID: [
                            {
                              _: 'OTH',
                              schemeID: 'UN/ECE 5153',
                              schemeAgencyID: '6',
                            },
                          ],
                        },
                      ],
                    },
                  ],
                },
              ],
            },
          ],
          Item: [
            {
              CommodityClassification: [
                {
                  ItemClassificationCode: [
                    {
                      _: '',
                      listID: 'PTC',
                    },
                  ],
                },
                {
                  ItemClassificationCode: [
                    {
                      _: '003',
                      listID: 'CLASS',
                    },
                  ],
                },
              ],
              Description: [
                {
                  _: 'Office Desk - Model A',
                },
              ],
            },
          ],
          Price: [
            {
              PriceAmount: [
                {
                  _: 500,
                  currencyID: 'MYR',
                },
              ],
            },
          ],
          ItemPriceExtension: [
            {
              Amount: [
                {
                  _: 500,
                  currencyID: 'MYR',
                },
              ],
            },
          ],
        },
        {
          ID: [
            {
              _: '2',
            },
          ],
          InvoicedQuantity: [
            {
              _: 1,
              unitCode: 'C62',
            },
          ],
          LineExtensionAmount: [
            {
              _: 300,
              currencyID: 'MYR',
            },
          ],
          TaxTotal: [
            {
              TaxAmount: [
                {
                  _: 18,
                  currencyID: 'MYR',
                },
              ],
              TaxSubtotal: [
                {
                  TaxableAmount: [
                    {
                      _: 300,
                      currencyID: 'MYR',
                    },
                  ],
                  TaxAmount: [
                    {
                      _: 18,
                      currencyID: 'MYR',
                    },
                  ],
                  Percent: [
                    {
                      _: 6,
                    },
                  ],
                  TaxCategory: [
                    {
                      ID: [
                        {
                          _: '01',
                        },
                      ],
                      TaxScheme: [
                        {
                          ID: [
                            {
                              _: 'OTH',
                              schemeID: 'UN/ECE 5153',
                              schemeAgencyID: '6',
                            },
                          ],
                        },
                      ],
                    },
                  ],
                },
              ],
            },
          ],
          Item: [
            {
              CommodityClassification: [
                {
                  ItemClassificationCode: [
                    {
                      _: '',
                      listID: 'PTC',
                    },
                  ],
                },
                {
                  ItemClassificationCode: [
                    {
                      _: '003',
                      listID: 'CLASS',
                    },
                  ],
                },
              ],
              Description: [
                {
                  _: 'Office Chair - Executive',
                },
              ],
            },
          ],
          Price: [
            {
              PriceAmount: [
                {
                  _: 300,
                  currencyID: 'MYR',
                },
              ],
            },
          ],
          ItemPriceExtension: [
            {
              Amount: [
                {
                  _: 300,
                  currencyID: 'MYR',
                },
              ],
            },
          ],
        },
        {
          ID: [
            {
              _: '3',
            },
          ],
          InvoicedQuantity: [
            {
              _: 1,
              unitCode: 'C62',
            },
          ],
          LineExtensionAmount: [
            {
              _: 200,
              currencyID: 'MYR',
            },
          ],
          TaxTotal: [
            {
              TaxAmount: [
                {
                  _: 12,
                  currencyID: 'MYR',
                },
              ],
              TaxSubtotal: [
                {
                  TaxableAmount: [
                    {
                      _: 200,
                      currencyID: 'MYR',
                    },
                  ],
                  TaxAmount: [
                    {
                      _: 12,
                      currencyID: 'MYR',
                    },
                  ],
                  Percent: [
                    {
                      _: 6,
                    },
                  ],
                  TaxCategory: [
                    {
                      ID: [
                        {
                          _: '01',
                        },
                      ],
                      TaxScheme: [
                        {
                          ID: [
                            {
                              _: 'OTH',
                              schemeID: 'UN/ECE 5153',
                              schemeAgencyID: '6',
                            },
                          ],
                        },
                      ],
                    },
                  ],
                },
              ],
            },
          ],
          Item: [
            {
              CommodityClassification: [
                {
                  ItemClassificationCode: [
                    {
                      _: '',
                      listID: 'PTC',
                    },
                  ],
                },
                {
                  ItemClassificationCode: [
                    {
                      _: '003',
                      listID: 'CLASS',
                    },
                  ],
                },
              ],
              Description: [
                {
                  _: 'Filing Cabinet',
                },
              ],
            },
          ],
          Price: [
            {
              PriceAmount: [
                {
                  _: 200,
                  currencyID: 'MYR',
                },
              ],
            },
          ],
          ItemPriceExtension: [
            {
              Amount: [
                {
                  _: 200,
                  currencyID: 'MYR',
                },
              ],
            },
          ],
        },
      ],
    },
  ],
}
