export const data = [
  {
    id: 'gid://shopify/Order/5640826650678',
    billingAddress: {
      address1: 'Jalan Bukit Bintang',
      address2: null,
      city: 'Kuala Lumpur',
      coordinatesValidated: false,
      company: null,
      country: 'Malaysia',
      countryCodeV2: 'MY',
      firstName: '<PERSON>',
      id: 'gid://shopify/MailingAddress/11126157836453?model_name=Address',
      lastName: 'Khoo',
      latitude: null,
      longitude: null,
      name: '<PERSON>',
      phone: null,
      province: 'Wilayah Persekutuan',
      provinceCode: 'MY-14',
      timeZone: 'Asia/Kuala_Lumpur',
      validationResultSummary: null,
      zip: '55100',
    },
    totalWeight: '0',
    totalTaxSet: {
      shopMoney: {
        amount: '0.0',
        currencyCode: 'MYR',
      },
    },
    totalShippingPriceSet: {
      shopMoney: {
        amount: '10.0',
        currencyCode: 'MYR',
      },
    },
    totalRefundedShippingSet: {
      shopMoney: {
        amount: '0.0',
        currencyCode: 'MYR',
      },
    },
    totalRefundedSet: {
      shopMoney: {
        amount: '0.0',
        currencyCode: 'MYR',
      },
    },
    totalReceivedSet: {
      shopMoney: {
        amount: '0.0',
        currencyCode: 'MYR',
      },
    },
    totalPriceSet: {
      shopMoney: {
        amount: '0.0',
        currencyCode: 'MYR',
      },
    },
    totalDiscountsSet: {
      shopMoney: {
        amount: '42.49',
        currencyCode: 'MYR',
      },
    },
    totalOutstandingSet: {
      shopMoney: {
        amount: '0.0',
        currencyCode: 'MYR',
      },
    },
    totalCashRoundingAdjustment: {
      paymentSet: {
        shopMoney: {
          amount: '0.0',
          currencyCode: 'MYR',
        },
      },
      refundSet: {
        shopMoney: {
          amount: '0.0',
          currencyCode: 'MYR',
        },
      },
    },
    taxesIncluded: true,
    taxLines: [],
    taxExempt: false,
    tags: [],
    subtotalPriceSet: {
      shopMoney: {
        amount: '0.0',
        currencyCode: 'MYR',
      },
    },
    subtotalLineItemsQuantity: 1,
    shippingLine: {
      carrierIdentifier: 'da22abc49b60bbaacbf44882dfe71263',
      code: 'Standard Shipping',
      currentDiscountedPriceSet: {
        shopMoney: {
          currencyCode: 'MYR',
        },
      },
      title: 'Standard Shipping',
      taxLines: [],
      originalPriceSet: {
        shopMoney: {
          amount: '10.0',
          currencyCode: 'MYR',
        },
      },
      discountedPriceSet: {
        shopMoney: {
          amount: '0.0',
          currencyCode: 'MYR',
        },
      },
      discountAllocations: [
        {
          allocatedAmountSet: {
            shopMoney: {
              amount: '10.0',
              currencyCode: 'MYR',
            },
          },
        },
      ],
      deliveryCategory: null,
    },
    shippingAddress: {
      address1: 'Jalan Bukit Bintang',
      address2: null,
      city: 'Kuala Lumpur',
      company: null,
      coordinatesValidated: true,
      country: 'Malaysia',
      countryCodeV2: 'MY',
      firstName: 'Ricky',
      lastName: 'Khoo',
      latitude: 3.1478,
      longitude: 101.7068,
      name: 'Ricky Khoo',
      phone: null,
      province: 'Wilayah Persekutuan',
      provinceCode: 'MY-14',
      timeZone: 'Asia/Kuala_Lumpur',
      validationResultSummary: null,
      zip: '55100',
    },
    requiresShipping: true,
    processedAt: '2023-04-06T15:21:23Z',
    presentmentCurrencyCode: 'MYR',
    phone: null,
    originalTotalPriceSet: {
      shopMoney: {
        currencyCode: 'MYR',
        amount: '0.0',
      },
    },
    originalTotalDutiesSet: null,
    note: null,
    name: '#1001',
    fulfillments: [],
    cartDiscountAmountSet: {
      shopMoney: {
        amount: '42.49',
        currencyCode: 'MYR',
      },
    },
    closed: false,
    closedAt: null,
    confirmationNumber: 'F8Z05LR4Q',
    confirmed: true,
    createdAt: '2023-04-06T15:21:25Z',
    currencyCode: 'MYR',
    customer: {
      verifiedEmail: true,
      updatedAt: '2025-03-26T08:11:20Z',
      addressesV2: {
        nodes: [
          {
            address1: 'Jalan Bukit Bintang',
            address2: null,
            city: 'Kuala Lumpur',
            company: null,
            coordinatesValidated: false,
            country: 'Malaysia',
            countryCodeV2: 'MY',
            firstName: 'Ricky',
            id: 'gid://shopify/MailingAddress/7888853336229?model_name=CustomerAddress',
            lastName: 'Khoo',
            latitude: null,
            longitude: null,
            name: 'Ricky Khoo',
            phone: null,
            province: 'Wilayah Persekutuan',
            provinceCode: 'MY-14',
            timeZone: 'Asia/Kuala_Lumpur',
            zip: '55100',
            validationResultSummary: null,
          },
        ],
      },
      displayName: 'Ricky Khoo',
      firstName: 'Ricky',
      id: 'gid://shopify/Customer/6594675441829',
      lastName: 'Khoo',
    },
    // buyerTin: 'IG40023601100', // TODO: This value does not exist from Shopify GraphQL, should be provided by user through form submission
    // buyerRegistrationNumber: '930723075219', //TODO: This value does not exist from Shopify GraphQL, should be provided by user through form submission
    // buyerRegistrationType: 'NRIC', // TODO: This value does not exist from Shopify GraphQL, should be provided by user through form submission
    discountApplications: {
      nodes: [
        {
          allocationMethod: 'ACROSS',
          index: 0,
          targetSelection: 'ENTITLED',
          targetType: 'LINE_ITEM',
        },
        {
          allocationMethod: 'EACH',
          index: 1,
          targetSelection: 'ALL',
          targetType: 'SHIPPING_LINE',
        },
      ],
    },
    discountCodes: ['ENWATFBEWNJT', 'V75WV6RNZD5Z'],
    email: '<EMAIL>',
    displayFulfillmentStatus: 'UNFULFILLED',
    displayFinancialStatus: 'PAID',
    lineItems: {
      nodes: [
        {
          discountedTotalSet: {
            shopMoney: {
              amount: '0.0',
              currencyCode: 'MYR',
            },
          },
          discountAllocations: [
            {
              allocatedAmountSet: {
                shopMoney: {
                  amount: '32.49',
                  currencyCode: 'MYR',
                },
              },
            },
          ],
          discountedUnitPriceSet: {
            shopMoney: {
              amount: '32.49',
              currencyCode: 'MYR',
            },
          },
          id: 'gid://shopify/LineItem/12518976880805',
          name: 'THE FROST AIR - iPhone 14 / Smoke Black',
          originalTotalSet: {
            shopMoney: {
              amount: '32.49',
              currencyCode: 'MYR',
            },
          },
          originalUnitPriceSet: {
            shopMoney: {
              currencyCode: 'MYR',
              amount: '32.49',
            },
          },
          quantity: 1,
          taxable: true,
          title: 'THE FROST AIR',
          totalDiscountSet: {
            shopMoney: {
              amount: '0.0',
              currencyCode: 'MYR',
            },
          },
          variant: {
            title: 'iPhone 14 / Smoke Black',
            sku: 'FA1461B',
            price: '32.49',
            id: 'gid://shopify/ProductVariant/42302727553189',
          },
          variantTitle: 'iPhone 14 / Smoke Black',
          vendor: 'CASEFINITE',
          product: {
            title: 'THE FROST AIR',
            category: null,
            descriptionHtml: '<span data-mce-fragment="1">Ultra thin smartphone case</span>',
            featuredMedia: {
              id: 'gid://shopify/MediaImage/**************',
              originalSource: {
                url: 'https://shopify-shop-assets.storage.googleapis.com/s/files/1/d/a523/0615/4328/4901/products/FA61_B_Graphite.jpg?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=merchant-assets%40shopify-tiers.iam.gserviceaccount.com%2F20250327%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250327T050951Z&X-Goog-Expires=300&X-Goog-SignedHeaders=host&X-Goog-Signature=0a58b49af1707676792fb66b93dc91f6fe3ba305acf1d518dc9c2b7823fb26a6fbe9088f50ce2f961f5afed65e5d627d3701fb499deecb04a1afc589629e7bcd1dae57623469a592a1807ac298b13fe8c091e6ea36b043ff46734e623f17d96d46bcadbc00b734bdb5d05f84af28889984aee34c86da7c2175f253ddb8219afc61239e0fb9efedb28a16f921a484975950904076a2a52ec587acfd909209feada22a3fd4e202fec3116ee51f18c87dbdc1f4c6ae09e8e07784111dd13403b9c043f230aba715e752b414ccf81ee655cab28eccf7067c58adcb0a977bf15cdc63f40494ab152373c2908294228ad84a50262cbd85a0bf5694599f732de9ce2376',
              },
            },
            handle: 'frostair',
            id: 'gid://shopify/Product/*************',
            onlineStoreUrl: null,
            options: [
              {
                name: 'Model',
                values: [
                  'iPhone 14',
                  'iPhone 14 Plus',
                  'iPhone 14 Pro',
                  'iPhone 14 Pro Max',
                  'iPhone 13 mini',
                  'iPhone 13',
                  'iPhone 13 Pro',
                  'iPhone 13 Pro Max',
                  'iPhone 12 mini',
                  'iPhone 12/12 Pro',
                  'iPhone 12 Pro Max',
                  'iPhone 11',
                  'iPhone 11 Pro',
                  'iPhone 11 Pro Max',
                  'iPhone X',
                  'iPhone XS',
                  'iPhone XR',
                  'iPhone 7/8/SE',
                ],
              },
              {
                name: 'Color',
                values: ['Smoke Black', 'Ice White', 'Metallic Blue', 'Classic Green'],
              },
            ],
            tags: ['Android', 'iOS'],
          },
        },
      ],
    },
  },
  {
    id: 'gid://shopify/Order/5655146201142',
    billingAddress: {
      address1: 'Jalan Bukit Bintang',
      address2: null,
      city: 'Kuala Lumpur',
      coordinatesValidated: false,
      company: null,
      country: 'Malaysia',
      countryCodeV2: 'MY',
      firstName: 'Ricky',
      id: 'gid://shopify/MailingAddress/11126162555045?model_name=Address',
      lastName: 'Khoo',
      latitude: null,
      longitude: null,
      name: 'Ricky Khoo',
      phone: null,
      province: 'Wilayah Persekutuan',
      provinceCode: 'MY-14',
      timeZone: 'Asia/Kuala_Lumpur',
      validationResultSummary: null,
      zip: '55100',
    },
    totalWeight: '0',
    totalTaxSet: {
      shopMoney: {
        amount: '0.0',
        currencyCode: 'MYR',
      },
    },
    totalShippingPriceSet: {
      shopMoney: {
        amount: '10.0',
        currencyCode: 'MYR',
      },
    },
    totalRefundedShippingSet: {
      shopMoney: {
        amount: '0.0',
        currencyCode: 'MYR',
      },
    },
    totalRefundedSet: {
      shopMoney: {
        amount: '0.0',
        currencyCode: 'MYR',
      },
    },
    totalReceivedSet: {
      shopMoney: {
        amount: '0.0',
        currencyCode: 'MYR',
      },
    },
    totalPriceSet: {
      shopMoney: {
        amount: '0.0',
        currencyCode: 'MYR',
      },
    },
    totalDiscountsSet: {
      shopMoney: {
        amount: '42.49',
        currencyCode: 'MYR',
      },
    },
    totalOutstandingSet: {
      shopMoney: {
        amount: '0.0',
        currencyCode: 'MYR',
      },
    },
    totalCashRoundingAdjustment: {
      paymentSet: {
        shopMoney: {
          amount: '0.0',
          currencyCode: 'MYR',
        },
      },
      refundSet: {
        shopMoney: {
          amount: '0.0',
          currencyCode: 'MYR',
        },
      },
    },
    taxesIncluded: true,
    taxLines: [],
    taxExempt: false,
    tags: [],
    subtotalPriceSet: {
      shopMoney: {
        amount: '0.0',
        currencyCode: 'MYR',
      },
    },
    subtotalLineItemsQuantity: 1,
    shippingLine: {
      carrierIdentifier: '650f1a14fa979ec5c74d063e968411d4',
      code: 'Standard Shipping',
      currentDiscountedPriceSet: {
        shopMoney: {
          currencyCode: 'MYR',
        },
      },
      title: 'Standard Shipping',
      taxLines: [],
      originalPriceSet: {
        shopMoney: {
          amount: '10.0',
          currencyCode: 'MYR',
        },
      },
      discountedPriceSet: {
        shopMoney: {
          amount: '0.0',
          currencyCode: 'MYR',
        },
      },
      discountAllocations: [
        {
          allocatedAmountSet: {
            shopMoney: {
              amount: '10.0',
              currencyCode: 'MYR',
            },
          },
        },
      ],
      deliveryCategory: null,
    },
    shippingAddress: {
      address1: 'Jalan Bukit Bintang',
      address2: null,
      city: 'Kuala Lumpur',
      company: null,
      coordinatesValidated: true,
      country: 'Malaysia',
      countryCodeV2: 'MY',
      firstName: 'Ricky',
      lastName: 'Khoo',
      latitude: 3.1478,
      longitude: 101.7068,
      name: 'Ricky Khoo',
      phone: null,
      province: 'Wilayah Persekutuan',
      provinceCode: 'MY-14',
      timeZone: 'Asia/Kuala_Lumpur',
      validationResultSummary: null,
      zip: '55100',
    },
    requiresShipping: true,
    processedAt: '2023-04-06T15:24:43Z',
    presentmentCurrencyCode: 'MYR',
    phone: null,
    originalTotalPriceSet: {
      shopMoney: {
        currencyCode: 'MYR',
        amount: '0.0',
      },
    },
    originalTotalDutiesSet: null,
    note: null,
    name: '#1002',
    fulfillments: [],
    cartDiscountAmountSet: {
      shopMoney: {
        amount: '42.49',
        currencyCode: 'MYR',
      },
    },
    closed: false,
    closedAt: null,
    confirmationNumber: 'LW1KFHHFP',
    confirmed: true,
    createdAt: '2023-04-06T15:24:44Z',
    currencyCode: 'MYR',
    customer: {
      verifiedEmail: true,
      updatedAt: '2025-03-26T08:11:20Z',
      addressesV2: {
        nodes: [
          {
            address1: 'Jalan Bukit Bintang',
            address2: null,
            city: 'Kuala Lumpur',
            company: null,
            coordinatesValidated: false,
            country: 'Malaysia',
            countryCodeV2: 'MY',
            firstName: 'Ricky',
            id: 'gid://shopify/MailingAddress/7888853336229?model_name=CustomerAddress',
            lastName: 'Khoo',
            latitude: null,
            longitude: null,
            name: 'Ricky Khoo',
            phone: null,
            province: 'Wilayah Persekutuan',
            provinceCode: 'MY-14',
            timeZone: 'Asia/Kuala_Lumpur',
            zip: '55100',
            validationResultSummary: null,
          },
        ],
      },
      displayName: 'Ricky Khoo',
      firstName: 'Ricky',
      id: 'gid://shopify/Customer/6594675441829',
      lastName: 'Khoo',
    },
    // buyerTin: 'IG40023601100', // TODO: This value does not exist from Shopify GraphQL, should be provided by user through form submission
    // buyerRegistrationNumber: '930723075219', //TODO: This value does not exist from Shopify GraphQL, should be provided by user through form submission
    // buyerRegistrationType: 'NRIC', // TODO: This value does not exist from Shopify GraphQL, should be provided by user through form submission
    discountApplications: {
      nodes: [
        {
          allocationMethod: 'ACROSS',
          index: 0,
          targetSelection: 'ENTITLED',
          targetType: 'LINE_ITEM',
        },
        {
          allocationMethod: 'EACH',
          index: 1,
          targetSelection: 'ALL',
          targetType: 'SHIPPING_LINE',
        },
      ],
    },
    discountCodes: ['ENWATFBEWNJT', 'V75WV6RNZD5Z'],
    email: '<EMAIL>',
    displayFulfillmentStatus: 'UNFULFILLED',
    displayFinancialStatus: 'PAID',
    lineItems: {
      nodes: [
        {
          discountedTotalSet: {
            shopMoney: {
              amount: '0.0',
              currencyCode: 'MYR',
            },
          },
          discountAllocations: [
            {
              allocatedAmountSet: {
                shopMoney: {
                  amount: '32.49',
                  currencyCode: 'MYR',
                },
              },
            },
          ],
          discountedUnitPriceSet: {
            shopMoney: {
              amount: '32.49',
              currencyCode: 'MYR',
            },
          },
          id: 'gid://shopify/LineItem/12518982025381',
          name: 'THE FROST AIR - iPhone 12 Pro Max / Smoke Black',
          originalTotalSet: {
            shopMoney: {
              amount: '32.49',
              currencyCode: 'MYR',
            },
          },
          originalUnitPriceSet: {
            shopMoney: {
              currencyCode: 'MYR',
              amount: '32.49',
            },
          },
          quantity: 1,
          taxable: true,
          title: 'THE FROST AIR',
          totalDiscountSet: {
            shopMoney: {
              amount: '0.0',
              currencyCode: 'MYR',
            },
          },
          variant: {
            title: 'iPhone 12 Pro Max / Smoke Black',
            sku: 'FA1267B',
            price: '32.49',
            id: 'gid://shopify/ProductVariant/42302728274085',
          },
          variantTitle: 'iPhone 12 Pro Max / Smoke Black',
          vendor: 'CASEFINITE',
          product: {
            title: 'THE FROST AIR',
            category: null,
            descriptionHtml: '<span data-mce-fragment="1">Ultra thin smartphone case</span>',
            featuredMedia: {
              id: 'gid://shopify/MediaImage/**************',
              originalSource: {
                url: 'https://shopify-shop-assets.storage.googleapis.com/s/files/1/d/a523/0615/4328/4901/products/FA61_B_Graphite.jpg?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=merchant-assets%40shopify-tiers.iam.gserviceaccount.com%2F20250327%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250327T050951Z&X-Goog-Expires=300&X-Goog-SignedHeaders=host&X-Goog-Signature=0a58b49af1707676792fb66b93dc91f6fe3ba305acf1d518dc9c2b7823fb26a6fbe9088f50ce2f961f5afed65e5d627d3701fb499deecb04a1afc589629e7bcd1dae57623469a592a1807ac298b13fe8c091e6ea36b043ff46734e623f17d96d46bcadbc00b734bdb5d05f84af28889984aee34c86da7c2175f253ddb8219afc61239e0fb9efedb28a16f921a484975950904076a2a52ec587acfd909209feada22a3fd4e202fec3116ee51f18c87dbdc1f4c6ae09e8e07784111dd13403b9c043f230aba715e752b414ccf81ee655cab28eccf7067c58adcb0a977bf15cdc63f40494ab152373c2908294228ad84a50262cbd85a0bf5694599f732de9ce2376',
              },
            },
            handle: 'frostair',
            id: 'gid://shopify/Product/*************',
            onlineStoreUrl: null,
            options: [
              {
                name: 'Model',
                values: [
                  'iPhone 14',
                  'iPhone 14 Plus',
                  'iPhone 14 Pro',
                  'iPhone 14 Pro Max',
                  'iPhone 13 mini',
                  'iPhone 13',
                  'iPhone 13 Pro',
                  'iPhone 13 Pro Max',
                  'iPhone 12 mini',
                  'iPhone 12/12 Pro',
                  'iPhone 12 Pro Max',
                  'iPhone 11',
                  'iPhone 11 Pro',
                  'iPhone 11 Pro Max',
                  'iPhone X',
                  'iPhone XS',
                  'iPhone XR',
                  'iPhone 7/8/SE',
                ],
              },
              {
                name: 'Color',
                values: ['Smoke Black', 'Ice White', 'Metallic Blue', 'Classic Green'],
              },
            ],
            tags: ['Android', 'iOS'],
          },
        },
      ],
    },
  },
  {
    id: 'gid://shopify/Order/5655163174966',
    billingAddress: {
      address1: 'Jalan Bukit Bintang',
      address2: null,
      city: 'Kuala Lumpur',
      coordinatesValidated: false,
      company: null,
      country: 'Malaysia',
      countryCodeV2: 'MY',
      firstName: 'Ricky',
      id: 'gid://shopify/MailingAddress/11219750584485?model_name=Address',
      lastName: 'Khoo',
      latitude: null,
      longitude: null,
      name: 'Ricky Khoo',
      phone: null,
      province: 'Wilayah Persekutuan',
      provinceCode: 'MY-14',
      timeZone: 'Asia/Kuala_Lumpur',
      validationResultSummary: null,
      zip: '55100',
    },
    totalWeight: '0',
    totalTaxSet: {
      shopMoney: {
        amount: '26.56',
        currencyCode: 'MYR',
      },
    },
    totalShippingPriceSet: {
      shopMoney: {
        amount: '0.0',
        currencyCode: 'MYR',
      },
    },
    totalRefundedShippingSet: {
      shopMoney: {
        amount: '0.0',
        currencyCode: 'MYR',
      },
    },
    totalRefundedSet: {
      shopMoney: {
        amount: '0.0',
        currencyCode: 'MYR',
      },
    },
    totalReceivedSet: {
      shopMoney: {
        amount: '0.0',
        currencyCode: 'MYR',
      },
    },
    totalPriceSet: {
      shopMoney: {
        amount: '291.92',
        currencyCode: 'MYR',
      },
    },
    totalDiscountsSet: {
      shopMoney: {
        amount: '0.0',
        currencyCode: 'MYR',
      },
    },
    totalOutstandingSet: {
      shopMoney: {
        amount: '0.0',
        currencyCode: 'MYR',
      },
    },
    totalCashRoundingAdjustment: {
      paymentSet: {
        shopMoney: {
          amount: '0.0',
          currencyCode: 'MYR',
        },
      },
      refundSet: {
        shopMoney: {
          amount: '0.0',
          currencyCode: 'MYR',
        },
      },
    },
    taxesIncluded: true,
    taxLines: [
      {
        channelLiable: null,
        priceSet: {
          shopMoney: {
            amount: '3.32',
            currencyCode: 'MYR',
          },
        },
        rate: 0.1,
        ratePercentage: 10,
        source: null,
        title: 'SST',
      },
      {
        channelLiable: null,
        priceSet: {
          shopMoney: {
            amount: '23.24',
            currencyCode: 'MYR',
          },
        },
        rate: 0.1,
        ratePercentage: 10,
        source: null,
        title: 'SST',
      },
    ],
    taxExempt: false,
    tags: [],
    subtotalPriceSet: {
      shopMoney: {
        amount: '291.92',
        currencyCode: 'MYR',
      },
    },
    subtotalLineItemsQuantity: 8,
    shippingLine: null,
    shippingAddress: {
      address1: 'Jalan Bukit Bintang',
      address2: null,
      city: 'Kuala Lumpur',
      company: null,
      coordinatesValidated: true,
      country: 'Malaysia',
      countryCodeV2: 'MY',
      firstName: 'Ricky',
      lastName: 'Khoo',
      latitude: 3.1478,
      longitude: 101.7068,
      name: 'Ricky Khoo',
      phone: null,
      province: 'Wilayah Persekutuan',
      provinceCode: 'MY-14',
      timeZone: 'Asia/Kuala_Lumpur',
      validationResultSummary: null,
      zip: '55100',
    },
    requiresShipping: true,
    processedAt: '2023-05-26T08:48:23Z',
    presentmentCurrencyCode: 'MYR',
    phone: null,
    originalTotalPriceSet: {
      shopMoney: {
        currencyCode: 'MYR',
        amount: '36.49',
      },
    },
    originalTotalDutiesSet: null,
    note: null,
    name: '#1003',
    fulfillments: [],
    cartDiscountAmountSet: null,
    closed: true,
    closedAt: '2023-05-26T17:03:40Z',
    confirmationNumber: '7YZU0IAXE',
    confirmed: true,
    createdAt: '2023-05-26T08:48:23Z',
    currencyCode: 'MYR',
    customer: {
      verifiedEmail: true,
      updatedAt: '2025-03-26T08:11:20Z',
      addressesV2: {
        nodes: [
          {
            address1: 'Jalan Bukit Bintang',
            address2: null,
            city: 'Kuala Lumpur',
            company: null,
            coordinatesValidated: false,
            country: 'Malaysia',
            countryCodeV2: 'MY',
            firstName: 'Ricky',
            id: 'gid://shopify/MailingAddress/7888853336229?model_name=CustomerAddress',
            lastName: 'Khoo',
            latitude: null,
            longitude: null,
            name: 'Ricky Khoo',
            phone: null,
            province: 'Wilayah Persekutuan',
            provinceCode: 'MY-14',
            timeZone: 'Asia/Kuala_Lumpur',
            zip: '55100',
            validationResultSummary: null,
          },
        ],
      },
      displayName: 'Ricky Khoo',
      firstName: 'Ricky',
      id: 'gid://shopify/Customer/6594675441829',
      lastName: 'Khoo',
    },
    // buyerTin: 'IG40023601100', // TODO: This value does not exist from Shopify GraphQL, should be provided by user through form submission
    // buyerRegistrationNumber: '930723075219', //TODO: This value does not exist from Shopify GraphQL, should be provided by user through form submission
    // buyerRegistrationType: 'NRIC', // TODO: This value does not exist from Shopify GraphQL, should be provided by user through form submission
    discountApplications: {
      nodes: [],
    },
    discountCodes: [],
    email: '<EMAIL>',
    displayFulfillmentStatus: 'UNFULFILLED',
    displayFinancialStatus: 'VOIDED',
    lineItems: {
      nodes: [
        {
          discountedTotalSet: {
            shopMoney: {
              amount: '291.92',
              currencyCode: 'MYR',
            },
          },
          discountAllocations: [],
          discountedUnitPriceSet: {
            shopMoney: {
              amount: '36.49',
              currencyCode: 'MYR',
            },
          },
          id: 'gid://shopify/LineItem/12619029905573',
          name: 'THE CASEFINITE GLASS - iPhone 14 Pro / Clear',
          originalTotalSet: {
            shopMoney: {
              amount: '291.92',
              currencyCode: 'MYR',
            },
          },
          originalUnitPriceSet: {
            shopMoney: {
              currencyCode: 'MYR',
              amount: '36.49',
            },
          },
          quantity: 8,
          taxable: true,
          title: 'THE CASEFINITE GLASS',
          totalDiscountSet: {
            shopMoney: {
              amount: '0.0',
              currencyCode: 'MYR',
            },
          },
          variant: {
            title: 'iPhone 14 Pro / Clear',
            sku: 'SP1461C',
            price: '36.49',
            id: 'gid://shopify/ProductVariant/42302729420965',
          },
          variantTitle: 'iPhone 14 Pro / Clear',
          vendor: 'CASEFINITE',
          product: {
            title: 'THE CASEFINITE GLASS',
            category: null,
            descriptionHtml:
              '<meta charset="utf-8">\n<p data-mce-fragment="1">Industry-leading 9H hardness, hard coating technology allows the film to absorb external impacts and protect the screen from damage in daily life.</p>\n<p data-mce-fragment="1">In addition, precise round edge processing makes the glass film feel less noticeable when applied.</p>\n<p data-mce-fragment="1">Also, a dedicated application guide frame is included, so the smartphone body and glass film can be applied easily without misalignment, even for first-time users.</p>\n<p data-mce-fragment="1">Using matte technology, we have achieved even better fingerprint resistance. Furthermore, it feels good to touch and can be operated comfortably.</p>\n<h6 data-mce-fragment="1">※ Accessories: 2 tempered glass films, 1 application frame, wet sheet, dust removal sheet, wiping cloth (1 each)</h6>\n<h6 data-mce-fragment="1">※ Depending on the condition of the smartphone body (scratches, etc.), bubbles may occur.</h6>\n<h6 data-mce-fragment="1">※ The glass film for iPhone 7/8/SE 2020 does not come with a dedicated application frame, so you need to apply it yourself.</h6>\n<h6 data-mce-fragment="1">\n<meta charset="utf-8">\n<span data-mce-fragment="1">※ The attached photo is of the iPhone 12 series glass film.</span>\n</h6>\n<h6 data-mce-fragment="1">※ Regarding compatibility, this glass film was designed after conducting fitting tests with numerous cases, so it can be used without problems with 99.99% or more of smartphone cases.</h6>',
            featuredMedia: {
              id: 'gid://shopify/MediaImage/**************',
              originalSource: {
                url: 'https://shopify-shop-assets.storage.googleapis.com/s/files/1/d/2a4d/0615/4328/4901/products/DSCF6613.jpg?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=merchant-assets%40shopify-tiers.iam.gserviceaccount.com%2F20250327%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250327T050951Z&X-Goog-Expires=300&X-Goog-SignedHeaders=host&X-Goog-Signature=7def383a8996e514a615526700e3b5e8f9d8fbf804fc3ed22a60097e4d8ecfb383e30803f85fba64d788ebfc9446d23165d3563b8891fc05bfcfb081c2e0f12df7f105e2f10270ce15f0b7dcd7b30057301c5d7b55ca7e9a62ff3f09cdf274898caf1fb1c1766e8f29f29ff48ac8543cadf728e6955a5816eecac30e21535765e950fa31bcc0addf2ceb4421531e1c3a9f2c515ad80b60ebeb90bb1d87c47f445a7d9b0de7acb356ff174f53b3eaca6482655adf9a65e96fa2257402c5aeba1dc8230022f98d92091f46667cd1fe2456f4668c82c795a9c6d9915dbe0b873242e60f1b2de23d6fa1c34662fd96f98088e1466291eaa097da49fa84262476e35f',
              },
            },
            handle: 'casefiniteglass',
            id: 'gid://shopify/Product/*************',
            onlineStoreUrl: null,
            options: [
              {
                name: 'Model',
                values: [
                  'iPhone 14 Pro',
                  'iPhone 14 Pro Max',
                  'iPhone 13 mini',
                  'iPhone 13/13 Pro/14',
                  'iPhone 13 Pro Max/14 Plus',
                  'iPhone 12 mini',
                  'iPhone 12/12 Pro',
                  'iPhone 7/8/SE',
                ],
              },
              {
                name: 'Type',
                values: ['Clear', 'Anti-Fingerprint'],
              },
            ],
            tags: ['accessories', 'iOS'],
          },
        },
      ],
    },
  },
]
