import type { ShopifyInvoiceDetails } from '#types/shopify'
import { DateTime } from 'luxon'

export const data: ShopifyInvoiceDetails[] = [
  {
    id: 'gid://shopify/Order/5592613781669',
    taxesIncluded: true,
    taxLines: [
      {
        priceSet: {
          shopMoney: {
            amount: Number.parseFloat('17.72'),
            currencyCode: 'MYR',
          },
        },
        rate: 0.1,
        title: 'SST',
      },
    ],
    createdAt: DateTime.fromISO('2024-07-30T01:55:49Z'),
    lineItems: {
      nodes: [
        // {
        //   discountedUnitPriceSet: {
        //     shopMoney: {
        //       amount: parseFloat('32.49'),
        //       currencyCode: 'MYR',
        //     },
        //   },
        //   originalUnitPriceSet: {
        //     shopMoney: {
        //       currencyCode: 'MYR',
        //       amount: parseFloat('32.49'),
        //     },
        //   },
        //   quantity: 2,
        //   product: {
        //     descriptionHtml: '<span data-mce-fragment="1">Ultra thin smartphone case</span>',
        //   },
        // },
        // {
        //   discountedUnitPriceSet: {
        //     shopMoney: {
        //       amount: parseFloat('32.49'),
        //       currencyCode: 'MYR',
        //     },
        //   },
        //   originalUnitPriceSet: {
        //     shopMoney: {
        //       currencyCode: 'MYR',
        //       amount: parseFloat('32.49'),
        //     },
        //   },
        //   quantity: 2,
        //   product: {
        //     descriptionHtml: '<span data-mce-fragment="1">Ultra thin smartphone case</span>',
        //   },
        // },
        // {
        //   discountedUnitPriceSet: {
        //     shopMoney: {
        //       amount: parseFloat('32.49'),
        //       currencyCode: 'MYR',
        //     },
        //   },
        //   originalUnitPriceSet: {
        //     shopMoney: {
        //       currencyCode: 'MYR',
        //       amount: parseFloat('32.49'),
        //     },
        //   },
        //   quantity: 1,
        //   product: {
        //     descriptionHtml: '<span data-mce-fragment="1">Ultra thin smartphone case</span>',
        //   },
        // },
        {
          id: 'gid://shopify/LineItem/12518982025381',
          discountedUnitPriceSet: {
            shopMoney: {
              amount: Number.parseFloat('32.49'),
              currencyCode: 'MYR',
            },
          },
          originalUnitPriceSet: {
            shopMoney: {
              currencyCode: 'MYR',
              amount: Number.parseFloat('32.49'),
            },
          },
          quantity: 1,
          product: {
            descriptionHtml: '<span data-mce-fragment="1">Ultra thin smartphone case</span>',
          },
        },
      ],
    },
    name: ''
  },
]
