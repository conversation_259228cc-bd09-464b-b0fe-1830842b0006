import SubmittedDocument from '#models/submitted_document'
import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'

export default class SubmittedDocumentsController {
  public async retrieveSubmittedDocument({ response, auth, params: { id } }: HttpContext) {
    const user = await auth.authenticate()
    const submittedDocument = await SubmittedDocument.query()
      .where('user_id', user.id)
      .andWhere('id', id)
      .firstOrFail()

    return response.ok({
      data: submittedDocument,
    })
  }

  public async retrieveSubmittedDocumentsWithOrderId({
    request,
    response,
    auth,
    params: { order_id },
  }: HttpContext) {
    logger.info('retrieveSubmittedDocumentsWithOrderId')
    const user = auth.getUserOrFail()
    try {
      const page = Number(request.input('page', 1))
      const perPage = Number(request.input('per_page', 10))
      const sort = request.input('sort', 'created_at:desc').split(':')

      const submittedDocuments = await SubmittedDocument.query()
        .where('user_id', user.id)
        .andWhere('order_id', order_id)
        .orderBy(sort[0], sort[1])
        .paginate(page, perPage)

      console.log(submittedDocuments[0])

      return response.ok({
        data: submittedDocuments,
      })
    } catch (err) {
      logger.error('retrieveSubmittedDocumentsWithOrderId', err)
      return response.badRequest({ success: false, message: 'Failed, please try again' })
    }
  }
}
