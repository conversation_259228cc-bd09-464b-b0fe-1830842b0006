import SubmittedDocument from '#models/submitted_document'
import type { HttpContext } from '@adonisjs/core/http'
import logger from '@adonisjs/core/services/logger'

export default class SubmittedDocumentsController {
  /**
   * @retrieveSubmittedDocument
   * @summary Retrieve a specific submitted document
   * @tag SubmittedDocuments
   * @description Retrieve details of a specific submitted document by ID
   * @operationId retrieveSubmittedDocument
   * @parameter id path true - Submitted document ID to retrieve
   * @responseBody 200 - {"data": {"id": "number", "uuid": "string", "status": "string", "document_hash": "string", "created_at": "string", "updated_at": "string"}}
   * @responseBody 401 - {"error": "Unauthorized"}
   * @responseBody 404 - {"error": "Submitted document not found"}
   * @responseBody 500 - {"error": "Internal server error"}
   */
  public async retrieveSubmittedDocument({ response, auth, params: { id } }: HttpContext) {
    const user = await auth.authenticate()
    const submittedDocument = await SubmittedDocument.query()
      .where('user_id', user.id)
      .andWhere('id', id)
      .firstOrFail()

    return response.ok({
      data: submittedDocument,
    })
  }

  /**
   * @retrieveSubmittedDocumentsWithOrderId
   * @summary Retrieve submitted documents for a specific order
   * @tag SubmittedDocuments
   * @description Retrieve a paginated list of submitted documents associated with a specific order ID
   * @operationId retrieveSubmittedDocumentsWithOrderId
   * @parameter order_id path true - Order ID to retrieve submitted documents for
   * @parameter page query false - Page number for pagination (default: 1)
   * @parameter per_page query false - Number of items per page (default: 10)
   * @parameter sort query false - Sort field and direction (default: created_at:desc)
   * @responseBody 200 - {"data": {"data": "array", "meta": {"total": "number", "per_page": "number", "current_page": "number", "last_page": "number"}}}
   * @responseBody 400 - {"success": "boolean", "message": "Failed, please try again"}
   * @responseBody 401 - {"error": "Unauthorized"}
   * @responseBody 500 - {"error": "Internal server error"}
   */
  public async retrieveSubmittedDocumentsWithOrderId({
    request,
    response,
    auth,
    params: { order_id },
  }: HttpContext) {
    logger.info('retrieveSubmittedDocumentsWithOrderId')
    const user = auth.getUserOrFail()
    try {
      const page = Number(request.input('page', 1))
      const perPage = Number(request.input('per_page', 10))
      const sort = request.input('sort', 'created_at:desc').split(':')

      const submittedDocuments = await SubmittedDocument.query()
        .where('user_id', user.id)
        .andWhere('order_id', order_id)
        .orderBy(sort[0], sort[1])
        .paginate(page, perPage)

      console.log(submittedDocuments[0])

      return response.ok({
        data: submittedDocuments,
      })
    } catch (err) {
      logger.error('retrieveSubmittedDocumentsWithOrderId', err)
      return response.badRequest({ success: false, message: 'Failed, please try again' })
    }
  }
}
