import crypto from 'node:crypto'
import type { HttpContext } from '@adonisjs/core/http'
import env from '#start/env'
import User from '#models/user'
import Company from '#models/company'
import ShopifyStore from '#models/shopify_store'
import { companyLoginOrCreateSchema, shopifyInstallSchema } from '#validators/auth'
import { errors } from '@vinejs/vine'
import { EInvoiceService } from '#services/lhdn_service'

export default class AuthController {
  /**
   * @loginMyInvoisApi
   * @summary Login to my invois API with the client id and secret acquired from myinvois portal
   * @tag Authentication
   * @description Authenticate a company myinvois API credentials as an intermediary system and issue an access token
   * @operationId loginMyInvoisApi
   * @requestBody <companyLoginOrCreateSchema>
   * @responseBody 200 - {"data": {"access_token": "string", "token_type": "string", "expires_in": "number", "scope": "string"}}
   * @responseBody 400 - {"error": "string", "error_description": "string", "error_uri": "string"}
   * @responseBody 401 - {"error": "Missing Authorization header"}
   * @responseBody 500 - {"error": "Internal server error"}
   */
  async loginMyInvoisApi({ request, response, auth, logger }: HttpContext) {
    logger.info('loginMyInvoisApi')

    try {
      // Validate request parameters
      const payload = await companyLoginOrCreateSchema.validate(request.all())

      logger.info(payload, 'loginMyInvoisApi: Validated payload')

      const user = auth.getUserOrFail()

      // Find or create company
      const company = await Company.firstOrCreate(
        { clientId: payload.client_id, userId: user.id },
        {
          ...payload,
          scope: 'InvoicingAPI',
        }
      )

      const tokenResponse = await EInvoiceService.refreshMyInvoisCompanyAccessToken(company)

      // Check if existing token is still valid
      // if refreshMyInvoisCompanyAccessToken() return void means existing still valid)
      if (!tokenResponse) {
        logger.info('loginMyInvoisApi: Reusing existing token')

        return response.json({
          data: {
            access_token: company.accessToken,
            token_type: 'Bearer',
            expires_in: company.tokenExpiresIn,
          },
        })
      }

      return response.json({
        data: {
          access_token: tokenResponse.data.access_token,
          token_type: tokenResponse.data.token_type,
          expires_in: tokenResponse.data.expires_in,
          scope: tokenResponse.data.scope,
        },
      })
    } catch (error) {
      logger.error(error, 'loginMyInvoisApi')

      if (error instanceof errors.E_VALIDATION_ERROR) {
        return response.status(422).json({
          error: 'validation_error',
          messages: error.messages || [
            {
              message: error.message,
              field: 'unknown',
            },
          ],
        })
      }

      if (error.response?.status === 400) {
        return response.status(400).json({
          error: error.response.data.error,
          error_description: error.response.data.error_description,
          error_uri: error.response.data.error_uri,
        })
      }

      return response.status(error.response?.status || error.status || 500).json({
        error: error.response?.data || error.message || 'Internal server error',
      })
    }
  }

  /**
   * @login
   * @summary Login
   * @tag Authentication
   * @description Logs in a user
   * @operationId login
   * @requestBody {"email": "<EMAIL>", "password": "abcd1234"}
   * @responseBody 200 - {"success": "boolean", "data": {"id": "number","username": "string","email": "string","role": "string","token": "string"}}
   * @responseBody 401 - {"error": "Invalid email or password"}
   * @responseBody 500 - {"error": "Internal server error"}
   */
  async login({ request, response, logger }: HttpContext) {
    logger.info('login')

    try {
      const { email, password } = request.only(['email', 'password'])

      const user = await User.verifyCredentials(email, password)
      const token = await User.accessTokens.create(user, undefined, { expiresIn: '30 days' })

      return response.ok({
        success: true,
        // TODO: do not return full user
        data: { ...user.serialize(), token: token.value?.release() },
      })
    } catch (error) {
      logger.error(error, 'login')

      return response.unauthorized({ error: error.message })
    }
  }

  /**
   * @logout
   * @summary Logout
   * @tag Authentication
   * @description Logs out a user
   * @operationId logout
   * @responseBody 200 - {"success": "boolean"}
   * @responseBody 401 - {"error": "Missing Authorization header"}
   * @responseBody 500 - {"error": "Internal server error"}
   */
  async logout({ response, auth, logger }: HttpContext) {
    logger.info('logout')

    const user = auth.getUserOrFail()
    const tokens = await User.accessTokens.all(user)

    for (const token of tokens) {
      await User.accessTokens.delete(user, token.identifier)
    }

    return response.ok({ success: true })
  }

  /**
   * @shopifyInstall
   * @summary Authenticate with Shopify by verifying app bridge token
   * @tag Authentication
   * @description Authenticate a shopify app after installation
   * @operationId shopifyInstall
   * @requestBody <shopifyInstallSchema>
   * @responseBody 200 - {"success": "boolean"}
   * @responseBody 400 - {"error": "string"}
   * @responseBody 500 - {"error": "Internal server error"}
   */
  async shopifyInstall({ request, response, logger }: HttpContext) {
    logger.info('shopifyInstall')

    try {
      const payload = await shopifyInstallSchema.validate(request.all())
      // eslint-disable-next-line @typescript-eslint/naming-convention
      const { shop, access_token } = payload

      // Verify the shop and token with Shopify API
      const query = `
        query {
          shop {
            name
            email
            myshopifyDomain
          }
        }
      `
      const shopifyResponse = await fetch(`https://${shop}/admin/api/2025-01/graphql.json`, {
        method: 'POST',
        headers: {
          'X-Shopify-Access-Token': access_token,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ query }),
      })

      if (!shopifyResponse.ok) {
        return response.status(500).json({
          error: 'Invalid Shopify access token',
        })
      }

      const shopResponse = (await shopifyResponse.json()) as {
        data: {
          shop: {
            name: string
            email: string
            myshopifyDomain: string
          }
        }
      }

      const shopData = shopResponse.data.shop

      const shopifyStore = await ShopifyStore.updateOrCreate(
        {
          shopDomain: shopData.myshopifyDomain,
        },
        {
          shopName: shopData.name,
          accessToken: access_token,
        }
      )

      let user: User | null = await User.query().where('email', shopData.email).first()
      if (!user) {
        user = await User.create({
          email: shopData.email,
          authType: 'shopify',
        })
      }

      await shopifyStore.related('user').associate(user)

      // generate a token for this user
      const token = await User.accessTokens.create(user, undefined, { expiresIn: '30 days' })

      return response.json({
        success: true,
        token: token.value?.release(),
      })
    } catch (error) {
      logger.error(error, 'shopifyInstall')

      if (error.messages) {
        // Validation errors
        return response.status(400).json({
          errors: error.messages,
        })
      }

      return response.status(500).json({
        error: 'Internal server error',
      })
    }
  }

  /**
   * @shopifyInstallWebhook
   * @summary Shopify webhook
   * @tag Authentication
   * @description Handles Shopify webhooks for app installed event (in case shopifyInstall fails)
   * @operationId shopifyInstallWebhook
   * @responseBody 200 - {"success": "boolean"}
   * @responseBody 400 - {"error": "string"}
   * @responseBody 500 - {"error": "Internal server error"}
   */
  async shopifyInstallWebhook({ request, response, logger }: HttpContext) {
    const hmac = request.header('x-shopify-hmac-sha256')
    const body = request.body()
    const calculatedHmac = crypto
      .createHmac('sha256', env.get('SHOPIFY_API_SECRET'))
      .update(JSON.stringify(body))
      .digest('base64')

    if (calculatedHmac !== hmac) {
      return response.status(401).json({ error: 'Invalid webhook signature' })
    }

    const { shop_domain: shop } = body

    logger.info(`Webhook triggered for ${shop}`)

    const existingShop = await ShopifyStore.query().where('shopDomain', shop).first()
    if (existingShop?.accessToken) {
      return response.ok({ success: true })
    }

    // Note: Webhook doesn’t provide accessToken; fetch from DB
    // TODO: get access token from supabase
    // const { data, error } = await supabase
    //   .from("temp_tokens")
    //   .select("access_token")
    //   .eq("shop", shop)
    //   .single();

    // if (error || !data) {
    //   console.error(`Failed to fetch token for ${shop} from Supabase:`, error);
    //   return response.badRequest({ error: 'Token not found' })
    // }

    // const accessToken = data.access_token;

    const accessToken = '1'

    // Run install logic
    const appUrl = 'http://localhost:3000' // TODO: change to production URL
    const installResponse = await fetch(`${appUrl}/api/v1/auth/shopify-install`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // 'X-Shopify-App-Secret': env.get('INTERNAL_API_SECRET'),
      },
      body: JSON.stringify({ shop, accessToken }),
    })

    if (!installResponse.ok) {
      return response.status(400).json({ error: 'Failed to install shop' })
    }

    // Cleanup Supabase
    // await supabase.from('temp_tokens').delete().eq('shop', shop)

    return response.ok({ success: true })
  }

  /**
   * @apiKeyAuth
   * @summary Authenticate with API Key
   * @tag Authentication
   * @description Authenticate using an API key for machine-to-machine communication
   * @operationId apiKeyAuth
   * @requestBody {"api_key": "string"}
   * @responseBody 200 - {"success": "boolean", "data": {"id": "number","email": "string","token": "string"}}
   * @responseBody 401 - {"error": "Invalid API key"}
   * @responseBody 500 - {"error": "Internal server error"}
   */
  async apiKeyAuth({ request, response, logger }: HttpContext) {
    logger.info('apiKeyAuth')

    try {
      const { api_key: apiKey } = request.only(['api_key'])

      if (!apiKey) {
        return response.status(401).json({
          error: 'API key is required',
        })
      }

      // Find user by API key
      const user = await User.query().where('apiKey', apiKey).first()

      if (!user) {
        return response.status(401).json({
          error: 'Invalid API key',
        })
      }

      // Create access token
      const token = await User.accessTokens.create(user, undefined, { expiresIn: '30 days' })

      return response.json({
        success: true,
        data: {
          id: user.id,
          email: user.email,
          token: token.value?.release(),
        },
      })
    } catch (error) {
      logger.error(error, 'apiKeyAuth')

      return response.status(500).json({
        error: 'Internal server error',
      })
    }
  }
}
