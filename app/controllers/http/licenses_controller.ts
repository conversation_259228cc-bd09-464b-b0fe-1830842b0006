import type { HttpContext } from '@adonisjs/core/http'
import User from '#models/user'
import { UserSyncService } from '../../services/user_sync_service.js'
import { LicenseApiService } from '../../services/license_api_service.js'

export default class LicensesController {
  /**
   * Sync a user to License system
   */
  public async syncUser({ request, response }: HttpContext) {
    const userId = request.param('id')

    // Check if the user exists
    const user = await User.find(userId)
    if (!user) {
      return response.notFound({ error: 'User not found' })
    }

    try {
      // Use UserSyncService to sync the user
      const licenseUserId = await UserSyncService.syncUserToLicense(user)

      if (!licenseUserId) {
        return response.internalServerError({ error: 'Failed to sync user to License system' })
      }

      return response.ok({
        success: true,
        data: {
          coreUserId: user.id,
          licenseUserId,
        },
      })
    } catch (error) {
      console.error('Failed to sync user to License system:', error)
      return response.internalServerError({ error: 'Failed to sync user to License system' })
    }
  }

  /**
   * Sync the authenticated user to License system
   */
  public async syncCoreUserToLicense({ response, auth }: HttpContext) {
    const user = auth.getUserOrFail()

    // Reuse the syncUser logic by creating a mock request with the user's ID
    const mockRequest = {
      param: () => user.id,
    }

    return this.syncUser({ request: mockRequest as any, response } as HttpContext)
  }

  /**
   * Get the License user ID for a Core user
   */
  public async getUserLicense({ request, response }: HttpContext) {
    const userId = request.param('id')

    // Check if the user exists
    const user = await User.find(userId)
    if (!user) {
      return response.notFound({ error: 'User not found' })
    }

    if (!user.externalId) {
      return response.notFound({ error: 'License user license not found' })
    }

    return response.ok({
      success: true,
      data: {
        coreUserId: user.id,
        licenseUserId: user.externalId,
      },
    })
  }

  /**
   * Get the Core user for a License user ID
   */
  public async getCoreUser({ request, response }: HttpContext) {
    const licenseUserId = request.param('id')

    // Get the Core user by externalId
    const user = await User.query().where('externalId', licenseUserId).first()
    if (!user) {
      return response.notFound({ error: 'Core user not found for the given License user ID' })
    }

    return response.ok({
      success: true,
      data: {
        coreUserId: user.id,
        licenseUserId,
        user: user.serialize(),
      },
    })
  }

  /**
   * Get organizations for a user from License system
   */
  public async getUserOrganizations({ request, response }: HttpContext) {
    const userId = request.param('id')

    // Check if the user exists
    const user = await User.find(userId)
    if (!user) {
      return response.notFound({ error: 'User not found' })
    }

    // Check if the user has an externalId
    if (!user.externalId) {
      return response.notFound({ error: 'User has no license' })
    }

    try {
      // Get the user's organizations from License system
      const organizations = await LicenseApiService.getUserOrganizations(user.externalId)

      return response.ok({
        success: true,
        data: {
          coreUserId: user.id,
          organizations,
        },
      })
    } catch (error) {
      console.error('Failed to get user organizations from License system:', error)
      return response.notFound({ error: 'Organizations not found for the user' })
    }
  }

  /**
   * Get primary organization for a user from License system
   */
  public async getUserPrimaryOrganization({ request, response }: HttpContext) {
    const userId = request.param('id')

    // Check if the user exists
    const user = await User.find(userId)
    if (!user) {
      return response.notFound({ error: 'User not found' })
    }

    // Check if the user has an externalId
    if (!user.externalId) {
      return response.notFound({ error: 'User has no license' })
    }

    try {
      // Get the user's primary organization from License system
      const organization = await LicenseApiService.getUserPrimaryOrganization(user.externalId)

      return response.ok({
        success: true,
        data: {
          coreUserId: user.id,
          organization,
        },
      })
    } catch (error) {
      console.error('Failed to get user primary organization from License system:', error)
      return response.notFound({ error: 'Primary organization not found for the user' })
    }
  }
}
