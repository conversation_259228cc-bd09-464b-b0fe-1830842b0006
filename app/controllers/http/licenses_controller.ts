import type { HttpContext } from '@adonisjs/core/http'
import User from '#models/user'
import { UserSyncService } from '../../services/user_sync_service.js'
import { LicenseApiService } from '../../services/license_api_service.js'

export default class LicensesController {
  /**
   * @syncUser
   * @summary Sync a user to License system
   * @tag Licenses
   * @description Synchronize a Core user with the License system and create external ID mapping
   * @operationId syncUser
   * @parameter id path true - Core user ID to sync
   * @responseBody 200 - {"success": "boolean", "data": {"coreUserId": "number", "licenseUserId": "string"}}
   * @responseBody 404 - {"error": "User not found"}
   * @responseBody 500 - {"error": "Failed to sync user to License system"}
   */
  public async syncUser({ request, response }: HttpContext) {
    const userId = request.param('id')

    // Check if the user exists
    const user = await User.find(userId)
    if (!user) {
      return response.notFound({ error: 'User not found' })
    }

    try {
      // Use UserSyncService to sync the user
      const licenseUserId = await UserSyncService.syncUserToLicense(user)

      if (!licenseUserId) {
        return response.internalServerError({ error: 'Failed to sync user to License system' })
      }

      return response.ok({
        success: true,
        data: {
          coreUserId: user.id,
          licenseUserId,
        },
      })
    } catch (error) {
      console.error('Failed to sync user to License system:', error)
      return response.internalServerError({ error: 'Failed to sync user to License system' })
    }
  }

  /**
   * @syncCoreUserToLicense
   * @summary Sync authenticated user to License system
   * @tag Licenses
   * @description Synchronize the currently authenticated user with the License system
   * @operationId syncCoreUserToLicense
   * @responseBody 200 - {"success": "boolean", "data": {"coreUserId": "number", "licenseUserId": "string"}}
   * @responseBody 401 - {"error": "Unauthorized"}
   * @responseBody 500 - {"error": "Failed to sync user to License system"}
   */
  public async syncCoreUserToLicense({ response, auth }: HttpContext) {
    const user = auth.getUserOrFail()

    // Reuse the syncUser logic by creating a mock request with the user's ID
    const mockRequest = {
      param: () => user.id,
    }

    return this.syncUser({ request: mockRequest as any, response } as HttpContext)
  }

  /**
   * @getUserLicense
   * @summary Get License user ID for a Core user
   * @tag Licenses
   * @description Retrieve the License system user ID for a given Core user ID
   * @operationId getUserLicense
   * @parameter id path true - Core user ID to get License ID for
   * @responseBody 200 - {"success": "boolean", "data": {"coreUserId": "number", "licenseUserId": "string"}}
   * @responseBody 404 - {"error": "User not found"}
   * @responseBody 500 - {"error": "Internal server error"}
   */
  public async getUserLicense({ request, response }: HttpContext) {
    const userId = request.param('id')

    // Check if the user exists
    const user = await User.find(userId)
    if (!user) {
      return response.notFound({ error: 'User not found' })
    }

    if (!user.externalId) {
      return response.notFound({ error: 'License user license not found' })
    }

    return response.ok({
      success: true,
      data: {
        coreUserId: user.id,
        licenseUserId: user.externalId,
      },
    })
  }

  /**
   * @getCoreUser
   * @summary Get Core user for a License user ID
   * @tag Licenses
   * @description Retrieve the Core user information for a given License system user ID
   * @operationId getCoreUser
   * @parameter id path true - License user ID to get Core user for
   * @responseBody 200 - {"success": "boolean", "data": {"coreUserId": "number", "licenseUserId": "string", "user": "object"}}
   * @responseBody 404 - {"error": "Core user not found for the given License user ID"}
   * @responseBody 500 - {"error": "Internal server error"}
   */
  public async getCoreUser({ request, response }: HttpContext) {
    const licenseUserId = request.param('id')

    // Get the Core user by externalId
    const user = await User.query().where('externalId', licenseUserId).first()
    if (!user) {
      return response.notFound({ error: 'Core user not found for the given License user ID' })
    }

    return response.ok({
      success: true,
      data: {
        coreUserId: user.id,
        licenseUserId,
        user: user.serialize(),
      },
    })
  }

  /**
   * @getUserOrganizations
   * @summary Get user organizations from License system
   * @tag Licenses
   * @description Retrieve all organizations that a user belongs to from the License system
   * @operationId getUserOrganizations
   * @parameter id path true - Core user ID to get organizations for
   * @responseBody 200 - {"success": "boolean", "data": {"coreUserId": "number", "organizations": "array"}}
   * @responseBody 404 - {"error": "User not found"}
   * @responseBody 500 - {"error": "Internal server error"}
   */
  public async getUserOrganizations({ request, response }: HttpContext) {
    const userId = request.param('id')

    // Check if the user exists
    const user = await User.find(userId)
    if (!user) {
      return response.notFound({ error: 'User not found' })
    }

    // Check if the user has an externalId
    if (!user.externalId) {
      return response.notFound({ error: 'User has no license' })
    }

    try {
      // Get the user's organizations from License system
      const organizations = await LicenseApiService.getUserOrganizations(user.externalId)

      return response.ok({
        success: true,
        data: {
          coreUserId: user.id,
          organizations,
        },
      })
    } catch (error) {
      console.error('Failed to get user organizations from License system:', error)
      return response.notFound({ error: 'Organizations not found for the user' })
    }
  }

  /**
   * @getUserPrimaryOrganization
   * @summary Get user primary organization from License system
   * @tag Licenses
   * @description Retrieve the primary organization for a user from the License system
   * @operationId getUserPrimaryOrganization
   * @parameter id path true - Core user ID to get primary organization for
   * @responseBody 200 - {"success": "boolean", "data": {"coreUserId": "number", "organization": "object"}}
   * @responseBody 404 - {"error": "User not found"}
   * @responseBody 500 - {"error": "Internal server error"}
   */
  public async getUserPrimaryOrganization({ request, response }: HttpContext) {
    const userId = request.param('id')

    // Check if the user exists
    const user = await User.find(userId)
    if (!user) {
      return response.notFound({ error: 'User not found' })
    }

    // Check if the user has an externalId
    if (!user.externalId) {
      return response.notFound({ error: 'User has no license' })
    }

    try {
      // Get the user's primary organization from License system
      const organization = await LicenseApiService.getUserPrimaryOrganization(user.externalId)

      return response.ok({
        success: true,
        data: {
          coreUserId: user.id,
          organization,
        },
      })
    } catch (error) {
      console.error('Failed to get user primary organization from License system:', error)
      return response.notFound({ error: 'Primary organization not found for the user' })
    }
  }
}
