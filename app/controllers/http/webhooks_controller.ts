import type { HttpContext } from '@adonisjs/core/http'
import User from '#models/user'
import env from '#start/env'

async function userCreated({ request, response }: HttpContext) {
  const { data } = request.body()
  const { userId, email, name } = data
  // firstName, lastName, phone

  if (!userId || !email) {
    return response.badRequest({ error: 'Missing required user data' })
  }

  // Check if user already exists by externalId
  let user = await User.query().where('externalId', userId).first()

  if (user) {
    // User already exists, update their information
    user.email = email
    user.fullName = name
    await user.save()

    return response.ok({
      success: true,
      message: 'User updated successfully',
      userId: user.id,
    })
  }

  // Create new user
  user = await User.create({
    email,
    fullName: name,
    authType: 'license',
    externalId: userId,
    password: null,
    apiKey: null,
  })

  return response.ok({
    success: true,
    message: 'User created successfully',
    userId: user.id,
  })
}

async function organizationCreated({ request, response }: HttpContext) {
  const { data } = request.body()
  const { organizationId, name, ownerId } = data

  if (!organizationId || !ownerId) {
    return response.badRequest({ error: 'Missing required organization data' })
  }

  if (!name) {
    return response.badRequest({ error: 'Missing organization name' })
  }

  // Find the owner in the Core system by externalId
  const owner = await User.query().where('externalId', ownerId).first()
  if (!owner) {
    // Create the owner if they don't exist
    return response.badRequest({
      error: 'Owner not found in Core system',
      message: 'Please ensure the owner exists in the Core system before creating an organization',
    })
  }

  // We don't create a company in Core for the organization
  // Just log that we received the webhook
  console.log(
    `Received organization creation webhook for organization ${organizationId} (${name}) owned by user ${owner.id}`
  )

  return response.ok({
    success: true,
    message: 'Organization webhook received successfully',
  })
}

export default class WebhooksController {
  /**
   * @licenseHandler
   * @summary Handle License system webhooks
   * @tag Webhooks
   * @description Handle webhooks from the License system for user and organization events
   * @operationId licenseHandler
   * @requestBody {"event": "string", "data": "object"}
   * @responseBody 200 - {"success": "boolean", "message": "string", "userId": "number"}
   * @responseBody 400 - {"error": "Invalid event type"}
   * @responseBody 401 - {"error": "Invalid webhook secret"}
   * @responseBody 500 - {"error": "Failed to process webhook", "message": "string"}
   */
  public async licenseHandler(context: HttpContext) {
    const { request, response } = context

    try {
      // Verify webhook secret
      const webhookSecret = request.header('X-Webhook-Secret')
      if (webhookSecret !== env.get('LICENSE_WEBHOOK_SECRET')) {
        return response.unauthorized({ error: 'Invalid webhook secret' })
      }

      // Get user data from request body
      const { event } = request.body()

      switch (event) {
        case 'user.created':
          return userCreated(context)
        case 'organization.created':
          return organizationCreated(context)
        default:
          return response.badRequest({ error: 'Invalid event type' })
      }
    } catch (error) {
      console.error('Error processing user creation webhook:', error)
      return response.internalServerError({
        error: 'Failed to process webhook',
        message: error.message,
      })
    }
  }
}
