import { countryCodesService, EInvoiceService, stateCodesService } from '#services/lhdn_service'
import type { HttpContext } from '@adonisjs/core/http'
import Company from '#models/company'
import {
  manualInvoiceMultiSchema,
  shopifyEinvoiceSchema,
  syncInvoiceStatusScehma,
} from '#validators/invoice'
// import { multiple_products_1_invoice } from '../../shopify_dummy/shopify_data.js'
import type {
  BillingPeriod,
  EInvoiceV1,
  InvoiceLevelAllowanceCharge,
  InvoiceLevelLineItemTaxesSubTotal,
  LineItem,
  Prepayment,
  Supplier,
} from '#types/einvoice'
import SubmittedDocument from '#models/submitted_document'
import { errors } from '@vinejs/vine'
import { DateTime } from 'luxon'
import { ShopifyService } from '#services/shopify_service'
import type { ShopifyInvoiceDetails, ShopifyTaxLine } from '#types/shopify'
import Order from '#models/order'
// import { consolidatedEInvoiceV1 } from '../../dummy_einvoice/consolidate_invoice.js'
import env from '#start/env'
// import { consolidateInvoiceWithSignature } from '../../dummy_einvoice/consolidate_invoice_with_signature.js'

export default class InvoicesController {
  /**
   * @apiIntegratedSelfServeSubmission
   * @summary API integrated self-serve submission
   * @tag Invoices
   * @description Placeholder endpoint for API integrated self-serve submission functionality
   * @operationId apiIntegratedSelfServeSubmission
   * @responseBody 200 - {"success": "boolean", "message": "string"}
   * @responseBody 500 - {"error": "Internal server error"}
   */
  async apiIntegratedSelfServeSubmission({ response }: HttpContext) {
    // Pending automate with Order row (if seller uploaded the Order info to our db, we can search the order with the invoice code number)
    return response.ok({ success: true, message: 'test' })
  }

  /**
   * @processShopifyInvoiceSubmission
   * @summary Process Shopify invoice submission
   * @tag Invoices
   * @description Process and submit Shopify invoices to MyInvois in bulk
   * @operationId processShopifyInvoiceSubmission
   * @requestBody <shopifyEinvoiceSchema>
   * @responseBody 200 - {"success": "boolean", "message": "string"}
   * @responseBody 400 - {"error": "Failed, please try again"}
   * @responseBody 401 - {"error": "Unauthorized"}
   * @responseBody 404 - {"error": "Company not found"}
   * @responseBody 500 - {"error": "Internal server error"}
   */
  async processShopifyInvoiceSubmission({ request, response, auth, logger }: HttpContext) {
    logger.info('processShopifyInvoiceSubmission')
    const user = await auth.authenticate()

    // Convert to array first
    // Because data submitted from einvoice_shopify for unknown reason will convert from array into object
    const requestBody = request.all()
    const convertToArray = []
    for (const value of Object.values(requestBody)) {
      // TODO: Make sure seller setup tax rate in shopify, else here will hard code to sales tax with fix 0 rate
      const { taxLines, ...other } = value
      convertToArray.push({
        taxLines: [
          ...(taxLines.length > 0
            ? taxLines.map((taxLine: ShopifyTaxLine) => {
                return {
                  priceSet: taxLine.priceSet,
                  rate: taxLine.rate,
                  title: taxLine.title,
                }
              })
            : [
                {
                  priceSet: {
                    shopMoney: {
                      amount: 0,
                      currencyCode: 'MYR',
                    },
                  },
                  rate: 0,
                  title: 'Not Applicable',
                },
              ]),
        ],
        ...other,
      })
    }

    try {
      const payload = await shopifyEinvoiceSchema.validate(convertToArray)

      // Use this for test if needed
      // const shopifyInvoice: ShopifyInvoiceDetails[] = [
      //   payload.map((invoice) => {
      //     const { createdAt, ...otherData } = invoice
      //     return { createdAt: DateTime.fromISO(createdAt.toISOString()), ...otherData }
      //   })[0],
      // ] // multiple_products_1_invoice

      const shopifyInvoice: ShopifyInvoiceDetails[] = payload.map((invoice) => {
        const { createdAt, ...otherData } = invoice
        return { createdAt: DateTime.fromISO(createdAt.toISOString()), ...otherData }
      })
      const company = await Company.findBy('tin_code', 'IG40125832070') // TDDO: use header, auth('my_company')...

      if (!company) {
        return response.notFound('Company not found')
      }

      const supplier: Supplier = {
        address: {
          addressLine0: company.address,
          cityName: company.city,
          country: countryCodesService.getCountryByName(company.country)!.Code,
          postalZone: company.zipCode,
          state: stateCodesService.getByName(company.state)!.Code,
        },
        businessActivityDescription: company.businessActivityDescription,
        contactNumber: company.phone,
        msic: company.msicCode,
        name: company.name,
        registrationNumber: company.registrationNumber,
        registrationType: company.registrationType,
        tin: company.tinCode,
        sstRegistrationNumber: company.sstRegistrationNumber,
        tourismTaxRegistrationNumber: company.tourismTaxRegistrationNumber,
        bankAccount: company.bankAccount,
        exporterCertifiedNumber: company.exporterCertifiedNumber,
        email: company.email,
      }

      const eInvoiceV1s: (EInvoiceV1 & {
        isConsolidate: boolean
        isReady: boolean
        externalId: string
        companyId: number
        userId: number
      })[] = shopifyInvoice.map((invoice) => {
        return {
          ...ShopifyService.transformToEInvoiceV1Format(invoice, supplier),
          isConsolidate: true,
          isReady: true,

          // External Id
          externalId: invoice.id,

          companyId: company.id,
          userId: user.id,
        }
      })

      const submissionOrders = await Order.updateOrCreateMany('invoiceCode', eInvoiceV1s)

      // Proceed with bulk submission
      await EInvoiceService.documentSubmissionService({
        orders: submissionOrders,
        user: user,
        company: company,
      })

      return response.ok({ success: true, message: 'Bulk submit eInvoices Submitted successfully' })
    } catch (error) {
      logger.error('processShopifyInvoiceSubmission')
      return response.status(400).json({
        error: 'Failed, please try again',
      })
    }
  }

  /**
   * @syncShopifyDocumentStatus
   * @summary Sync Shopify document status
   * @tag Invoices
   * @description Synchronize document status between Shopify and MyInvois system
   * @operationId syncShopifyDocumentStatus
   * @requestBody <syncInvoiceStatusScehma>
   * @responseBody 200 - {"message": "string", "data": "array"}
   * @responseBody 400 - {"errors": "array"}
   * @responseBody 422 - {"error": "validation_error", "messages": "array"}
   * @responseBody 500 - {"error": "Internal server error"}
   */
  async syncShopifyDocumentStatus({ request, response, logger }: HttpContext) {
    logger.info('syncShopifyDocumentStatus')
    try {
      const { syncDocuments } = await syncInvoiceStatusScehma.validate(request.all())

      const documentNumbers = syncDocuments.map((doc) => doc.documentNumber)

      const submittedDocuments = await Order.query()
        .select('*')
        .whereIn('invoice_code', documentNumbers)
        .preload('submittedDocuments', (query) => {
          query.orderBy('created_at', 'desc')
        })

      const toSyncDocs: {
        invoiceCodeNumber: string
        status: string
        submissionDate: string
        documentSubmissionId?: string
        failReason?: string
      }[] = []
      for (const doc of submittedDocuments) {
        const originalDoc = syncDocuments.find((d) => d.documentNumber === doc.invoiceCode)

        // Note: Document that is not submitted (empty metadata) in einvoice_shopify will have status 'NotSubmitted', by right when sync, we should only expect all document that has been submitted.
        // But we want to handle the scenario where backend has already submitted but fail to sync at the frontend (einvoice_shopify). Hence, not submitted also need to be sync.
        if (originalDoc?.status !== doc.submittedDocuments[0].status) {
          // Note: Retrieve only latest rows for each document number (hence we use retrieve [0], since we already sort with descending order with created_at column)
          toSyncDocs.push({
            invoiceCodeNumber: doc.invoiceCode,
            status: doc.submittedDocuments[0].status,
            submissionDate: doc.submittedDocuments[0].createdAt?.toISO()?.toString() ?? '',
            documentSubmissionId: doc.submittedDocuments[0].uuid as string | undefined,
            failReason: doc.submittedDocuments[0].failReason,
          })
        }
      }

      return response.ok({
        message: 'Invoice status synced successfully',
        data: toSyncDocs,
      })
    } catch (error) {
      logger.error(error, 'syncShopifyDocumentStatus')

      if (error instanceof errors.E_VALIDATION_ERROR) {
        return response.status(422).json({
          error: 'validation_error',
          messages: error.messages || [
            {
              message: error.message,
              field: 'unknown',
            },
          ],
        })
      }

      if (error.messages) {
        return response.status(400).json({
          errors: error.messages,
        })
      }

      return response.status(500).json({
        error: 'Internal server error',
      })
    }
  }

  /**
   * @retrieveUnlinkedSubmittedDocuments
   * @summary Retrieve unlinked submitted documents
   * @tag Invoices
   * @description Retrieve submitted documents that are not linked with any order (deprecated function)
   * @operationId retrieveUnlinkedSubmittedDocuments
   * @parameter company_id header true - Company ID to filter documents
   * @responseBody 200 - {"data": "array"}
   * @responseBody 400 - {"message": "Company ID is required"}
   * @responseBody 401 - {"error": "Unauthorized"}
   * @responseBody 500 - {"error": "Internal server error"}
   */
  // NOTE: likely deprecated function
  // Retrieve Submitted Documents that is not linked with any order(purpose is to displayed all pending einvoice request that is pending to be validated by seller manually)
  // Since there can be a chance user submit invalid order, seller need to validate it manually
  async retrieveUnlinkedSubmittedDocuments({ request, response, auth }: HttpContext) {
    const user = await auth.authenticate()
    await user.preload('companies')
    const companyId = request.header('company_id')

    if (!companyId) {
      return response.badRequest({
        message: 'Company ID is required',
      })
    }

    const submittedDocuments = await SubmittedDocument.query()
      .whereIn(
        'company_id',
        user.companies.map((company) => company.id)
      )
      .andWhere('user_id', user.id)
      .andWhereNull('order_id')

    return response.ok({
      data: submittedDocuments,
    })
  }

  /**
   * @sellerFullManualSubmitEinvoice
   * @summary Seller full manual submit e-invoice (deprecated)
   * @tag Invoices
   * @description Submit multiple invoices manually by seller (deprecated function)
   * @operationId sellerFullManualSubmitEinvoice
   * @requestBody <manualInvoiceMultiSchema>
   * @responseBody 200 - {"success": "boolean", "message": "string"}
   * @responseBody 400 - {"error": "Bad Request"}
   * @responseBody 401 - {"error": "Unauthorized"}
   * @responseBody 404 - {"message": "Company not found"}
   * @responseBody 422 - {"error": "validation_error", "messages": "array"}
   * @responseBody 500 - {"error": "Internal server error"}
   */
  // NOTE: deprecated function
  async sellerFullManualSubmitEinvoice({ request, response, auth, logger }: HttpContext) {
    const user = auth.getUserOrFail()
    if (!user) {
      logger.error('User not found', 'OrdersController.store')
      return response.unauthorized('Unauthorized')
    }

    const company = await Company.query().where('user_id', user.id).first()
    if (!company) {
      logger.error('Company not found', 'OrdersController.store')
      return response.notFound({
        message: 'Company not found',
      })
    }

    const payload = await request.validateUsing(manualInvoiceMultiSchema)

    try {
      const eInvoiceV1s: (EInvoiceV1 & {
        isReady: boolean
        isConsolidate: boolean
        companyId: number
        userId: number
      })[] = payload.map((invoice) => {
        //============Compute and Prepare data START============
        const invoiceLevelAllowanceCharge: InvoiceLevelAllowanceCharge | undefined =
          invoice.invoiceLevelAllowanceCharge

        const prePayment: Prepayment | undefined = invoice.prePayment
          ? {
              amount: invoice.prePayment?.amount,
              date: DateTime.fromISO(invoice.prePayment?.date.toISOString()),
              time: DateTime.fromISO(invoice.prePayment?.time.toISOString()),
              referenceNumber: invoice.prePayment?.referenceNumber,
            }
          : undefined

        const lineItems: LineItem[] = invoice.lineItems.map((item) => {
          return EInvoiceService.prepareLineItemWithCalculatedTaxes(item)
        })

        const billingPeriod: BillingPeriod | undefined = invoice.billingPeriod
          ? {
              frequency: invoice.billingPeriod.frequency,
              startDate: DateTime.fromISO(invoice.billingPeriod.startDate.toISOString()),
              endDate: DateTime.fromISO(invoice.billingPeriod.endDate.toISOString()),
            }
          : undefined

        const invoiceLevelTax = EInvoiceService.calculateInvoiceLevelTax({
          lineItems: lineItems,
          prePayment: prePayment,
          invoiceLevelAllowanceCharge: invoiceLevelAllowanceCharge,
        })

        //============Compute and Prepare data END============
        return {
          // Core Parties
          supplier: {
            address: {
              addressLine0: company.address,
              cityName: company.city,
              country: countryCodesService.getCountryByName(company.country)!.Code,
              postalZone: company.zipCode,
              state: stateCodesService.getByName(company.state)!.Code,
            },
            businessActivityDescription: company.businessActivityDescription,
            contactNumber: company.phone,
            msic: company.msicCode,
            name: company.name,
            registrationNumber: company.registrationNumber,
            registrationType: company.registrationType,
            tin: company.tinCode,
            sstRegistrationNumber: company.sstRegistrationNumber,
            tourismTaxRegistrationNumber: company.tourismTaxRegistrationNumber,
            bankAccount: company.bankAccount,
            exporterCertifiedNumber: company.exporterCertifiedNumber,
            email: company.email,
          },
          buyer: invoice.isConsolidate
            ? {
                name: env.get('MYINVOIS_API_GENERAL_BUYER_NAME'),
                tin: env.get('MYINVOIS_API_GENERAL_TIN'),
                contactNumber: 'NA',
                sstRegistrationNumber: 'NA',
              }
            : {
                name: invoice.buyer!.name,
                tin: invoice.buyer!.tin,
                address: invoice.buyer!.address,
                contactNumber: invoice.buyer!.contactNumber,
                email: invoice.buyer!.email,
                registrationNumber: invoice.buyer!.registrationNumber,
                registrationType: invoice.buyer!.registrationType,
                sstRegistrationNumber: invoice.buyer!.sstRegistrationNumber,
              },

          invoiceCode: invoice.invoiceCode,
          invoiceDateTime: {
            date: DateTime.fromISO(invoice.invoiceDateTime.date.toISOString()),
            time: DateTime.fromISO(invoice.invoiceDateTime.time.toISOString()),
          },

          lineItems: lineItems,

          invoiceLevelLineItemTaxes: {
            totalTaxAmount: EInvoiceService.calculateInvoiceLevelLineItemTaxes(
              lineItems,
              'TotalTaxAmount'
            ) as number,
            taxSubtotals: EInvoiceService.calculateInvoiceLevelLineItemTaxes(
              lineItems,
              'TaxSubtotals'
            ) as InvoiceLevelLineItemTaxesSubTotal[],
          },

          legalMonetaryTotal: {
            excludingTax: invoiceLevelTax.excludingTax,
            includingTax: invoiceLevelTax.includingTax,
            payableAmount: invoiceLevelTax.payableAmount,

            // Optionals
            discountValue: invoiceLevelTax.discountValue,
            feeAmount: invoiceLevelTax.feeAmount,
            netAmount: invoiceLevelTax.netAmount,
            payableRoundingAmount: invoiceLevelTax.payableRoundingAmount,
          },

          // Mandatory where applicable
          additionalDocumentReference: invoice.additionalDocumentReference,

          // Optionals
          foreignCurrency: invoice.foreignCurrency,
          billingPeriod: billingPeriod,
          payment: invoice.payment,
          prePayment: prePayment,
          billingReferenceNumber: invoice.billingReferenceNumber,
          invoiceLevelAllowanceCharges: invoiceLevelAllowanceCharge,

          // Flags
          isConsolidate: false,
          isReady: true,

          companyId: company.id,
          userId: user.id,
        }
      })

      const submissionOrders = await Order.updateOrCreateMany('id', eInvoiceV1s)

      // Proceed with bulk submission
      await EInvoiceService.documentSubmissionService({
        orders: submissionOrders,
        user: user,
        company: company,
      })

      return response.ok({ success: true, message: 'Submitted successfully' })
    } catch (err) {
      logger.error('manualSubmitEinvoice')
      return response.status(400).json({
        error: 'Failed, please try again',
      })
    }
  }

  /**
   * @retrieveLhdnSubmittedDocuments
   * @summary Retrieve LHDN submitted document details
   * @tag Invoices
   * @description Retrieve detailed information about a submitted document from LHDN for synchronization
   * @operationId retrieveLhdnSubmittedDocuments
   * @parameter uuid path true - UUID of the submitted document to retrieve
   * @responseBody 200 - {"data": "object"}
   * @responseBody 401 - {"error": "Unauthorized"}
   * @responseBody 404 - {"error": "Document not found"}
   * @responseBody 500 - {"error": "Internal server error"}
   */
  // NOTE: use for synchronising between Backend and LHDN
  async retrieveLhdnSubmittedDocuments({
    request,
    response,
    auth,
    params: { uuid },
    logger,
  }: HttpContext) {
    const user = await auth.authenticate()

    const company = await Company.query()
      .where('id', request.header('company_id') ?? '')
      .andWhere('user_id', user.id)
      .firstOrFail()

    try {
      const documentDetails = await EInvoiceService.retrieveDocumentDetails(uuid, company)
      return response.ok({ success: true, data: documentDetails })
    } catch (err) {
      logger.error('retrieveLhdnSubmittedDocuments')
      return response.status(400).json({
        error: 'Failed, please try again',
      })
    }
  }
}
