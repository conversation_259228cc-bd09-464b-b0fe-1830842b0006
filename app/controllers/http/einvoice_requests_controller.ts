import type { HttpContext } from '@adonisjs/core/http'
import Company from '#models/company'
import EinvoiceRequest from '#models/einvoice_request'
import { countryCodesService, EInvoiceService, stateCodesService } from '#services/lhdn_service'
import {
  BillingPeriod,
  InvoiceLevelAllowanceCharge,
  InvoiceLevelLineItemTaxesSubTotal,
  LineItem,
  PartialEinvoiceV1,
  Prepayment,
} from '#types/einvoice'
import {
  selfServeEinvoicePortalSchema,
  sellerReviewSelfServeRequestSchema,
} from '#validators/invoice'
import { DateTime } from 'luxon'
import Order from '#models/order'

export default class EinvoiceRequestsController {
  // async index()

  // async show()

  async selfServeRequest({ request, response, logger }: HttpContext) {
    logger.info('selfServeRequest')
    const payload = await selfServeEinvoicePortalSchema.validate(request.all())

    const company = await Company.findBy('tin_code', request.header('company_tin'))

    if (!company) {
      return response.notFound('Company not found')
    }

    // convert to EInvoiceV1 type
    const partialEInvoiceV1: PartialEinvoiceV1 = {
      invoiceCode: payload.invoiceCodeNumber,
      buyer: {
        name: payload.name,
        address: payload.address,
        contactNumber: payload.contactNumber,
        registrationType: payload.businessRegistration.regType,
        registrationNumber: payload.businessRegistration.newNumber,
        tin: payload.tin,
        email: payload.email,
        sstRegistrationNumber: payload.sstRegistrationNumber,
      },
      supplier: {
        address: {
          addressLine0: company.address,
          cityName: company.city,
          country: countryCodesService.getCountryByName(company.country)!.Code,
          postalZone: company.zipCode,
          state: stateCodesService.getByName(company.state)!.Code,
        },
        businessActivityDescription: company.businessActivityDescription,
        contactNumber: company.phone,
        msic: company.msicCode,
        name: company.name,
        registrationNumber: company.registrationNumber,
        registrationType: company.registrationType,
        tin: company.tinCode,
        sstRegistrationNumber: company.sstRegistrationNumber,
        tourismTaxRegistrationNumber: company.tourismTaxRegistrationNumber,
        bankAccount: company.bankAccount,
        exporterCertifiedNumber: company.exporterCertifiedNumber,
        email: company.email,
      },
    }

    try {
      await EinvoiceRequest.create({
        invoiceCode: partialEInvoiceV1.invoiceCode,
        documentDetails: partialEInvoiceV1,
        companyId: company.id,
      })

      return response.ok({ success: true, message: 'Submitted successfully' })
    } catch (err) {
      logger.error(err, 'selfServeRequest')
      return response.badRequest({
        error: 'Failed, please try again',
      })
    }
  }

  async sellerReviewSelfServeRequest({
    request,
    response,
    auth,
    params: { einvoice_request_id },
    logger,
  }: HttpContext) {
    logger.info('sellerReviewSelfServeRequest')

    // Check if submitted document belongs to user
    const user = await auth.authenticate()
    await user.load('companies')

    const einvoice_request = await EinvoiceRequest.findBy('id', einvoice_request_id)

    if (!einvoice_request) {
      return response.notFound({ success: false, message: 'Einvoice request not found' })
    }

    if (!user.companies.map((company) => company.id).includes(einvoice_request.companyId)) {
      return response.unauthorized({ success: false, message: 'Unauthorized' })
    }

    const company = user.companies.filter((company) => company.id === einvoice_request.companyId)[0]

    const { status, ...payload } = await request.validateUsing(sellerReviewSelfServeRequestSchema)

    try {
      if (status == 'Reject') {
        await einvoice_request.merge({ status: status }).save()
        return response.ok({ success: true, message: 'Rejected e-invoice request successfully' })
      } else {
        // Create the JSON structure based on the MyInvois v1.0 format
        const partialEInvoiceV1: PartialEinvoiceV1 =
          einvoice_request.documentDetails as PartialEinvoiceV1

        //============Compute and Prepare data START============
        const invoiceLevelAllowanceCharge: InvoiceLevelAllowanceCharge | undefined =
          payload.invoiceLevelAllowanceCharge
        const prePayment: Prepayment | undefined = payload.prePayment
          ? {
              amount: payload.prePayment?.amount,
              date: DateTime.fromISO(payload.prePayment?.date.toISOString()),
              time: DateTime.fromISO(payload.prePayment?.time.toISOString()),
              referenceNumber: payload.prePayment?.referenceNumber,
            }
          : undefined

        const lineItems: LineItem[] = payload.lineItems.map((item) => {
          return EInvoiceService.prepareLineItemWithCalculatedTaxes(item)
        })

        const billingPeriod: BillingPeriod | undefined = payload.billingPeriod
          ? {
              frequency: payload.billingPeriod.frequency,
              startDate: DateTime.fromISO(payload.billingPeriod.startDate.toISOString()),
              endDate: DateTime.fromISO(payload.billingPeriod.endDate.toISOString()),
            }
          : undefined

        const invoiceLevelTax = EInvoiceService.calculateInvoiceLevelTax({
          lineItems: lineItems,
          prePayment: prePayment,
          invoiceLevelAllowanceCharge: invoiceLevelAllowanceCharge,
        })

        const invoiceLevelLineItemTaxes = {
          totalTaxAmount: EInvoiceService.calculateInvoiceLevelLineItemTaxes(
            lineItems,
            'TotalTaxAmount'
          ) as number,
          taxSubtotals: EInvoiceService.calculateInvoiceLevelLineItemTaxes(
            lineItems,
            'TaxSubtotals'
          ) as InvoiceLevelLineItemTaxesSubTotal[],
        }

        const invoiceDateTime = {
          date: DateTime.fromISO(payload.invoiceDateTime.toISOString()),
          time: DateTime.fromISO(payload.invoiceDateTime.toISOString()),
        }
        //============Compute and Prepare data END============

        const orders = await Order.updateOrCreateMany('invoiceCode', [
          {
            // Self-serve (Non-API integrate automated details)
            invoiceCode: partialEInvoiceV1.invoiceCode,
            invoiceDateTime: invoiceDateTime,
            supplier: partialEInvoiceV1.supplier,
            buyer: partialEInvoiceV1.buyer,

            // Supplier(our software service client) provided details
            invoiceLevelLineItemTaxes: invoiceLevelLineItemTaxes,

            lineItems: lineItems,

            legalMonetaryTotal: {
              excludingTax: invoiceLevelTax.excludingTax,
              includingTax: invoiceLevelTax.includingTax,
              payableAmount: invoiceLevelTax.payableAmount,

              // Optionals
              discountValue: invoiceLevelTax.discountValue,
              feeAmount: invoiceLevelTax.feeAmount,
              netAmount: invoiceLevelTax.netAmount,
              payableRoundingAmount: invoiceLevelTax.payableRoundingAmount,
            },

            // Mandatory where applicable
            additionalDocumentReference: payload.additionalDocumentReference,

            // Optionals
            deliveryDetails: payload.deliveryDetails,
            foreignCurrency: payload.foreignCurrency,
            billingPeriod: billingPeriod,
            payment: payload.payment,
            prePayment: prePayment,
            billingReferenceNumber: payload.billingReferenceNumber,
            invoiceLevelAllowanceCharge: invoiceLevelAllowanceCharge,

            // Flags
            isConsolidate: false,
            isReady: true,

            userId: user.id,
            companyId: company.id,
          },
        ])

        await EInvoiceService.documentSubmissionService({
          orders: orders,
          user: user,
          company: company,
        })

        return response.ok({ success: true, message: 'Submitted successfully' })
      }
    } catch (error) {
      logger.error('sellerReviewSelfServeRequest')
      return response.status(400).json({
        error: 'Failed, please try again',
      })
    }
  }
}
