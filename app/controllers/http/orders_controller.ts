import Company from '#models/company'
import Order from '#models/order'
import SubmittedDocument from '#models/submitted_document'
import { countryCodesService, EInvoiceService, stateCodesService } from '#services/lhdn_service'
import env from '#start/env'
import type {
  InvoiceLevelAllowanceCharge,
  Prepayment,
  LineItem,
  BillingPeriod,
  InvoiceLevelLineItemTaxesSubTotal,
  EInvoiceV1,
  LegalMonetaryTotal,
  InvoiceDateTime,
} from '#types/einvoice'
import { manualInvoiceSingleSchema } from '#validators/invoice'
import { cancelOrderSchema, updateOrderSchema } from '#validators/order'
import type { HttpContext } from '@adonisjs/core/http'
import db from '@adonisjs/lucid/services/db'
import { DateTime } from 'luxon'

export default class OrdersController {
  /**
   * @submitConsolidateOrdersToMyInvoice
   * @summary Submit consolidate orders to my invois API
   * @tag Orders
   * @description Submit consolidate orders to my invois API
   * @operationId submitConsolidateOrdersToMyInvoice
   * @responseBody 200 - {"data": {"success": "boolean", "message": "string"}}
   * @responseBody 400 - {"error": "Bad Request"}
   * @responseBody 401 - {"error": "Unauthorized"}
   * @responseBody 500 - {"error": "Internal server error"}
   */
  // TODO: To be remove (this is for Pohkim to use in DEMO)
  async submitConsolidateOrdersToMyInvoice({ request, response, auth, logger }: HttpContext) {
    logger.debug({ body: request.all() }, 'OrdersController.submitConsolidateOrdersToMyInvoice')

    const user = auth.getUserOrFail()

    const company = await Company.query().where('user_id', user.id).firstOrFail()

    try {
      const unsubmittedConsolidateInvoices = await Order.query()
        .where('company_id', company.id)
        .andWhere('is_submitted_to_lhdn', false)
        .andWhere('is_ready', true)
        .andWhere('is_consolidate', true)

      await EInvoiceService.documentSubmissionService({
        orders: unsubmittedConsolidateInvoices,
        company: company,
        user: user,
      })

      return response.ok({ success: true, message: 'Consolidate invoices submitted successfully' })
    } catch (err) {
      logger.error(err, 'OrdersController.submitConsolidateOrdersToMyInvoice')
      return response.badRequest({
        success: false,
        message: 'Failed to submit consolidate invoices, please try again.',
      })
    }
  }

  /**
   * @store
   * @summary Create a new order
   * @tag Orders
   * @description Create a new order/invoice draft that can be submitted to MyInvois later
   * @operationId store
   * @requestBody <manualInvoiceSingleSchema>
   * @responseBody 200 - {"success": "boolean", "data": {"id": "number", "invoice_code": "string", "status": "string", "created_at": "string", "updated_at": "string"}}
   * @responseBody 400 - {"error": "Bad Request"}
   * @responseBody 401 - {"error": "Unauthorized"}
   * @responseBody 409 - {"success": "boolean", "message": "Invoice code already exists"}
   * @responseBody 422 - {"error": "validation_error", "messages": "array"}
   * @responseBody 500 - {"error": "Internal server error"}
   */
  // C
  async store({ request, response, auth, logger }: HttpContext) {
    logger.debug({ body: request.all() }, 'OrdersController.store')

    const user = auth.getUserOrFail()

    if (!user) {
      logger.error('User not found', 'OrdersController.store')
      return response.unauthorized('Unauthorized')
    }

    const company = await Company.query().where('user_id', user.id).first()
    if (!company) {
      logger.error('Company not found', 'OrdersController.store')
      return response.notFound({
        message: 'Company not found',
      })
    }

    try {
      const { isSelfBill, ...payload } = await request.validateUsing(manualInvoiceSingleSchema)

      //============Compute and Prepare data START============
      const invoiceLevelAllowanceCharge: InvoiceLevelAllowanceCharge | undefined =
        payload.invoiceLevelAllowanceCharge

      const prePayment: Prepayment | undefined = payload.prePayment
        ? {
            amount: payload.prePayment?.amount,
            date: DateTime.fromISO(payload.prePayment?.date.toISOString()),
            time: DateTime.fromISO(payload.prePayment?.time.toISOString()),
            referenceNumber: payload.prePayment?.referenceNumber,
          }
        : undefined

      const lineItems: LineItem[] = payload.lineItems.map((item) => {
        return EInvoiceService.prepareLineItemWithCalculatedTaxes(item)
      })

      logger.debug(lineItems, 'lineItems')

      const billingPeriod: BillingPeriod | undefined = payload.billingPeriod
        ? {
            frequency: payload.billingPeriod.frequency,
            startDate: DateTime.fromISO(payload.billingPeriod.startDate.toISOString()),
            endDate: DateTime.fromISO(payload.billingPeriod.endDate.toISOString()),
          }
        : undefined

      const invoiceLevelTax = EInvoiceService.calculateInvoiceLevelTax({
        lineItems: lineItems,
        prePayment: prePayment,
        invoiceLevelAllowanceCharge: invoiceLevelAllowanceCharge,
      })
      //============Compute and Prepare data END============

      //============Null check START============
      const country = countryCodesService.getCountryByName(company.country)?.Code
      const state = stateCodesService.getByName(company.state)?.Code

      if (!country || !state) {
        throw new Error('Company address is not valid')
      }

      if (!payload.isConsolidate && !payload.buyer) {
        throw new Error('Buyer is required for consolidate invoice')
      }

      if (!company.tinCode) {
        throw new Error('Supplier tin is required')
      }
      //============Null check END============

      //============Self Bill Checking START============
      if (isSelfBill !== undefined && isSelfBill) {
        // TODO: Implement to handle isSelfbill
      }
      //============Self Bill Checking END============

      const invoiceV1: EInvoiceV1 = {
        // Core Parties
        supplier: {
          address: {
            addressLine0: company.address,
            cityName: company.city,
            country,
            postalZone: company.zipCode,
            state,
          },
          businessActivityDescription: company.businessActivityDescription,
          contactNumber: company.phone,
          msic: company.msicCode,
          name: company.name,
          registrationNumber: company.registrationNumber,
          registrationType: company.registrationType,
          tin: company.tinCode,
          sstRegistrationNumber: company.sstRegistrationNumber,
          tourismTaxRegistrationNumber: company.tourismTaxRegistrationNumber,
          bankAccount: company.bankAccount,
          exporterCertifiedNumber: company.exporterCertifiedNumber,
          email: company.email,
        },
        buyer: payload.isConsolidate
          ? {
              name: env.get('MYINVOIS_API_GENERAL_BUYER_NAME'),
              tin: env.get('MYINVOIS_API_GENERAL_TIN'),
              contactNumber: 'NA',
              sstRegistrationNumber: 'NA',
            }
          : {
              name: payload.buyer!.name,
              tin: payload.buyer!.tin,
              address: payload.buyer!.address,
              contactNumber: payload.buyer!.contactNumber,
              email: payload.buyer!.email,
              registrationNumber: payload.buyer!.registrationNumber,
              registrationType: payload.buyer!.registrationType,
              sstRegistrationNumber: payload.buyer!.sstRegistrationNumber,
            },

        invoiceCode: payload.invoiceCode,
        invoiceDateTime: {
          date: DateTime.fromISO(payload.invoiceDateTime.date.toISOString()),
          time: DateTime.fromISO(payload.invoiceDateTime.time.toISOString()),
        },

        lineItems: lineItems,

        invoiceLevelLineItemTaxes: {
          totalTaxAmount: EInvoiceService.calculateInvoiceLevelLineItemTaxes(
            lineItems,
            'TotalTaxAmount'
          ) as number,
          taxSubtotals: EInvoiceService.calculateInvoiceLevelLineItemTaxes(
            lineItems,
            'TaxSubtotals'
          ) as InvoiceLevelLineItemTaxesSubTotal[],
        },

        legalMonetaryTotal: {
          excludingTax: invoiceLevelTax.excludingTax,
          includingTax: invoiceLevelTax.includingTax,
          payableAmount: invoiceLevelTax.payableAmount,

          // Optionals
          discountValue: invoiceLevelTax.discountValue,
          feeAmount: invoiceLevelTax.feeAmount,
          netAmount: invoiceLevelTax.netAmount,
          payableRoundingAmount: invoiceLevelTax.payableRoundingAmount,
        },

        // Mandatory where applicable
        additionalDocumentReference: payload.additionalDocumentReference,

        // Optionals
        foreignCurrency: payload.foreignCurrency,
        billingPeriod: billingPeriod,
        payment: payload.payment,
        prePayment: prePayment,
        billingReferenceNumber: payload.billingReferenceNumber,
        invoiceLevelAllowanceCharge: invoiceLevelAllowanceCharge,
      }

      logger.debug(invoiceV1, 'invoiceV1')

      const orders = await Order.updateOrCreateMany('invoiceCode', [
        {
          ...invoiceV1,
          isConsolidate: payload.isConsolidate,
          isReady: payload.isReady,
          companyId: company.id,
          userId: user.id,
        },
      ])

      await EInvoiceService.documentSubmissionService({
        orders,
        user,
        company,
      })

      return response.ok({
        success: true,
        message: 'Submitted successfully',
        data: {
          id: orders[0].id,
        },
      })
    } catch (error) {
      logger.error(error, 'OrdersController.store')
      return response.status(400).json({
        message: error.message,
        error: error,
      })
    }
  }

  /**
   * @submitOrderToMyInvois
   * @summary Submit order to MyInvois
   * @tag Orders
   * @description Submit a draft order to MyInvois for validation and processing
   * @operationId submitOrderToMyInvois
   * @parameter id path true - Order ID to submit
   * @responseBody 200 - {"success": "boolean", "data": {"id": "number", "status": "string", "submitted_document": "object"}}
   * @responseBody 400 - {"error": "Bad Request"}
   * @responseBody 401 - {"error": "Unauthorized"}
   * @responseBody 403 - {"success": "boolean", "message": "Order cannot be submitted"}
   * @responseBody 404 - {"error": "Order not found"}
   * @responseBody 500 - {"error": "Internal server error"}
   */
  // NOTE: to submit a draft Order, or resubmit a failed Order after updates
  async submitOrderToMyInvois({ request, response, auth, logger, params: { id } }: HttpContext) {
    logger.debug({ body: request.all() }, 'OrdersController.submitOrderToMyInvois')

    const user = auth.getUserOrFail()

    const company = await Company.query().where('user_id', user.id).firstOrFail()

    const order = await Order.query()
      .where('user_id', user.id)
      .andWhere('company_id', company.id)
      .andWhere('id', id)
      .preload('submittedDocuments')
      .firstOrFail()

    try {
      await EInvoiceService.documentSubmissionService({
        orders: [order],
        user,
        company,
      })

      return response.ok({ success: true, message: 'Submitted successfully' })
    } catch (error) {
      logger.error(error, 'OrdersController.submitOrderToMyInvois')
      return response.status(400).json({
        error: 'Failed, please try again',
      })
    }
  }

  /**
   * @cancelLatestSubmittedDocument
   * @summary Cancel latest submitted document
   * @tag Orders
   * @description Cancel the latest submitted document for an order (only allowed within 72 hours of submission)
   * @operationId cancelLatestSubmittedDocument
   * @parameter id path true - Order ID whose latest submitted document should be cancelled
   * @requestBody <cancelOrderSchema>
   * @responseBody 200 - {"success": "boolean", "message": "Document cancelled successfully"}
   * @responseBody 400 - {"error": "Bad Request"}
   * @responseBody 401 - {"error": "Unauthorized"}
   * @responseBody 403 - {"success": "boolean", "message": "Cannot cancel document"}
   * @responseBody 404 - {"error": "Order not found"}
   * @responseBody 500 - {"error": "Internal server error"}
   */
  // NOTE: to cancel a submitted document
  async cancelLatestSubmittedDocument({
    request,
    response,
    auth,
    logger,
    params: { id },
  }: HttpContext) {
    logger.info('OrdersController.cancelLatestSubmittedDocument')
    try {
      const { reason } = await request.validateUsing(cancelOrderSchema)

      const user = auth.getUserOrFail()

      const company = await Company.findByOrFail('user_id', user.id)

      const order = await Order.query()
        .where('id', id)
        .andWhere('company_id', company.id)
        .preload('submittedDocuments', (query) => query.orderBy('created_at', 'desc').limit(1))
        .firstOrFail()

      if (order.submittedDocuments.length === 0) {
        return response.badRequest({
          success: false,
          message: 'The latest submitted document not found.',
        })
      }

      const latestSubmittedDocument = await SubmittedDocument.query()
        .where('id', order.submittedDocuments[0].id)
        .firstOrFail()

      if (['Cancelled', 'Invalid'].includes(latestSubmittedDocument.status)) {
        return response.forbidden({
          success: false,
          message: 'The latest submitted document has already been cancelled/invalid.',
        })
      }

      if (['Valid', 'Submitted'].includes(latestSubmittedDocument.status)) {
        if (latestSubmittedDocument.createdAt.diffNow().hours >= 72) {
          return response.forbidden({
            success: false,
            message:
              'The latest submitted document has been submitted more than 72 hours ago. Submitted documents can only be cancelled within 72 hours.',
          })
        }
      }

      const cancelResponse = await EInvoiceService.cancelSubmittedDocument(
        latestSubmittedDocument.uuid!,
        company,
        reason
      )

      console.log(cancelResponse)
      // NOTE: example cancelResponse.data = { uuid: 'M0SK5SD115DVNNNPKSCBNDYJ10', status: 'Cancelled' }, if success, status seems always is 'Cancelled'
      // according to the doc https://sdk.myinvois.hasil.gov.my/einvoicingapi/03-cancel-document/
      if (cancelResponse.data.status === 'Cancelled') {
        await db.transaction(async (trx) => {
          await latestSubmittedDocument
            .merge({ status: 'Cancelled', cancelReason: reason })
            .useTransaction(trx)
            .save()
          order.isSubmittedToLhdn = false
          await order.useTransaction(trx).save()
        })
      }

      return response.ok({
        success: cancelResponse.data.status === 'Cancelled',
        message: 'Cancelled submitted document successfully',
      })
    } catch (err) {
      logger.error(err.response.data?.error, 'OrdersController.cancelLatestSubmittedDocument')
      const errDetails = err.response.data?.error?.details[0]
      // Example : err.response.data?.error value
      // {
      //   code: 'ValidationError',
      //   message: 'The document cannot be cancelled or requested for rejection',
      //   target: 'Update Document Status',
      //   details: [
      //     {
      //       code: 'OperationPeriodOver',
      //       target: 'Status',
      //       message: "Document status can't be changed after limit time exceeded",
      //     },
      //   ],
      // }
      if (
        err.response.status === 400 &&
        (errDetails.code === 'OperationPeriodOver' ||
          errDetails.code === 'IncorrectState' ||
          errDetails.code === 'ActiveReferencingDocuments')
      ) {
        return response.badRequest({
          success: false,
          message: errDetails.message,
          code: errDetails.code,
        })
      }

      return response.badRequest({
        success: false,
        message: 'Cancellation failed, please try again.',
      })
    }
  }

  /**
   * @validateInvoiceCodes
   * @summary Validate invoice code uniqueness
   * @tag Orders
   * @description Check if an invoice code is unique within the company's orders
   * @operationId validateInvoiceCodes
   * @parameter invoice_code query true - Invoice code to validate
   * @responseBody 200 - {"data": {"success": "boolean"}}
   * @responseBody 400 - {"error": "Bad Request"}
   * @responseBody 401 - {"error": "Unauthorized"}
   * @responseBody 404 - {"message": "Company not found"}
   * @responseBody 500 - {"error": "Internal server error"}
   */
  // R
  async validateInvoiceCodes({ request, response, auth, logger }: HttpContext) {
    logger.debug({ body: request.all() }, 'OrdersController.retrieveInvoiceCodes')

    const user = auth.getUserOrFail()

    if (!user) {
      return response.unauthorized('Unauthorized')
    }

    const company = await Company.query().where('user_id', user.id).first()
    if (!company) {
      return response.notFound({
        message: 'Company not found',
      })
    }

    const invoiceCode = request.input('invoice_code')

    const findOrderWithInvoiceCode = await Order.query()
      .where('company_id', company.id)
      .andWhere('user_id', user.id)
      .where('invoice_code', invoiceCode)
      .first()

    return response.ok({
      data: {
        success: !findOrderWithInvoiceCode,
      },
    })
  }

  /**
   * @show
   * @summary Get a specific order
   * @tag Orders
   * @description Retrieve details of a specific order by ID
   * @operationId show
   * @parameter id path true - Order ID to retrieve
   * @responseBody 200 - {"data": {"id": "number", "invoice_code": "string", "status": "string", "buyer": "object", "supplier": "object", "line_items": "array", "submitted_documents": "array", "created_at": "string", "updated_at": "string"}}
   * @responseBody 401 - {"error": "Unauthorized"}
   * @responseBody 404 - {"message": "Company not found"}
   * @responseBody 500 - {"error": "Internal server error"}
   */
  async show({ response, auth, params: { id }, logger }: HttpContext) {
    logger.debug('OrdersController.show')

    const user = auth.getUserOrFail()

    if (!user) {
      return response.unauthorized('Unauthorized')
    }

    const company = await Company.query().where('user_id', user.id).first()
    if (!company) {
      return response.notFound({
        message: 'Company not found',
      })
    }

    const order = await Order.query()
      .where('id', id)
      .where('user_id', user.id)
      .where('company_id', company.id)
      .preload('submittedDocuments', (query) => query.orderBy('created_at', 'desc').limit(1))
      .firstOrFail()

    return response.ok({
      data: order,
    })
  }

  /**
   * @index
   * @summary List orders
   * @tag Orders
   * @description Retrieve a paginated list of orders for the authenticated user's company
   * @operationId index
   * @parameter page query false - Page number for pagination (default: 1)
   * @parameter per_page query false - Number of items per page (default: 10)
   * @parameter sort query false - Sort field and direction (default: created_at:desc)
   * @responseBody 200 - {"data": "array", "meta": {"total": "number", "per_page": "number", "current_page": "number", "last_page": "number"}}
   * @responseBody 401 - {"error": "Unauthorized"}
   * @responseBody 404 - {"message": "Company not found"}
   * @responseBody 500 - {"error": "Internal server error"}
   */
  // Retrieve Orders of a single company only
  async index({ request, response, auth, logger }: HttpContext) {
    logger.debug({ body: request.all() }, 'OrdersController.index')

    const page = Number(request.input('page', 1))
    const perPage = Number(request.input('per_page', 10))
    const sort = request.input('sort', 'created_at:desc').split(':')

    const user = auth.getUserOrFail()

    // const companyId = Number(request.header('company_id'))
    // if (!companyId) {
    //   return response.badRequest({
    //     message: 'Company ID is required',
    //   })
    // }

    const company = await Company.query().where('user_id', user.id).first()
    if (!company) {
      return response.notFound({
        message: 'Company not found',
      })
    }

    const orders = await Order.query()
      .where('company_id', company.id)
      .andWhere('user_id', user.id)
      .preload('submittedDocuments', (query) => query.orderBy('created_at', 'desc'))
      .orderBy(sort[0], sort[1])
      .paginate(page, perPage)

    return response.ok(orders)
  }

  /**
   * @update
   * @summary Update an order
   * @tag Orders
   * @description Update an existing order (only allowed for draft orders that haven't been submitted)
   * @operationId update
   * @parameter id path true - Order ID to update
   * @requestBody <updateOrderSchema>
   * @responseBody 200 - {"success": "boolean", "data": {"id": "number", "invoice_code": "string", "status": "string", "updated_at": "string"}}
   * @responseBody 400 - {"error": "Bad Request"}
   * @responseBody 401 - {"error": "Unauthorized"}
   * @responseBody 403 - {"success": "boolean", "message": "Order cannot be updated"}
   * @responseBody 404 - {"error": "Order not found"}
   * @responseBody 409 - {"success": "boolean", "message": "Invoice code already exists"}
   * @responseBody 422 - {"error": "validation_error", "messages": "array"}
   * @responseBody 500 - {"error": "Internal server error"}
   */
  // U
  async update({ request, response, auth, params: { id }, logger }: HttpContext) {
    logger.debug('OrdersController.update')

    const user = auth.getUserOrFail()
    await user.load('companies')

    if (!user) {
      return response.unauthorized('Unauthorized')
    }

    const order = await Order.query()
      .where('id', id)
      .andWhere('user_id', user.id)
      .preload('submittedDocuments')
      .firstOrFail()

    if (order.isSubmittedToLhdn) {
      return response.forbidden({
        success: false,
        message:
          'This document has been submitted to LHDN and is still pending for validation, hence no modification allowed.',
      })
    }

    try {
      const payload = await updateOrderSchema.validate(request.all())

      if (order.status === 'Submitted' || order.status === 'Valid') {
        return response.forbidden({
          success: false,
          message:
            'A submitted order cannot be updated, please cancel first and try again. A submitted document can only be cancelled within 72 hours after submission',
        })
      }

      if (payload.invoiceCode) {
        const notUniqueInvoiceCode = await Order.query()
          .where('invoice_code', payload.invoiceCode)
          .andWhere('company_id', user.companies[0].id)
          .andWhereNot('id', order.id)
          .first()

        if (notUniqueInvoiceCode) {
          return response.conflict({
            success: false,
            message: 'Invoice code already exists',
          })
        }
      }

      const updatedLineItemsWithTaxesAndAllowanceCharge: LineItem[] | undefined =
        payload.lineItems?.map((lineItem) => {
          return EInvoiceService.prepareLineItemWithCalculatedTaxes(lineItem)
        })

      const updatedInvoiceLevelAllowanceCharge = payload.invoiceLevelAllowanceCharge

      const updatedPrepayment: Prepayment | undefined = payload.prePayment
        ? {
            amount: payload.prePayment.amount,
            date: DateTime.fromISO(payload.prePayment.date.toISOString()),
            time: DateTime.fromISO(payload.prePayment.time.toISOString()),
            referenceNumber: payload.prePayment.referenceNumber,
          }
        : undefined

      const updatedLegalMonetaryTotal: LegalMonetaryTotal | undefined =
        updatedLineItemsWithTaxesAndAllowanceCharge &&
        EInvoiceService.calculateInvoiceLevelTax({
          lineItems: updatedLineItemsWithTaxesAndAllowanceCharge,
          prePayment: updatedPrepayment,
          invoiceLevelAllowanceCharge: updatedInvoiceLevelAllowanceCharge,
        })

      const updatedInvoiceLevelLineItemTaxes = updatedLineItemsWithTaxesAndAllowanceCharge && {
        totalTaxAmount: EInvoiceService.calculateInvoiceLevelLineItemTaxes(
          updatedLineItemsWithTaxesAndAllowanceCharge,
          'TotalTaxAmount'
        ) as number,
        taxSubtotals: EInvoiceService.calculateInvoiceLevelLineItemTaxes(
          updatedLineItemsWithTaxesAndAllowanceCharge,
          'TaxSubtotals'
        ) as InvoiceLevelLineItemTaxesSubTotal[],
      }

      const updatedBillingPeriod: BillingPeriod | undefined = payload.billingPeriod
        ? {
            frequency: payload.billingPeriod.frequency,
            startDate: DateTime.fromISO(payload.billingPeriod.startDate.toISOString()),
            endDate: DateTime.fromISO(payload.billingPeriod.endDate.toISOString()),
          }
        : undefined

      const updatedInvoiceDateTime: InvoiceDateTime | undefined = payload.invoiceDateTime && {
        date: DateTime.fromISO(payload.invoiceDateTime.date.toISOString()),
        time: DateTime.fromISO(payload.invoiceDateTime.time.toISOString()),
      }

      const updatedOrder = await order
        .merge({
          ...payload,
          invoiceDateTime: updatedInvoiceDateTime ?? order.invoiceDateTime,

          lineItems: updatedLineItemsWithTaxesAndAllowanceCharge ?? order.lineItems,
          legalMonetaryTotal: updatedLegalMonetaryTotal ?? order.legalMonetaryTotal,
          invoiceLevelLineItemTaxes:
            updatedInvoiceLevelLineItemTaxes ?? order.invoiceLevelLineItemTaxes,
          prePayment: updatedPrepayment ?? order.prePayment,
          billingPeriod: updatedBillingPeriod ?? order.billingPeriod,
          invoiceLevelAllowanceCharge:
            updatedInvoiceLevelAllowanceCharge ?? order.invoiceLevelAllowanceCharge,
        })
        .save()

      return response.ok({
        message: 'Order updated successfully',
        data: { id: updatedOrder.id },
      })
    } catch (error) {
      logger.error(error, 'OrdersController.update')
      return response.status(400).json({
        message: error.message,
        error: error,
      })
    }
  }

  /**
   * @destroy
   * @summary Delete an order
   * @tag Orders
   * @description Delete an existing order (only allowed for draft orders that haven't been submitted)
   * @operationId destroy
   * @parameter id path true - Order ID to delete
   * @responseBody 200 - {"message": "Order deleted successfully"}
   * @responseBody 401 - {"error": "Unauthorized"}
   * @responseBody 403 - {"success": "boolean", "message": "Order cannot be deleted"}
   * @responseBody 404 - {"error": "Order not found"}
   * @responseBody 500 - {"error": "Internal server error"}
   */
  // D
  async destroy({ response, auth, params: { id }, logger }: HttpContext) {
    logger.debug('OrdersController.destroy')

    const user = auth.getUserOrFail()

    if (!user) {
      return response.unauthorized('Unauthorized')
    }

    const order = await Order.query()
      .where('id', id)
      .andWhere('user_id', user.id)
      .preload('submittedDocuments')
      .firstOrFail()

    if (order.isSubmittedToLhdn) {
      return response.forbidden({
        success: false,
        message:
          'This document has been submitted to LHDN and is still pending for validation, hence no modification allowed.',
      })
    }

    await order.delete()

    return response.ok({
      message: 'Order deleted successfully',
    })
  }
}
