import type { HttpContext } from '@adonisjs/core/http'
import Company from '#models/company'
import { companyStoreSchema, companyUpdateSchema, tinValidationSchema } from '#validators/company'
import { EInvoiceService } from '#services/lhdn_service'
import { errors } from '@vinejs/vine'
import db from '@adonisjs/lucid/services/db'
import { DateTime } from 'luxon'

export default class CompaniesController {
  /**
   * @store
   * @summary Store a new company
   * @tag Companies
   * @description Store a new company
   * @operationId store
   * @requestBody <companyStoreSchema>
   * @responseBody 200 - {"data": {"id": "number", "name": "string", "tin_code": "string", "client_id": "string", "client_secret": "string", "scope": "string", "created_at": "string", "updated_at": "string"}}
   * @responseBody 400 - {"error": "string", "error_description": "string", "error_uri": "string"}
   * @responseBody 401 - {"error": "Missing Authorization header"}
   * @responseBody 500 - {"error": "Internal server error"}
   */
  async store({ request, response, auth, logger }: HttpContext) {
    logger.debug({ body: request.all() }, 'CompaniesController store')

    try {
      const payload = await companyStoreSchema.validate(request.all())
      const user = auth.getUserOrFail()

      const company = await Company.updateOrCreate(
        { name: payload.name, userId: user.id },
        {
          ...payload,
        }
      )

      return response.ok({
        data: company,
      })
    } catch (error) {
      logger.error(error, 'CompaniesController store')

      return response.badRequest({
        error: 'Invalid request data',
      })
    }
  }

  /**
   * @show
   * @summary Get the company for the current saas user
   * @tag Companies
   * @description Get a company
   * @operationId show
   * @responseBody 200 - {"data": {"id": "number", "name": "string", "tin_code": "string", "client_id": "string", "client_secret": "string", "scope": "string", "created_at": "string", "updated_at": "string"}}
   * @responseBody 401 - {"error": "Missing Authorization header"}
   * @responseBody 500 - {"error": "Internal server error"}
   */
  async showMyCompany({ response, auth, logger }: HttpContext) {
    logger.debug('CompaniesController showMyCompany')
    try {
      const user = auth.getUserOrFail()
      const company = await Company.query().where('user_id', user.id).first()

      return response.ok({ data: company })
    } catch (error) {
      logger.error(error, 'CompaniesController showMyCompany')

      return response.internalServerError({
        error: 'Failed to retrieve company',
      })
    }
  }

  /**
   * @update
   * @summary Update the company for the current saas user
   * @tag Companies
   * @description Update a company
   * @operationId update
   * @requestBody <companyUpdateSchema>
   * @responseBody 200 - {"data": {"id": "number", "name": "string", "tin_code": "string", "client_id": "string", "client_secret": "string", "scope": "string", "created_at": "string", "updated_at": "string"}}
   * @responseBody 400 - {"error": "string", "error_description": "string", "error_uri": "string"}
   * @responseBody 401 - {"error": "Missing Authorization header"}
   * @responseBody 500 - {"error": "Internal server error"}
   */
  async updateMyCompany({ auth, request, response, logger }: HttpContext) {
    logger.debug('CompaniesController updateMyCompany')

    try {
      const user = auth.getUserOrFail()
      const company = await Company.query().where('user_id', user.id).first()

      if (!company) {
        return response.notFound({
          error: 'Company not found',
        })
      }

      const payload = await companyUpdateSchema.validate(request.all())
      company.merge(payload)
      await company.save()

      return response.ok({ data: company })
    } catch (error) {
      logger.error(error, 'CompaniesController updateMyCompany')

      return response.internalServerError({
        error: 'Failed to update company',
      })
    }
  }

  /**
   * @update
   * @summary Update an existing company
   * @tag Companies
   * @description Update an existing company
   * @operationId update
   * @requestBody <companyUpdateSchema>
   * @responseBody 200 - {"data": {"id": "number", "name": "string", "tin_code": "string", "client_id": "string", "client_secret": "string", "scope": "string", "created_at": "string", "updated_at": "string"}}
   * @responseBody 400 - {"error": "string", "error_description": "string", "error_uri": "string"}
   * @responseBody 401 - {"error": "Missing Authorization header"}
   * @responseBody 500 - {"error": "Internal server error"}
   */
  async update({ request, response, params, logger }: HttpContext) {
    logger.debug('CompaniesController update')

    try {
      const payload = await companyUpdateSchema.validate(request.all())

      const company = await Company.query().where('id', params.id).first()

      if (!company) {
        return response.notFound({
          error: 'Company not found',
        })
      }

      company.merge(payload)
      await company.save()

      return response.ok({
        data: company,
      })
    } catch (error) {
      logger.error(error, 'CompaniesController update')

      return response.badRequest({
        error: 'Invalid request data',
      })
    }
  }

  ///////// Shopify API /////////

  /**
   * @shopifyShow
   * @summary Get the company for the current shopify user
   * @tag Shopify
   * @description Get the company for the current shopify user (assume shopify user would only have one company)
   * @operationId shopifyShow
   * @responseBody 200 - {"data": {"id": "number", "name": "string", "tin_code": "string", "client_id": "string", "client_secret": "string", "scope": "string", "created_at": "string", "updated_at": "string"}}
   * @responseBody 401 - {"error": "Missing Authorization header"}
   * @responseBody 500 - {"error": "Internal server error"}
   */
  async shopifyShow({ response, auth, logger }: HttpContext) {
    logger.debug('CompaniesController shopifyShow')

    const company = await Company.query().where('userId', auth.getUserOrFail().id).first()

    if (!company) {
      return response.notFound({
        error: 'Company not found',
      })
    }

    return response.ok({
      data: company,
    })
  }

  /**
   * @shopifyUpdate
   * @summary Update an existing company for the current shopify user
   * @tag Shopify
   * @description Update an existing company for the current shopify user
   * @operationId shopifyUpdate
   * @requestBody <companyUpdateSchema>
   * @responseBody 200 - {"data": {"id": "number", "name": "string", "tin_code": "string", "client_id": "string", "client_secret": "string", "scope": "string", "created_at": "string", "updated_at": "string"}}
   * @responseBody 400 - {"error": "string", "error_description": "string", "error_uri": "string"}
   * @responseBody 401 - {"error": "Missing Authorization header"}
   * @responseBody 500 - {"error": "Internal server error"}
   */
  async shopifyUpdate({ request, response, auth, logger }: HttpContext) {
    logger.debug('CompaniesController shopifyUpdate')

    const trx = await db.transaction()

    try {
      const payload = await companyUpdateSchema.validate(request.all())

      const company = await Company.updateOrCreate(
        {
          userId: auth.getUserOrFail().id,
        },
        { ...payload },
        { client: trx }
      )

      const tokenResponse = await EInvoiceService.LinkMyInvoisApi(
        company,
        company.clientId,
        company.clientSecret
      )

      // Update company with new token
      company.accessToken = tokenResponse.data.access_token
      company.tokenExpiresIn = tokenResponse.data.expires_in
      company.tokenExpiresAt = DateTime.now().plus({
        seconds: tokenResponse.data.expires_in,
      })
      company.scope = tokenResponse.data.scope
      const companyWithLatestAccessToken = await company.useTransaction(trx).save()

      if (
        companyWithLatestAccessToken.tinCode &&
        companyWithLatestAccessToken.registrationType &&
        companyWithLatestAccessToken.registrationNumber
      ) {
        await EInvoiceService.validateTin(
          companyWithLatestAccessToken.tinCode,
          tokenResponse.data.access_token,
          companyWithLatestAccessToken.registrationType,
          companyWithLatestAccessToken.registrationNumber
        )
      }

      await trx.commit()
      return response.ok({
        data: payload,
      })
    } catch (error) {
      logger.error(error, 'CompaniesController shopifyUpdate')
      trx.rollback()

      if (error instanceof errors.E_VALIDATION_ERROR) {
        return response.status(422).json({
          error: 'validation_error',
          messages: error.messages || [
            {
              message: error.message,
              field: 'unknown',
            },
          ],
        })
      }

      if (error.response?.status === 400) {
        return response.status(400).json({
          error: error.response.data.error,
          error_description: error.response.data.error_description,
          error_uri: error.response.data.error_uri,
        })
      }

      if (error.response?.status === 404) {
        return response.notFound({
          error: 'tin_invalid',
          error_description: 'TIN is invalid',
        })
      }

      return response.status(error.response?.status || error.status || 500).json({
        error: error.response?.data || error.message || 'Internal server error',
      })
    }
  }

  /**
   * @validateTin
   * @tag Companies
   * @summary Validate TIN
   * @description Validate TIN
   * @requestBody <tinValidationSchema>
   * @responseBody 200 - {"data": {"valid": "boolean"}}
   * @responseBody 400 - {"error": "string", "error_description": "string", "error_uri": "string"}
   * @responseBody 500 - {"error": "Internal server error"}
   */
  async validateTin({ auth, request, response, logger }: HttpContext) {
    logger.info('CompaniesController validateTin')

    const payload = await tinValidationSchema.validate(request.all())

    // use accessToken from Gaincue Sdn Bhd as const
    const user = auth.getUserOrFail()
    const company = await Company.query().where('user_id', user.id).first()
    
    if (!company?.accessToken) {
      // should never happen
      return response.internalServerError({
        error: 'Internal server error',
      })
    }

    await EInvoiceService.refreshMyInvoisCompanyAccessToken(company)

    try {
      await EInvoiceService.validateTin(
        payload.tin,
        company.accessToken,
        payload.registrationType,
        payload.registrationNumber
      )

      return response.ok({
        data: {
          valid: true,
        },
        message: 'TIN is valid',
      })
    } catch (error) {
      logger.error(error, 'CompaniesController validateTin')

      return response.status(error.response?.status || error.status || 500).json({
        error: error.response?.data || error.message || 'Internal server error',
      })
    }
  }
}
