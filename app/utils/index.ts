import { paymentMethodsService } from '#services/lhdn_service'

/**
 * Converts a Shopify payment method to the corresponding payment code
 * used in the e-invoice system
 *
 * @param paymentMethod The payment method from Shopify (e.g., 'credit_card', 'paypal', etc.)
 * @returns The payment code ('01' to '08') that corresponds to the payment method
 */
export function convertShopifyPaymentMethod(paymentMethod: string): string {
  if (!paymentMethod) {
    return '08' // Default to "Others" if no payment method provided
  }

  // Convert to lowercase for case-insensitive matching
  const method = paymentMethod.toLowerCase()

  // Map Shopify payment methods to e-invoice payment codes
  if (method.includes('cash') || method.includes('cod') || method.includes('cash_on_delivery')) {
    return '01' // Cash
  }

  if (method.includes('cheque') || method.includes('check') || method.includes('money_order')) {
    return '02' // Cheque
  }

  if (
    method.includes('bank_transfer') ||
    method.includes('bank_deposit') ||
    method.includes('wire') ||
    method.includes('direct_deposit') ||
    method.includes('eft') ||
    method.includes('electronic_funds_transfer')
  ) {
    return '03' // Bank Transfer
  }

  if (
    method.includes('credit') ||
    method.includes('credit_card') ||
    method.includes('visa') ||
    method.includes('mastercard') ||
    method.includes('amex') ||
    method.includes('american_express') ||
    method.includes('discover') ||
    method.includes('jcb') ||
    method.includes('diners_club') ||
    method.includes('unionpay')
  ) {
    return '04' // Credit Card
  }

  if (method.includes('debit') || method.includes('debit_card')) {
    return '05' // Debit Card
  }

  if (
    method.includes('wallet') ||
    method.includes('e_wallet') ||
    method.includes('digital_wallet') ||
    method.includes('apple_pay') ||
    method.includes('google_pay') ||
    method.includes('samsung_pay') ||
    method.includes('alipay') ||
    method.includes('wechat') ||
    method.includes('paypal') ||
    method.includes('venmo')
  ) {
    return '06' // e-Wallet / Digital Wallet
  }

  if (method.includes('digital_bank') || method.includes('online_banking')) {
    return '07' // Digital Bank
  }

  return '08' // Others
}

/**
 * Determines if a payment method is a digital payment
 *
 * @param paymentMethodCode The payment method code ('01' to '08')
 * @returns Boolean indicating if it's a digital payment
 */
export function isDigitalPayment(paymentMethodCode: string): boolean {
  return paymentMethodsService.isDigitalPayment(paymentMethodCode)
}
