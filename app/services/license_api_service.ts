import axios from 'axios'
import env from '#start/env'

// biome-ignore lint/complexity/noStaticOnlyClass: <explanation>
export class LicenseApiService {
  private static apiUrl = env.get('LICENSE_API_URL', 'http://localhost:3002')
  private static apiKey = env.get('LICENSE_OUTGOING_API_KEY')

  // Base request method with auth headers
  private static async request<T>(method: string, endpoint: string, data?: T) {
    return axios({
      method,
      url: `${LicenseApiService.apiUrl}${endpoint}`,
      data,
      headers: {
        'Content-Type': 'application/json',
        'X-Invois-Key': LicenseApiService.apiKey,
      },
    })
  }

  // User-related API methods
  public static async getUser(licenseUserId: string) {
    const { data: response } = await LicenseApiService.request(
      'get',
      `/api/integrations/users/${licenseUserId}`
    )
    return response.data
  }

  // TODO: need complete userData
  public static async createUser(userData: { email: string; name: string }) {
    const response = await LicenseApiService.request<typeof userData>(
      'post',
      '/api/integrations/users',
      userData
    )
    return response.data
  }

  // TODO: need complete userData
  public static async updateUser(licenseUserId: string, userData: { email: string; name: string }) {
    const response = await LicenseApiService.request<typeof userData>(
      'put',
      `/api/integrations/users/${licenseUserId}`,
      userData
    )
    return response.data
  }

  // Organization-related API methods
  public static async getUserOrganizations(licenseUserId: string) {
    const response = await LicenseApiService.request(
      'get',
      `/api/integrations/users/${licenseUserId}/organizations`
    )
    return response.data
  }

  public static async getUserPrimaryOrganization(licenseUserId: string) {
    const response = await LicenseApiService.request(
      'get',
      `/api/integrations/users/${licenseUserId}/primary-organization`
    )
    return response.data
  }

  public static async getOrganization(organizationId: string) {
    const response = await LicenseApiService.request(
      'get',
      `/api/integrations/organizations/${organizationId}`
    )
    return response.data
  }
}
