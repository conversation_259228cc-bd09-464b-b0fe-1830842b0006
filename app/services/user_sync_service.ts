import User from '#models/user'
import type { JWTPayload } from './jwt_auth_service.js'
import { LicenseApiService } from './license_api_service.js'

/**
 * Service to handle user synchronization between Core and the License auth service
 */

// biome-ignore lint/complexity/noStaticOnlyClass: <explanation>
export class UserSyncService {
  /**
   * Find or create a local user based on external user data
   */
  public static async findOrCreateLocalUser(
    externalUserId: string,
    payload: JWTPayload
  ): Promise<User | null> {
    try {
      // Only check by externalId since that's the only way we link users now
      // First try to find user by externalId
      let localUser = await User.query().where('externalId', externalUserId).first()

      if (localUser) {
        return localUser
      }

      // Create a new local user from jwt payload data
      localUser = await User.create({
        email: payload.email,
        fullName: payload.name,
        authType: 'license',
        externalId: externalUserId,
        password: null,
        apiKey: null,
      })

      return localUser
    } catch (error) {
      console.error('Failed to find or create local user:', error)
      return null
    }
  }

  /**
   * Update a local user with data from the external auth service
   */
  public static async updateLocalUser(localUser: User, payload: JWTPayload): Promise<User | null> {
    try {
      if (!localUser.externalId) {
        throw new Error('User has no external ID')
      }

      // Update the local user with data from the JWT payload
      if (payload.email) {
        localUser.email = payload.email
      }

      if (payload.name) {
        localUser.fullName = payload.name
      }

      await localUser.save()

      return localUser
    } catch (error) {
      console.error('Failed to update local user:', error)
      return null
    }
  }

  /**
   * Sync a local user to the License system
   */
  public static async syncUserToLicense(user: User): Promise<string | null> {
    try {
      const userData = {
        email: user.email,
        name: user.fullName || user.email.split('@')[0],
      }

      // If user already has an externalId, update the existing user
      if (user.externalId) {
        await LicenseApiService.updateUser(user.externalId, userData)
        return user.externalId
      }

      // Create a new user in License system
      const newUser = await LicenseApiService.createUser(userData)

      // Update the user with the new externalId
      user.externalId = newUser.id
      await user.save()

      return newUser.id
    } catch (error) {
      console.error('Failed to sync user to License system:', error)
      return null
    }
  }
}
