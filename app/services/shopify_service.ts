import type {
  Buyer,
  EInvoiceV1,
  InvoiceLevelLineItemTaxesSubTotal,
  LineItem,
  Supplier,
} from '#types/einvoice'
import logger from '@adonisjs/core/services/logger'
import { classificationCodesService, EInvoiceService, taxTypesService } from './lhdn_service.js'
import env from '#start/env'
import type { ShopifyInvoiceDetails, ShopifyLineItem, ShopifyTaxLine } from '#types/shopify'

// biome-ignore lint/complexity/noStaticOnlyClass: <explanation>
export class ShopifyService {
  /**
   * Transform Shopify order data to the invoice format required by the validator
   * @param shopifyInvoiceDetails The Shopify order data
   * @param supplierInfo The supplier information (not available in Shopify data)
   * @returns Transformed data that matches the invoice validator schema
   */
  public static transformToEInvoiceV1Format(
    shopifyInvoiceDetails: ShopifyInvoiceDetails,
    supplierInfo: Supplier
  ): EInvoiceV1 {
    // Consolidate Invoice, refer https://www.cleartax.com/my/en/consolidated-einvoicing-malaysia
    //============Compute and Prepare data START============
    const shopifyLineItems: ShopifyLineItem[] = shopifyInvoiceDetails.lineItems.nodes

    // Prepare Buyer Info (as consolidate buyer info, hence use general tin and name)
    const buyer: Buyer = {
      name: env.get('MYINVOIS_API_GENERAL_BUYER_NAME'),
      tin: env.get('MYINVOIS_API_GENERAL_TIN'),
      contactNumber: 'NA',
      sstRegistrationNumber: 'NA',
    }

    const lineItems: LineItem[] = shopifyLineItems.map((item: ShopifyLineItem) => {
      // calculate to get back the original price, if tax is included
      const taxIncluded = shopifyInvoiceDetails.taxesIncluded

      const originalPriceBeforeTax: number = taxIncluded
        ? item.originalUnitPriceSet.shopMoney.amount -
          shopifyInvoiceDetails.taxLines.reduce((sum, taxLine: ShopifyTaxLine) => {
            // To get the sum of tax amount of different tax type for a single item
            return (
              sum +
              (item.originalUnitPriceSet.shopMoney.amount -
                item.originalUnitPriceSet.shopMoney.amount / (1 + taxLine.rate))
            )
          }, 0)
        : item.originalUnitPriceSet.shopMoney.amount

      return {
        id: item.id,
        classifications: [
          classificationCodesService.getCodeByNumber('004')!.Code, // 004 stands for consolidated e-invoice type
        ],
        unit: {
          count: item.quantity,
          price: originalPriceBeforeTax,
        },
        description: item.product.descriptionHtml ?? 'Consolidated Invoice item',
        originCountry: 'MYS', // Only expect Malaysia seller on shopify to use E-Invoice.
        allowanceCharges: [
          {
            amount:
              item.discountedUnitPriceSet.shopMoney.amount ===
              item.originalUnitPriceSet.shopMoney.amount
                ? originalPriceBeforeTax
                : item.discountedUnitPriceSet.shopMoney.amount, // if equal means 100% discount (possibly free gift or promotional buy 1 free 1)
            reason: 'Discount/Free Item',
            isCharge: false,
            rate:
              item.discountedUnitPriceSet.shopMoney.amount !== 0
                ? item.discountedUnitPriceSet.shopMoney.amount /
                  item.originalUnitPriceSet.shopMoney.amount
                : 0,
            // Shopify API does not provide percentage for discount
            // Hence calculate ourself, we are using originalUnitPriceSet.shopMoney.amount (which can be taxes included) because in shopify API, discount amount also based on it.
            // eg. Discount for 32.49 (tax included), if the discount is full 32.49, then rate is 1, if the discount is 16.245, then rate is 0.5
            // Hence we should not use originalPriceBeforeTax (which can potentially be the original price before tax)
          },
        ],
        taxDetails:
          shopifyInvoiceDetails.taxLines.length > 0
            ? shopifyInvoiceDetails.taxLines.map((taxLine) => {
                return {
                  taxType:
                    taxTypesService.getByDescription(taxLine.title)?.Code ??
                    env.get('MYINVOIS_API_DEFAULT_TAX_TYPE'),
                  taxRate: {
                    percentage:
                      (taxTypesService.getByDescription(taxLine.title)?.Code ? taxLine.rate : 0) *
                      100,
                    // If Tax Type exist, then apply the taxLine.rate, else set percentage as 0
                    // convert to percentage %, 0.1 = 10%, since shopify rate is percentage in decimal.
                  },
                  // We assume shopify products will never have any tax exemption, if any should be not applicable.
                }
              })
            : [
                {
                  taxType: env.get('MYINVOIS_API_DEFAULT_TAX_TYPE'),
                  taxRate: {
                    percentage: 0,
                  },
                  // if no tax line element, default to not applicalbe and rate 0
                },
              ],
        taxAmount: taxIncluded
          ? item.originalUnitPriceSet.shopMoney.amount - originalPriceBeforeTax
          : shopifyInvoiceDetails.taxLines.reduce((sum, taxLine: ShopifyTaxLine) => {
              return sum + taxLine.rate * item.originalUnitPriceSet.shopMoney.amount
            }, 0) * item.quantity,
      }
    })

    const invoiceLevelTax = EInvoiceService.calculateInvoiceLevelTax({
      lineItems: lineItems,
    })
    //============Compute and Prepare data END============

    // Construct the final invoice data object
    return {
      supplier: supplierInfo,
      buyer: buyer,

      invoiceCode: shopifyInvoiceDetails.name,
      invoiceDateTime: {
        date: shopifyInvoiceDetails.createdAt,
        time: shopifyInvoiceDetails.createdAt,
      },

      invoiceLevelLineItemTaxes: {
        totalTaxAmount: EInvoiceService.calculateInvoiceLevelLineItemTaxes(
          lineItems,
          'TotalTaxAmount'
        ) as number,
        taxSubtotals: EInvoiceService.calculateInvoiceLevelLineItemTaxes(
          lineItems,
          'TaxSubtotals'
        ) as InvoiceLevelLineItemTaxesSubTotal[],
      },

      legalMonetaryTotal: {
        excludingTax: invoiceLevelTax.excludingTax,
        includingTax: invoiceLevelTax.includingTax,
        payableAmount: invoiceLevelTax.payableAmount,

        // Optionals
        discountValue: invoiceLevelTax.discountValue,
        feeAmount: invoiceLevelTax.feeAmount,
        netAmount: invoiceLevelTax.netAmount,
        payableRoundingAmount: invoiceLevelTax.payableRoundingAmount,
      },

      lineItems: lineItems,
    }
  }

  /**
   * Notify Shopify about the status of the document
   * @param shop The shop domain
   * @param updates The document status
   */
  public static async notifyShopifyDocumentStatus(
    shop: string,
    updates: {
      invoiceCodeNumber: string
      status: 'Valid' | 'Invalid' | 'Submitted' | 'Cancelled'
      submissionDate: string
      documentSubmissionId?: string
      failReason?: string
    }[]
  ) {
    const baseUrl = '' // TODO: base url is app hosted url in production, use cloudflare link from shopify for now
    const secret = 'debug' // TODO: different secret for different shop, add to shopify store model?

    try {
      const response = await fetch(`${baseUrl}/webhooks/app/order_status_update`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${secret}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ shop, updates }),
      })

      if (!response.ok) {
        throw new Error(`Failed to notify Shopify: ${response.statusText}`)
      }
    } catch (error) {
      logger.error('Failed to notify Shopify about document status', { error })
    }
  }
}
