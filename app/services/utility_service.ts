// biome-ignore lint/complexity/noStaticOnlyClass: <explanation>
export class UtilityService {
  public static async sha256(content: string): Promise<string> {
    const encoder = new TextEncoder()
    const data = encoder.encode(content)
    const hashBuffer = await crypto.subtle.digest('SHA-256', data)
    const hashArray = Array.from(new Uint8Array(hashBuffer))
    return hashArray.map((b) => b.toString(16).padStart(2, '0')).join('')
  }

  public static toBase64 = (input: string): string => {
    return Buffer.from(input).toString('base64')
  }

  public static minifyJson = (json: string | object): string => {
    try {
      // If input is a string, parse it first
      const obj = typeof json === 'string' ? JSON.parse(json) : json

      // Stringify with no spacing or formatting
      return JSON.stringify(obj, null, 0)
    } catch (e) {
      console.error('Failed to minify JSON:', e)
      throw new Error(`Invalid JSON provided: ${e.message}`)
    }
  }

  public static roundingCurrencySens = (amount: number): number => {
    // Rounding Mechanism refer https://www.bnm.gov.my/misc/-/asset_publisher/2BOPbOBfILtL/content/about-the-rounding-mechanism
    const stringifiedAmount = amount.toFixed(2)
    let roundedAmount: number = 0

    // Note: 10.34 split into [0] 10 and [1] 34
    if (!stringifiedAmount.split('.')[1]) {
      // means it is integer eg. 1000 which means 1000.00
      return roundedAmount
    }

    const amountDecimals = stringifiedAmount.split('.')[1]

    if (['1', '2', '6', '7'].includes(amountDecimals[1])) {
      roundedAmount = ['2', '7'].includes(amountDecimals[1]) ? -0.02 : -0.01
    } else if (['3', '4', '8', '9'].includes(amountDecimals[1])) {
      roundedAmount = ['4', '9'].includes(amountDecimals[1]) ? 0.01 : 0.02
    }
    return roundedAmount
  }

  public static iso3166_2To3(code_3166_2: string): string {
    return code_3166_2.split('-')[1]
  }

  public static removeSpaces(data: string): string {
    return data.replace(/ /g, '')
  }

  public static convertToSpecifiedDecimalPlaces({
    amount,
    decimal_range = 5,
  }: {
    amount: number
    decimal_range?: number
  }): number {
    return parseFloat(amount.toFixed(decimal_range))
  }
}
