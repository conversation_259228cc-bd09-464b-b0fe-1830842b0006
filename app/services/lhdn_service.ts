import {
  AdditionalDocumentReferenceType,
  type ClassificationCode,
  type CountryCode,
  type CurrencyCode,
  type DocumentSubmissionItem,
  type DocumentSubmissionResponse,
  type EInvoiceV1,
  GetDocumentDetailsResponse,
  GetDocumentSubmissionResponse,
  type InvoiceLevelAllowanceCharge,
  type InvoiceLevelLineItemTaxesSubTotal,
  type InvoiceType,
  type LineItem,
  LineItemAllowanceCharge,
  type LineItemTaxDetail,
  LineItemTaxExemption,
  type MSICCode,
  type PaymentMethod,
  type Prepayment,
  RegistrationType,
  type StateCode,
  type TaxType,
  type UnitType,
} from '#types/einvoice'
import type Company from '#models/company'
import { DocumentSubmission } from '#models/document_submission'
import SubmittedDocument from '#models/submitted_document'
import type User from '#models/user'
import env from '#start/env'
import axios, { type AxiosResponse } from 'axios'
import { UtilityService } from './utility_service.js'
import Order from '#models/order'
import { DateTime } from 'luxon'
import logger from '@adonisjs/core/services/logger'
import db from '@adonisjs/lucid/services/db'

const MYINVOIS_IDENTITY_URL =
  env.get('NODE_ENV') === 'production'
    ? env.get('MYINVOIS_IDENTITY_PROD_URL')
    : env.get('MYINVOIS_IDENTITY_SANDBOX_URL')

const MYINVOIS_API_URL =
  env.get('NODE_ENV') === 'production'
    ? env.get('MYINVOIS_API_PROD_URL')
    : env.get('MYINVOIS_API_SANDBOX_URL')

// biome-ignore lint/complexity/noStaticOnlyClass: <explanation>
export class EInvoiceService {
  public static async schedulerSyncSubmittedDocumentStatus() {
    const submittedDocumentsWithStatusSubmitted = await SubmittedDocument.query()
      .where('status', 'Submitted')
      .orderBy('created_at', 'asc')
      .preload('company')
      .preload('submission')
    const companies = new Set(
      submittedDocumentsWithStatusSubmitted.map((submittedDocument) => submittedDocument.company)
    )

    for await (const company of companies) {
      const refreshTokenResponse = await EInvoiceService.refreshMyInvoisCompanyAccessToken(company)
      const companySubmittedDocuments = submittedDocumentsWithStatusSubmitted.filter(
        (submittedDocument) => {
          return submittedDocument.company.id === company.id
        }
      )

      const submissions = new Set(
        companySubmittedDocuments.map((submittedDocument) => submittedDocument.submission)
      )

      for await (const submission of submissions) {
        const myInvoisResponse = await axios.get<GetDocumentSubmissionResponse>(
          `${env.get('MYINVOIS_API_SANDBOX_URL')}/documentsubmissions/${submission.submissionUid}?pageNo=1&pageSize=${submission.totalDocuments}`,
          {
            headers: {
              'Authorization': refreshTokenResponse
                ? refreshTokenResponse.data.access_token
                : company.accessToken,
              'Content-Type': 'application/json',
            },
          }
        )

        if (myInvoisResponse.data.overallStatus !== 'in progress') {
          // Not in progress, means some document already updated with new status.
          const documentsWithUpdatedStatus = myInvoisResponse.data.documentSummary.filter(
            (documentSummary) => documentSummary.status !== 'Submitted'
          )

          for await (const documentWithUpdatedStatus of documentsWithUpdatedStatus) {
            const submittedDocument = submittedDocumentsWithStatusSubmitted.find(
              (submittedDocument) =>
                submittedDocument.submission.submissionUid ===
                documentWithUpdatedStatus.submissionUid
            )

            if (submittedDocument) {
              await db.transaction(async (trx) => {
                submittedDocument.status = documentWithUpdatedStatus.status
                documentWithUpdatedStatus.documentStatusReason &&
                  (submittedDocument.failReason = documentWithUpdatedStatus.documentStatusReason)
                submittedDocument.longId =  documentWithUpdatedStatus.longId

                await submittedDocument.useTransaction(trx).save()

                // Update Order Flag
                const order = await Order.findOrFail(submittedDocument.orderId)
                order.isSubmittedToLhdn = false
                await order.useTransaction(trx).save()
              })
            }
          }
        }
      }

      // TODO: Add email event to send email notify user about document status have been updated
    }
  }

  public static async schedulerSyncInvalidSubmittedDocumentFailDetails() {
    const submittedDocumentsWithStatusInvalidAndEmptyReason = await SubmittedDocument.query()
      .where('status', 'Invalid')
      .andWhereNull('fail_reason')
      .andWhereNull('fail_details')
      .orderBy('created_at', 'asc')
      .preload('company')
    const companies = new Set(
      submittedDocumentsWithStatusInvalidAndEmptyReason.map(
        (submittedDocument) => submittedDocument.company
      )
    )

    for await (const company of companies) {
      const refreshTokenResponse = await EInvoiceService.refreshMyInvoisCompanyAccessToken(company)
      const companySubmittedDocuments = submittedDocumentsWithStatusInvalidAndEmptyReason.filter(
        (submittedDocument) => {
          return submittedDocument.company.id === company.id
        }
      )

      for await (const submittedDocument of companySubmittedDocuments) {
        const myInvoisResponse = await axios.get<GetDocumentDetailsResponse>(
          `${env.get('MYINVOIS_API_SANDBOX_URL')}/documents/${submittedDocument.uuid}/details`,
          {
            headers: {
              'Authorization': refreshTokenResponse
                ? refreshTokenResponse.data.access_token
                : company.accessToken,
              'Content-Type': 'application/json',
            },
          }
        )

        submittedDocument.failReason = 'Validation Error'
        submittedDocument.failDetails = myInvoisResponse.data.validationResults.validationSteps

        await submittedDocument.save()
      }
    }
  }

  public static sumLineItemAllowanceCharges(
    lineItem: LineItem | Omit<LineItem, 'taxAmount'>
  ): number {
    return UtilityService.convertToSpecifiedDecimalPlaces({
      amount:
        lineItem.allowanceCharges?.reduce((sum, charge) => {
          // Amount
          if (charge.amount) {
            const allowanceChargeInAmount = charge.amount
            if (charge.isCharge) {
              return sum + allowanceChargeInAmount
            } else {
              return sum - allowanceChargeInAmount
            }
          } else {
            const allowanceRateInDecimal = charge.rate! / 100
            const allowanceChargeInRate = allowanceRateInDecimal * lineItem.unit.price

            // Percentage Rate
            if (charge.isCharge) {
              return sum + allowanceChargeInRate
            } else {
              return sum - allowanceChargeInRate
            }
          }
        }, 0) ?? 0,
    })
  }

  public static prepareLineItemWithCalculatedTaxes(item: Omit<LineItem, 'taxAmount'>): LineItem {
    // item type is line item without property taxAmount, since taxAmount is expected to be calculated here.
    const itemAllowanceChargesWithAmountAndRate: LineItemAllowanceCharge[] | undefined =
      item.allowanceCharges?.map((allowanceCharge) => {
        const allowanceRateInDecimal = allowanceCharge.rate! / 100
        return {
          amount: UtilityService.convertToSpecifiedDecimalPlaces({
            amount: allowanceCharge.amount
              ? allowanceCharge.amount
              : allowanceRateInDecimal * item.unit.price,
          }),
          rate: UtilityService.convertToSpecifiedDecimalPlaces({
            amount: allowanceCharge.rate
              ? allowanceCharge.rate
              : allowanceRateInDecimal / item.unit.price,
          }),
          isCharge: allowanceCharge.isCharge,
          reason: allowanceCharge.reason,
        }
      }) ?? undefined

    const exemptionAmount = item.taxExemption?.taxableAmount ?? 0
    const computedAllowanceCharge = EInvoiceService.sumLineItemAllowanceCharges(item)

    const finalItemPriceAfterAllowanceCharge = UtilityService.convertToSpecifiedDecimalPlaces({
      amount: item.unit.price + computedAllowanceCharge,
    })

    const lineItemTaxDetailsWithComputedTaxAmount: LineItemTaxDetail[] = []
    const finalTaxableAmount = UtilityService.convertToSpecifiedDecimalPlaces({
      amount: finalItemPriceAfterAllowanceCharge - exemptionAmount,
    })

    if (finalTaxableAmount < 0) {
      throw Error(
        `Item ${item.id} taxable amount after including allowance/charge and exemption is less than 0.`
      )
    }

    const computedTaxAmount = item.taxDetails.reduce(
      (sum: number, taxDetail: LineItemTaxDetail) => {
        const notApplicableTaxType = taxTypesService.getByCode('06')
        if (
          (taxDetail.taxType == notApplicableTaxType!.Code.toString() &&
            taxDetail.taxRate.percentage == undefined) ||
          (taxDetail.taxType == notApplicableTaxType!.Code.toString() &&
            taxDetail.taxRate.percentage &&
            taxDetail.taxRate.percentage > 0)
        ) {
          throw Error(
            `Item ${item.id}, percentage rate of 0 is compulsory for Not Applicable tax type.`
          )
        }

        if (taxDetail.taxType == notApplicableTaxType!.Code && taxDetail.taxRate.ratePerUnit) {
          throw Error(`Item ${item.id}, rate per unit is not allowed for Not Applicable tax type.`)
        }

        if (taxDetail.taxRate.percentage) {
          const taxAmountComputed = UtilityService.convertToSpecifiedDecimalPlaces({
            amount: item.unit.count * finalTaxableAmount * (taxDetail.taxRate.percentage / 100),
          })

          const finalTaxAmount = taxAmountComputed >= 0 ? taxAmountComputed : 0

          lineItemTaxDetailsWithComputedTaxAmount.push({
            ...taxDetail,
            taxAmount: finalTaxAmount,
            taxableAmount: finalTaxableAmount,
          })
          return UtilityService.convertToSpecifiedDecimalPlaces({
            amount: sum + finalTaxAmount,
          })
        } else {
          // If exemption amount is 0 means no exemption, assign 1 to it, means original rate per unit no changes
          const exemptedPercentage =
            exemptionAmount == 0 ? 1 : exemptionAmount / finalItemPriceAfterAllowanceCharge
          const taxAmountComputed = UtilityService.convertToSpecifiedDecimalPlaces({
            amount:
              item.unit.count *
              (taxDetail.taxRate.ratePerUnit! -
                taxDetail.taxRate.ratePerUnit! * exemptedPercentage),
          })

          const finalTaxAmount = taxAmountComputed >= 0 ? taxAmountComputed : 0

          lineItemTaxDetailsWithComputedTaxAmount.push({
            ...taxDetail,
            taxAmount: finalTaxAmount,
            taxableAmount: finalTaxableAmount,
          })
          return UtilityService.convertToSpecifiedDecimalPlaces({
            amount: sum + finalTaxAmount,
          })
        }
      },
      0
    )

    const computedTaxAmountWithoutExemption = item.taxDetails.reduce(
      (sum: number, taxDetails: LineItemTaxDetail) => {
        if (taxDetails.taxRate.percentage) {
          return UtilityService.convertToSpecifiedDecimalPlaces({
            amount:
              sum +
              item.unit.count *
                finalItemPriceAfterAllowanceCharge *
                (taxDetails.taxRate.percentage / 100),
          })
        } else {
          return UtilityService.convertToSpecifiedDecimalPlaces({
            amount: sum + item.unit.count * taxDetails.taxRate.ratePerUnit!,
          })
        }
      },
      0
    )
    const itemExemption: LineItemTaxExemption | undefined = item.taxExemption
      ? {
          reason: item.taxExemption.reason,
          taxableAmount: item.taxExemption.taxableAmount,
          taxAmount: UtilityService.convertToSpecifiedDecimalPlaces({
            amount: computedTaxAmountWithoutExemption - computedTaxAmount,
          }),
        }
      : undefined

    logger.debug({ computedTaxAmount: computedTaxAmount }, 'Computed tax amount:')
    logger.debug(
      { computedTaxAmountWithoutExemption: computedTaxAmountWithoutExemption },
      'Computed tax amount without exemption: '
    )
    logger.debug(
      lineItemTaxDetailsWithComputedTaxAmount,
      'Line item tax details with computed tax amount:'
    )
    logger.debug(item.taxExemption, 'Exemption:')

    return {
      id: item.id,
      classifications: item.classifications,
      description: item.description,
      unit: item.unit,
      taxDetails: lineItemTaxDetailsWithComputedTaxAmount,
      taxAmount: computedTaxAmount,
      taxExemption: itemExemption,
      originCountry: item.originCountry,
      allowanceCharges: itemAllowanceChargesWithAmountAndRate,
      tarriffCode: item.tarriffCode,
    }
  }

  public static calculateInvoiceLevelLineItemTaxes(
    lineItems: LineItem[],
    type: 'TotalTaxAmount' | 'TaxSubtotals'
  ): number | InvoiceLevelLineItemTaxesSubTotal[] {
    if (type === 'TotalTaxAmount') {
      return UtilityService.convertToSpecifiedDecimalPlaces({
        amount: lineItems.reduce((sum, item) => {
          return sum + item.taxAmount
        }, 0),
      })
    } else {
      const taxSubtotalOfEachTaxType: InvoiceLevelLineItemTaxesSubTotal[] = []

      lineItems.forEach((item) => {
        item.taxDetails.forEach((taxDetail) => {
          const isExist = taxSubtotalOfEachTaxType
            .map((taxSubtotal) => taxSubtotal.taxType)
            .indexOf(taxDetail.taxType)

          if (isExist == -1) {
            taxSubtotalOfEachTaxType.push({
              taxableAmount: taxDetail.taxableAmount!,
              taxAmount: taxDetail.taxAmount!,
              taxType: taxDetail.taxType,
            })
          } else {
            taxSubtotalOfEachTaxType[isExist].taxableAmount =
              UtilityService.convertToSpecifiedDecimalPlaces({
                amount: taxSubtotalOfEachTaxType[isExist].taxableAmount + taxDetail.taxableAmount!,
              })

            taxSubtotalOfEachTaxType[isExist].taxAmount =
              UtilityService.convertToSpecifiedDecimalPlaces({
                amount: taxSubtotalOfEachTaxType[isExist].taxAmount + taxDetail.taxAmount!,
              })
          }
        })

        // Handle exemption of each item if any
        if (item.taxExemption) {
          const isExist = taxSubtotalOfEachTaxType
            .map((taxSubtotal) => taxSubtotal.taxType)
            .indexOf(taxTypesService.getByCode('E')!.Code)

          if (isExist == -1) {
            // new in array, push
            taxSubtotalOfEachTaxType.push({
              taxableAmount: item.taxExemption.taxableAmount,
              taxAmount: item.taxExemption.taxAmount!,
              taxType: taxTypesService.getByCode('E')!.Code,
            })
          } else {
            // already exist, add amount
            taxSubtotalOfEachTaxType[isExist].taxableAmount =
              UtilityService.convertToSpecifiedDecimalPlaces({
                amount:
                  taxSubtotalOfEachTaxType[isExist].taxableAmount + item.taxExemption.taxableAmount,
              })
            taxSubtotalOfEachTaxType[isExist].taxAmount =
              UtilityService.convertToSpecifiedDecimalPlaces({
                amount: taxSubtotalOfEachTaxType[isExist].taxAmount + item.taxExemption.taxAmount!,
              })
          }
        }
      })

      return taxSubtotalOfEachTaxType
    }
  }

  public static calculateInvoiceLevelTax({
    lineItems,
    prePayment,
    invoiceLevelAllowanceCharge,
  }: {
    lineItems: LineItem[]
    prePayment?: Prepayment
    invoiceLevelAllowanceCharge?: InvoiceLevelAllowanceCharge
  }): {
    excludingTax: number
    includingTax: number
    payableAmount: number
    netAmount: number
    discountValue: number | undefined
    feeAmount: number | undefined
    payableRoundingAmount: number | undefined
  } {
    const totalNetAmount = UtilityService.convertToSpecifiedDecimalPlaces({
      amount: lineItems.reduce((sum, item) => {
        return (
          sum +
          item.unit.count * (item.unit.price + EInvoiceService.sumLineItemAllowanceCharges(item))
        )
      }, 0),
    })

    const lineItemsAllowanceCharges: LineItemAllowanceCharge[] = []
    for (const lineItem of lineItems) {
      if (lineItem.allowanceCharges) {
        lineItemsAllowanceCharges.push(...lineItem.allowanceCharges)
      }
    }

    const totalDiscountValues = UtilityService.convertToSpecifiedDecimalPlaces({
      amount:
        (invoiceLevelAllowanceCharge?.discount?.amount ?? 0) +
        (lineItemsAllowanceCharges?.reduce((sum, charge) => {
          return sum + (!charge.isCharge ? charge.amount! : 0)
        }, 0) ?? 0),
    })

    const totalFeeValues = UtilityService.convertToSpecifiedDecimalPlaces({
      amount:
        (invoiceLevelAllowanceCharge?.fee?.amount ?? 0) +
        (lineItemsAllowanceCharges?.reduce((sum, charge) => {
          return sum + (charge.isCharge ? charge.amount! : 0)
        }, 0) ?? 0),
    })

    logger.debug(
      {
        totalNetAmount: totalNetAmount,
        totalDiscountValues: totalDiscountValues,
        totalFeeValues: totalFeeValues,
      },
      'BEFORE FINAL CALCULATE details:'
    )

    // Note: totalNetAmount is totalNetAmount + 'invoice level discounts and charges'
    const totalExcludingTaxes = UtilityService.convertToSpecifiedDecimalPlaces({
      amount:
        totalNetAmount -
        (invoiceLevelAllowanceCharge?.discount?.amount ?? 0) +
        (invoiceLevelAllowanceCharge?.fee?.amount ?? 0),
    })

    const totalTaxAmount = UtilityService.convertToSpecifiedDecimalPlaces({
      amount: this.calculateInvoiceLevelLineItemTaxes(lineItems, 'TotalTaxAmount') as number,
    })

    const totalIncludingTaxes = UtilityService.convertToSpecifiedDecimalPlaces({
      amount: totalExcludingTaxes + totalTaxAmount,
    })

    // Rounding amount, eg. total payable before rounding = 10.31 round down to 10.30 - rounding amount = 0.01
    // Rounding Mechanism refer https://www.bnm.gov.my/misc/-/asset_publisher/2BOPbOBfILtL/content/about-the-rounding-mechanism
    const totalPayableAmountBeforeRounding = UtilityService.convertToSpecifiedDecimalPlaces({
      amount: totalIncludingTaxes - (prePayment?.amount ?? 0),
    })
    const payableRoundingAmount = undefined // Note: undefined for now since it optional field //UtilityService.roundingCurrencySens(
    //   totalPayableAmountBeforeRounding
    // )
    const totalPayableAmountAfterRounding = UtilityService.convertToSpecifiedDecimalPlaces({
      amount: totalPayableAmountBeforeRounding,
    }) //+ payableRoundingAmount

    logger.debug(
      {
        totalNetAmount: totalNetAmount,
        totalDiscountValues: totalDiscountValues,
        totalFeeValues: totalFeeValues,
        totalExcludingTaxes: totalExcludingTaxes,
        totalTaxAmount: totalTaxAmount,
        totalIncludingTaxes: totalIncludingTaxes,
        totalPayableAmountBeforeRounding: totalPayableAmountBeforeRounding,
        totalPayableAmountAfterRounding: totalPayableAmountAfterRounding,
        payableRoundingAmount: undefined, // payableRoundingAmount,
      },
      'calculateInvoiceLevelTax details:'
    )

    return {
      excludingTax: totalExcludingTaxes,
      includingTax: totalIncludingTaxes,
      payableAmount: totalPayableAmountAfterRounding,
      netAmount: totalNetAmount,
      discountValue: totalDiscountValues,
      feeAmount: totalFeeValues,
      payableRoundingAmount: payableRoundingAmount,
    }
  }

  public static async refreshMyInvoisCompanyAccessToken(company: Company): Promise<any> {
    // Check if existing token is still valid, if not, refresh new
    if (
      !(company.accessToken && company.tokenExpiresAt && DateTime.now() < company.tokenExpiresAt)
    ) {
      // Request new token from MyInvois as an intermediary system
      const tokenResponse = await EInvoiceService.LinkMyInvoisApi(
        company,
        company.clientId,
        company.clientSecret
      )

      // Update company with new token
      company.accessToken = tokenResponse.data.access_token
      company.tokenExpiresIn = tokenResponse.data.expires_in
      company.tokenExpiresAt = DateTime.now().plus({
        seconds: tokenResponse.data.expires_in,
      })
      company.scope = tokenResponse.data.scope
      await company.save()

      logger.info(tokenResponse.data, 'loginMyInvoisApi: Received new token')

      return tokenResponse
    }

    return
  }

  public static async documentSubmissionService({
    orders,
    user,
    company,
  }: {
    orders: Order[]
    user: User
    company: Company
  }): Promise<DocumentSubmission | void> {
    const documentSubmissionItems: DocumentSubmissionItem[] = []
    const readyPendingOrders = orders.filter((order) => order.status == 'Pending') // only send ready & not submitted to LHDN document orders

    if (readyPendingOrders.length === 0) return

    // Create EInvoiceV1 Object from Order row(s).
    const documents: EInvoiceV1[] = readyPendingOrders.map((order) => {
      return {
        invoiceCode: order.invoiceCode,
        invoiceDateTime: order.invoiceDateTime,
        supplier: order.supplier,
        buyer: order.buyer,
        invoiceLevelLineItemTaxes: order.invoiceLevelLineItemTaxes,
        lineItems: order.lineItems,
        legalMonetaryTotal: order.legalMonetaryTotal,
        additionalDocumentReference: order.additionalDocumentReference,
        deliveryDetails: order.deliveryDetails,
        foreignCurrency: order.foreignCurrency,
        billingPeriod: order.billingPeriod,
        payment: order.payment,
        prePayment: order.prePayment,
        billingReferenceNumber: order.billingReferenceNumber,
        invoiceLevelAllowanceCharge: order.invoiceLevelAllowanceCharge,
      }
    })

    for await (const document of documents) {
      //const dummyConsolidatedEInvoice = oldInvoiceDummy // For debug test only, oldInvoiceDummy is a valid invoice v1.

      const UBLFormatInvoice = EInvoiceService.transformToUBLFormat(document)
      // logger.debug(UBLFormatInvoice, 'URL FORMAT:')
      const minified = UtilityService.minifyJson(UBLFormatInvoice)
      const documentBase64 = UtilityService.toBase64(minified)
      const documentHash = await UtilityService.sha256(minified)

      documentSubmissionItems.push({
        format: 'JSON',
        documentBase64: documentBase64,
        documentHash: documentHash,

        documentDetails: document,
        code: document.invoiceCode,
      })
    }

    const refreshTokenResponse = await EInvoiceService.refreshMyInvoisCompanyAccessToken(company)
    // Rename and Exclude the some property.
    // Because some properties are custom name for our table and should not be submitted to MyInvois API
    const submissionDocuments = documentSubmissionItems.map((item) => ({
      format: item.format,
      document: item.documentBase64,
      documentHash: item.documentHash,
      codeNumber: item.code,
    }))

    try {
      const myInvoisResponse = await axios.post<DocumentSubmissionResponse>(
        `${env.get('MYINVOIS_API_SANDBOX_URL')}/documentsubmissions`,
        { documents: submissionDocuments },
        {
          headers: {
            'Authorization': refreshTokenResponse
              ? refreshTokenResponse.data.access_token
              : company.accessToken,
            'Content-Type': 'application/json',
          },
        }
      )

      const submissionResult = myInvoisResponse.data

      // console.log(submissionResult.rejectedDocuments[0]?.error)

      // Create document submission record
      const submission = await DocumentSubmission.create({
        submissionUid: submissionResult.submissionUid,
        userId: user.id,
        companyId: company.id,
        totalDocuments:
          submissionResult.acceptedDocuments.length + submissionResult.rejectedDocuments.length,
      })

      // Create accepted document records
      for await (const doc of submissionResult.acceptedDocuments) {
        const originalDoc = documents.find((d) => d.invoiceCode === doc.invoiceCodeNumber)

        // Update Order isSubmittedToLHDN to true
        const orderToUpdate = readyPendingOrders.filter(
          (order) => order.invoiceCode === doc.invoiceCodeNumber
        )[0]
        await orderToUpdate.merge({ isSubmittedToLhdn: true }).save()

        // Create SubmittedDocument Row
        if (originalDoc) {
          await SubmittedDocument.create({
            code: doc.invoiceCodeNumber,
            orderId: orderToUpdate.id,
            companyId: company.id,
            userId: user.id,
            documentDetails: EInvoiceService.transformToUBLFormat(originalDoc),
            documentSubmissionId: submission.id,
            uuid: doc.uuid,
            status: 'Submitted',
          })
        }
      }

      // Create rejected document records
      for await (const doc of submissionResult.rejectedDocuments) {
        const originalDoc = documents.find((d) => d.invoiceCode === doc.invoiceCodeNumber)

        // Update Order isSubmittedToLHDN to false
        const orderToUpdate = readyPendingOrders.filter(
          (order) => order.invoiceCode === doc.invoiceCodeNumber
        )[0]
        await orderToUpdate.merge({ isSubmittedToLhdn: false }).save()

        // Create SubmittedDocument Row
        if (originalDoc) {
          await SubmittedDocument.create({
            code: doc.invoiceCodeNumber,
            orderId: orderToUpdate.id,
            companyId: company.id,
            userId: user.id,
            documentDetails: EInvoiceService.transformToUBLFormat(originalDoc),
            documentSubmissionId: submission.id,
            status: 'Invalid',
            failReason: doc.error?.message,
            failDetails: doc.error.details,
          })
        }
      }

      // TODO: call frontend webhook update order invoice process status

      return submission
    } catch (err) {
      logger.error(err, 'documentSubmissionService')
      console.log(err.response.status)
      console.dir(err.response.data, { depth: null })
      return err
    }
  }

  // TODO: Add signature field as Invoice v1.0 also forced to use signature now.
  // TODO: Pending Currency Exchange rate source of true (currently dependent from user input) for invoice.currencyExchangeRate
  public static transformToUBLFormat(invoice: EInvoiceV1) {
    const listVersionID = '1.0'
    const invoiceTypeCode = '01' // 01 is Invoice, Refer https://sdk.myinvois.hasil.gov.my/codes/e-invoice-types/
    const defaultCurrencyCode = 'MYR'
    const defaultUnitCode = 'C62' // represents one
    const taxScheme = {
      _: 'OTH',
      schemeID: 'UN/ECE 5153',
      schemeAgencyID: '6',
    }

    const countryIdentificationCode = {
      listID: 'ISO3166-1',
      listAgencyID: '6',
    }

    const schemeAgencyName = 'CertEX'

    return {
      _D: 'urn:oasis:names:specification:ubl:schema:xsd:Invoice-2',
      _A: 'urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2',
      _B: 'urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2',
      Invoice: [
        {
          ID: [
            {
              _: invoice.invoiceCode,
            },
          ],
          IssueDate: [
            {
              _: DateTime.isDateTime(invoice.invoiceDateTime.date)
                ? invoice.invoiceDateTime.date.toUTC().toFormat('yyyy-MM-dd')
                : DateTime.fromISO(invoice.invoiceDateTime.date).toUTC().toFormat('yyyy-MM-dd'),
            },
          ],
          IssueTime: [
            {
              _: DateTime.isDateTime(invoice.invoiceDateTime.time)
                ? invoice.invoiceDateTime.time.toUTC().toFormat("HH:mm:ss'Z'")
                : DateTime.fromISO(invoice.invoiceDateTime.time).toUTC().toFormat("HH:mm:ss'Z'"),
            },
          ],

          InvoiceTypeCode: [
            {
              _: invoiceTypeCode,
              listVersionID: listVersionID,
            },
          ],

          DocumentCurrencyCode: [
            {
              _: invoice.foreignCurrency
                ? invoice.foreignCurrency.currencyCode
                : defaultCurrencyCode,
            },
          ],

          // Optional
          TaxCurrencyCode: [
            {
              _: defaultCurrencyCode,
            },
          ],

          // Mandatory where applicable
          ...(invoice.foreignCurrency
            ? {
                TaxExchangeRate: [
                  {
                    SourceCurrencyCode: [
                      {
                        _: invoice.foreignCurrency.currencyCode,
                      },
                    ],
                    TargetCurrencyCode: [
                      {
                        _: defaultCurrencyCode,
                      },
                    ],
                    CalculationRate: [
                      {
                        _: invoice.foreignCurrency.currencyExchangeRate,
                      },
                    ],
                  },
                ],
              }
            : {}),

          // Optional
          ...(invoice.billingPeriod
            ? {
                InvoicePeriod: [
                  {
                    StartDate: [
                      {
                        _: invoice.billingPeriod.startDate.toISODate(),
                      },
                    ],
                    EndDate: [
                      {
                        _: invoice.billingPeriod.endDate.toISODate(),
                      },
                    ],
                    Description: [
                      {
                        _: invoice.billingPeriod.frequency,
                      },
                    ],
                  },
                ],
              }
            : {}),

          // Optional
          ...(invoice.payment || invoice.supplier.bankAccount
            ? {
                PaymentMeans: [
                  {
                    ...(invoice.payment
                      ? {
                          PaymentMeansCode: [
                            {
                              _: invoice.payment.mode,
                            },
                          ],
                        }
                      : {}),

                    ...(invoice.supplier.bankAccount
                      ? {
                          PayeeFinancialAccount: [
                            {
                              ID: [
                                {
                                  _: invoice.supplier.bankAccount,
                                },
                              ],
                            },
                          ],
                        }
                      : {}),
                  },
                ],
              }
            : {}),

          // Optional
          ...(invoice.payment?.terms && {
            PaymentTerms: [
              {
                Note: [
                  {
                    _: invoice.payment?.terms,
                  },
                ],
              },
            ],
          }),

          // Optional
          ...(invoice.prePayment && {
            PrepaidPayment: [
              {
                ID: [
                  {
                    _: invoice.prePayment.referenceNumber,
                  },
                ],
                PaidAmount: [
                  {
                    _: invoice.prePayment.amount,
                    currencyID: invoice.foreignCurrency?.currencyCode ?? defaultCurrencyCode,
                  },
                ],
                PaidDate: [
                  {
                    _: invoice.prePayment.date.toISODate(),
                  },
                ],
                PaidTime: [
                  {
                    _: invoice.prePayment.time.toISOTime(),
                  },
                ],
              },
            ],
          }),

          // Optional
          ...(invoice.billingReferenceNumber
            ? {
                BillingReference: [
                  {
                    AdditionalDocumentReference: [
                      {
                        ID: [
                          {
                            _: invoice.billingReferenceNumber,
                          },
                        ],
                      },
                    ],
                  },
                ],
              }
            : {}),

          LegalMonetaryTotal: [
            {
              TaxExclusiveAmount: [
                {
                  _: invoice.legalMonetaryTotal.excludingTax,
                  currencyID: invoice.foreignCurrency?.currencyCode ?? defaultCurrencyCode,
                },
              ],
              TaxInclusiveAmount: [
                {
                  _: invoice.legalMonetaryTotal.includingTax,
                  currencyID: invoice.foreignCurrency?.currencyCode ?? defaultCurrencyCode,
                },
              ],
              PayableAmount: [
                {
                  _: invoice.legalMonetaryTotal.payableAmount,
                  currencyID: invoice.foreignCurrency?.currencyCode ?? defaultCurrencyCode,
                },
              ],

              // Optional
              ...(invoice.legalMonetaryTotal.netAmount
                ? {
                    LineExtensionAmount: [
                      {
                        _: invoice.legalMonetaryTotal.netAmount,
                        currencyID: invoice.foreignCurrency?.currencyCode ?? defaultCurrencyCode,
                      },
                    ],
                  }
                : {}),

              // Optional
              ...(invoice.legalMonetaryTotal.discountValue
                ? {
                    AllowanceTotalAmount: [
                      {
                        _: invoice.legalMonetaryTotal.discountValue,
                        currencyID: invoice.foreignCurrency?.currencyCode ?? defaultCurrencyCode,
                      },
                    ],
                  }
                : {}),

              // Optional
              ...(invoice.legalMonetaryTotal.feeAmount
                ? {
                    ChargeTotalAmount: [
                      {
                        _: invoice.legalMonetaryTotal.feeAmount,
                        currencyID: invoice.foreignCurrency?.currencyCode ?? defaultCurrencyCode,
                      },
                    ],
                  }
                : {}),

              // Optional
              ...(invoice.legalMonetaryTotal.payableRoundingAmount
                ? {
                    PayableRoundingAmount: [
                      {
                        _: invoice.legalMonetaryTotal.payableRoundingAmount,
                        currencyID: invoice.foreignCurrency?.currencyCode ?? defaultCurrencyCode,
                      },
                    ],
                  }
                : {}),
            },
          ],

          TaxTotal: [
            {
              TaxAmount: [
                {
                  _: invoice.invoiceLevelLineItemTaxes.totalTaxAmount,
                  currencyID: invoice.foreignCurrency?.currencyCode ?? defaultCurrencyCode,
                },
              ],
              TaxSubtotal: invoice.invoiceLevelLineItemTaxes.taxSubtotals.map((taxSubtotal) => {
                return {
                  TaxableAmount: [
                    {
                      _: taxSubtotal.taxableAmount,
                      currencyID: invoice.foreignCurrency?.currencyCode ?? defaultCurrencyCode,
                    },
                  ],
                  TaxAmount: [
                    {
                      _: taxSubtotal.taxAmount,
                      currencyID: invoice.foreignCurrency?.currencyCode ?? defaultCurrencyCode,
                    },
                  ],
                  TaxCategory: [
                    {
                      ID: [
                        {
                          _: taxSubtotal.taxType,
                        },
                      ],

                      TaxScheme: [
                        {
                          ID: [taxScheme],
                        },
                      ],
                    },
                  ],
                }
              }),
            },
          ],

          // Optional
          ...(invoice.invoiceLevelAllowanceCharge?.discount
            ? {
                AllowanceCharge: [
                  {
                    ChargeIndicator: [
                      {
                        _: false,
                      },
                    ],
                    AllowanceChargeReason: [
                      {
                        _: invoice.invoiceLevelAllowanceCharge.discount.reason,
                      },
                    ],
                    Amount: [
                      {
                        _: invoice.invoiceLevelAllowanceCharge.discount.amount,
                        currencyID: invoice.foreignCurrency?.currencyCode ?? defaultCurrencyCode,
                      },
                    ],
                  },
                ],
              }
            : {}),

          ...(invoice.invoiceLevelAllowanceCharge?.fee
            ? {
                AllowanceCharge: [
                  {
                    ChargeIndicator: [
                      {
                        _: true,
                      },
                    ],
                    AllowanceChargeReason: [
                      {
                        _: invoice.invoiceLevelAllowanceCharge.fee.reason,
                      },
                    ],
                    Amount: [
                      {
                        _: invoice.invoiceLevelAllowanceCharge.fee.amount,
                        currencyID: invoice.foreignCurrency?.currencyCode ?? defaultCurrencyCode,
                      },
                    ],
                  },
                ],
              }
            : {}),

          // Optional
          ...(invoice.deliveryDetails
            ? {
                Delivery: [
                  {
                    DeliveryParty: [
                      {
                        // Optional
                        ...(invoice.deliveryDetails.recipientName
                          ? {
                              PartyLegalEntity: [
                                {
                                  RegistrationName: [
                                    {
                                      _: invoice.deliveryDetails.recipientName,
                                    },
                                  ],
                                },
                              ],
                            }
                          : {}),
                        // Optional
                        ...(invoice.deliveryDetails.recipientAddress
                          ? {
                              PostalAddress: [
                                {
                                  CityName: [
                                    {
                                      _: invoice.deliveryDetails.recipientAddress.cityName,
                                    },
                                  ],
                                  PostalZone: [
                                    {
                                      _: invoice.deliveryDetails.recipientAddress.postalZone,
                                    },
                                  ],
                                  CountrySubentityCode: [
                                    {
                                      _: invoice.deliveryDetails.recipientAddress.state,
                                    },
                                  ],
                                  AddressLine: [
                                    {
                                      Line: [
                                        {
                                          _: invoice.deliveryDetails.recipientAddress.addressLine0,
                                        },
                                      ],
                                    },
                                    ...(invoice.deliveryDetails.recipientAddress.addressLine1
                                      ? [
                                          {
                                            Line: [
                                              {
                                                _: invoice.deliveryDetails.recipientAddress
                                                  .addressLine1,
                                              },
                                            ],
                                          },
                                        ]
                                      : []),
                                    ...(invoice.deliveryDetails.recipientAddress.addressLine2
                                      ? [
                                          {
                                            Line: [
                                              {
                                                _: invoice.deliveryDetails.recipientAddress
                                                  .addressLine2,
                                              },
                                            ],
                                          },
                                        ]
                                      : []),
                                  ],
                                  Country: [
                                    {
                                      IdentificationCode: [
                                        {
                                          _: invoice.deliveryDetails.recipientAddress.country,
                                          ...countryIdentificationCode,
                                        },
                                      ],
                                    },
                                  ],
                                },
                              ],
                            }
                          : {}),

                        // Optional
                        ...(invoice.deliveryDetails.recipientTin ||
                        invoice.deliveryDetails.recipientRegistration
                          ? {
                              PartyIdentification: [
                                ...(invoice.deliveryDetails.recipientTin
                                  ? [
                                      {
                                        ID: [
                                          {
                                            _: invoice.deliveryDetails.recipientTin,
                                            schemeID: 'TIN',
                                          },
                                        ],
                                      },
                                    ]
                                  : []),
                                ...(invoice.deliveryDetails.recipientRegistration
                                  ? [
                                      {
                                        ID: [
                                          {
                                            _:
                                              invoice.deliveryDetails.recipientRegistration.type +
                                              ':' +
                                              invoice.deliveryDetails.recipientRegistration.number,
                                            schemeID:
                                              invoice.deliveryDetails.recipientRegistration.type,
                                          },
                                        ],
                                      },
                                    ]
                                  : []),
                              ],
                            }
                          : {}),
                      },
                    ],
                    ...(invoice.deliveryDetails.shipmentDetails
                      ? {
                          Shipment: [
                            {
                              ID: [
                                {
                                  _: invoice.deliveryDetails.shipmentDetails
                                    .shipperInternalTrackingId,
                                },
                              ],
                              FreightAllowanceCharge: [
                                {
                                  ChargeIndicator: [
                                    {
                                      _: true, // Hardcoded to true, because shipment fee are always assumed as a charge
                                    },
                                  ],
                                  AllowanceChargeReason: [
                                    {
                                      _: invoice.deliveryDetails.shipmentDetails
                                        .allowanceChargeReason,
                                    },
                                  ],
                                  Amount: [
                                    {
                                      _: invoice.deliveryDetails.shipmentDetails.amount,
                                      currencyID:
                                        invoice.foreignCurrency?.currencyCode ??
                                        defaultCurrencyCode,
                                    },
                                  ],
                                },
                              ],
                            },
                          ],
                        }
                      : {}),
                  },
                ],
              }
            : {}),

          // Mandatory where applicable, need to follow the order for each different reference doc type as according to documentation.
          // CustomImportForm must be at element 1, FreeTradeAgreement must be at element 2, K2 must be at element 3, CIF must be at element 4
          ...(invoice.additionalDocumentReference && invoice.additionalDocumentReference.length > 0
            ? {
                AdditionalDocumentReference: invoice.additionalDocumentReference.map(
                  (reference) => {
                    let documentDetails = [{}, {}, {}, {}]

                    switch (reference.type) {
                      case AdditionalDocumentReferenceType.CUSTOM_IMPORT_FORM:
                        documentDetails[0] = {
                          ID: [
                            {
                              _: reference.id,
                            },
                          ],
                          DocumentType: [
                            {
                              _: reference.type,
                            },
                          ],
                        }
                        break
                      case AdditionalDocumentReferenceType.FREE_TRADE_AGREEMENT:
                        documentDetails[1] = {
                          ID: [
                            {
                              _: reference.id,
                            },
                          ],
                          DocumentType: [
                            {
                              _: reference.type,
                            },
                          ],
                        }
                        break
                      case AdditionalDocumentReferenceType.K2:
                        documentDetails[2] = {
                          ID: [
                            {
                              _: reference.id,
                            },
                          ],
                          DocumentType: [
                            {
                              _: reference.type,
                            },
                          ],
                        }
                        break
                      case AdditionalDocumentReferenceType.CIF:
                        documentDetails[3] = {
                          ID: [
                            {
                              _: reference.id,
                            },
                          ],
                        }
                        break
                    }

                    return documentDetails
                  }
                ),
              }
            : {}),

          AccountingSupplierParty: [
            {
              // Optional
              ...(invoice.supplier.exporterCertifiedNumber
                ? {
                    AdditionalAccountID: [
                      {
                        _: invoice.supplier.exporterCertifiedNumber,
                        schemeAgencyName: schemeAgencyName,
                      },
                    ],
                  }
                : {}),

              Party: [
                {
                  PartyLegalEntity: [
                    {
                      RegistrationName: [
                        {
                          _: invoice.supplier.name,
                        },
                      ],
                    },
                  ],
                  PartyIdentification: [
                    {
                      ID: [
                        {
                          _: invoice.supplier.tin,
                          schemeID: 'TIN',
                        },
                      ],
                    },
                    {
                      ID: [
                        {
                          _: invoice.supplier.registrationNumber,
                          schemeID: invoice.supplier.registrationType,
                        },
                      ],
                    },

                    // Mandatory for SST-registrant
                    {
                      ID: [
                        {
                          _: invoice.supplier.sstRegistrationNumber ?? 'NA',
                          schemeID: 'SST',
                        },
                      ],
                    },

                    // Mandatory for tourism tax registrant
                    {
                      ID: [
                        {
                          _: invoice.supplier.tourismTaxRegistrationNumber ?? 'NA',
                          schemeID: 'TTX',
                        },
                      ],
                    },
                  ],

                  IndustryClassificationCode: [
                    {
                      _: invoice.supplier.msic,
                      name: invoice.supplier.businessActivityDescription,
                    },
                  ],

                  PostalAddress: [
                    {
                      CityName: [
                        {
                          _: invoice.supplier.address.cityName,
                        },
                      ],
                      PostalZone: [
                        {
                          _: invoice.supplier.address.postalZone,
                        },
                      ],
                      CountrySubentityCode: [
                        {
                          _: invoice.supplier.address.state,
                        },
                      ],
                      AddressLine: [
                        {
                          Line: [
                            {
                              _: invoice.supplier.address.addressLine0,
                            },
                          ],
                        },
                        ...(invoice.supplier.address.addressLine1
                          ? [
                              {
                                Line: [
                                  {
                                    _: invoice.supplier.address.addressLine1,
                                  },
                                ],
                              },
                            ]
                          : []),
                        ...(invoice.supplier.address.addressLine2
                          ? [
                              {
                                Line: [
                                  {
                                    _: invoice.supplier.address.addressLine2,
                                  },
                                ],
                              },
                            ]
                          : []),
                      ],
                      Country: [
                        {
                          IdentificationCode: [
                            {
                              _: invoice.supplier.address.country,
                              ...countryIdentificationCode,
                            },
                          ],
                        },
                      ],
                    },
                  ],

                  Contact: [
                    {
                      Telephone: [
                        {
                          _: UtilityService.removeSpaces(invoice.supplier.contactNumber),
                        },
                      ],

                      // Optional
                      ...(invoice.supplier.email
                        ? {
                            ElectronicMail: [
                              {
                                _: invoice.supplier.email,
                              },
                            ],
                          }
                        : {}),
                    },
                  ],
                },
              ],
            },
          ],

          AccountingCustomerParty: [
            {
              Party: [
                {
                  PartyLegalEntity: [
                    {
                      RegistrationName: [
                        {
                          _: invoice.buyer.name,
                        },
                      ],
                    },
                  ],

                  PartyIdentification: [
                    {
                      ID: [
                        {
                          _: invoice.buyer.tin,
                          schemeID: 'TIN',
                        },
                      ],
                    },
                    {
                      ID: [
                        {
                          _: invoice.buyer.registrationNumber ?? 'NA',
                          schemeID: invoice.buyer.registrationType ?? RegistrationType.BRN,
                        },
                      ],
                    },

                    // Mandatory for SST-registrant
                    {
                      ID: [
                        {
                          _: invoice.buyer.sstRegistrationNumber ?? 'NA',
                          schemeID: 'SST',
                        },
                      ],
                    },
                    // Buyer does not have Tourism Tax field.
                  ],

                  PostalAddress: [
                    {
                      CityName: [
                        {
                          _: invoice.buyer.address?.cityName ?? '',
                        },
                      ],
                      PostalZone: [
                        {
                          _: invoice.buyer.address?.postalZone ?? '',
                        },
                      ],
                      CountrySubentityCode: [
                        {
                          _: invoice.buyer.address?.state ?? '',
                        },
                      ],
                      AddressLine: [
                        {
                          Line: [
                            {
                              _: invoice.buyer.address?.addressLine0 ?? 'NA',
                            },
                          ],
                        },
                        ...(invoice.buyer.address?.addressLine1
                          ? [
                              {
                                Line: [
                                  {
                                    _: invoice.buyer.address.addressLine1 ?? 'NA',
                                  },
                                ],
                              },
                            ]
                          : []),
                        ...(invoice.buyer.address?.addressLine2
                          ? [
                              {
                                Line: [
                                  {
                                    _: invoice.buyer.address.addressLine2 ?? 'NA',
                                  },
                                ],
                              },
                            ]
                          : []),
                      ],
                      Country: [
                        {
                          IdentificationCode: [
                            {
                              _: invoice.buyer.address?.country ?? '',
                              ...countryIdentificationCode,
                            },
                          ],
                        },
                      ],
                    },
                  ],

                  Contact: [
                    {
                      Telephone: [
                        {
                          _: invoice.buyer.contactNumber
                            ? UtilityService.removeSpaces(invoice.buyer.contactNumber)
                            : 'NA',
                        },
                      ],

                      // Optional
                      ...(invoice.buyer.email
                        ? {
                            ElectronicMail: [
                              {
                                _: invoice.buyer.email,
                              },
                            ],
                          }
                        : {}),
                    },
                  ],
                },
              ],
            },
          ],

          InvoiceLine: invoice.lineItems.map((lineItem) => {
            return {
              ID: [
                {
                  _: lineItem.id,
                },
              ],
              Price: [
                {
                  PriceAmount: [
                    {
                      _: lineItem.unit.price,
                      currencyID: invoice.foreignCurrency?.currencyCode ?? defaultCurrencyCode,
                    },
                  ],
                },
              ],

              TaxTotal: [
                {
                  TaxAmount: [
                    {
                      _: lineItem.taxAmount,
                      currencyID: invoice.foreignCurrency?.currencyCode ?? defaultCurrencyCode,
                    },
                  ],
                  TaxSubtotal: lineItem.taxDetails
                    .map((taxDetail: LineItemTaxDetail) => {
                      return {
                        // If fixed rate (ratePerUnit) is provided, either percent or fixed rate.
                        ...(taxDetail.taxRate.ratePerUnit
                          ? {
                              PerUnitAmount: [
                                {
                                  _: taxDetail.taxRate.ratePerUnit,
                                  currencyID:
                                    invoice.foreignCurrency?.currencyCode ?? defaultCurrencyCode,
                                },
                              ],
                              BaseUnitMeasure: [
                                {
                                  _: lineItem.unit.count,
                                  unitCode: lineItem.unit.code ?? defaultUnitCode,
                                },
                              ],
                              TaxAmount: [
                                {
                                  _: taxDetail.taxAmount!,
                                  currencyID:
                                    invoice.foreignCurrency?.currencyCode ?? defaultCurrencyCode,
                                },
                              ],
                            }
                          : {
                              Percent: [
                                {
                                  _: taxDetail.taxRate.percentage!,
                                },
                              ],
                              TaxAmount: [
                                {
                                  _: taxDetail.taxAmount!,
                                  currencyID:
                                    invoice.foreignCurrency?.currencyCode ?? defaultCurrencyCode,
                                },
                              ],
                            }),

                        TaxableAmount: [
                          {
                            _: taxDetail.taxableAmount!,
                            currencyID:
                              invoice.foreignCurrency?.currencyCode ?? defaultCurrencyCode,
                          },
                        ],

                        TaxCategory: [
                          {
                            ID: [
                              {
                                _: taxDetail.taxType,
                              },
                            ],

                            TaxScheme: [
                              {
                                ID: [taxScheme],
                              },
                            ],
                          },
                        ],
                      }
                    })
                    .concat(
                      lineItem.taxExemption
                        ? [
                            {
                              TaxAmount: [
                                {
                                  _: lineItem.taxExemption!.taxAmount!,
                                  currencyID:
                                    invoice.foreignCurrency?.currencyCode ?? defaultCurrencyCode,
                                },
                              ],

                              TaxableAmount: [
                                {
                                  _: lineItem.taxExemption!.taxableAmount!,
                                  currencyID:
                                    invoice.foreignCurrency?.currencyCode ?? defaultCurrencyCode,
                                },
                              ],

                              TaxCategory: [
                                {
                                  ID: [
                                    {
                                      _: taxTypesService.getByCode('E')!.Code,
                                    },
                                  ],

                                  // Note: Added ts ignore because normal non-exemption tax types don't have a reason field
                                  // @ts-ignore
                                  TaxExemptionReason: [
                                    {
                                      _: lineItem.taxExemption!.reason,
                                    },
                                  ],

                                  TaxScheme: [
                                    {
                                      ID: [taxScheme],
                                    },
                                  ],
                                },
                              ],
                            },
                          ]
                        : []
                    ),
                },
              ],

              Item: [
                {
                  CommodityClassification: [
                    // Optional [Tarriff code will be only applicable to goods]
                    ...(lineItem.tarriffCode
                      ? [
                          {
                            ItemClassificationCode: [
                              {
                                _: lineItem.tarriffCode,
                                listID: 'PTC',
                              },
                            ],
                          },
                        ]
                      : []),
                    ...lineItem.classifications.map((classification) => {
                      return {
                        ItemClassificationCode: [
                          {
                            _: classification,
                            listID: 'CLASS',
                          },
                        ],
                      }
                    }),
                  ],
                  Description: [
                    {
                      _: lineItem.description,
                    },
                  ],

                  // Optional
                  OriginCountry: [
                    {
                      IdentificationCode: [
                        {
                          _: lineItem.originCountry,
                        },
                      ],
                    },
                  ],
                },
              ],

              ItemPriceExtension: [
                {
                  Amount: [
                    {
                      _: lineItem.unit.count * lineItem.unit.price,
                      currencyID: invoice.foreignCurrency?.currencyCode ?? defaultCurrencyCode,
                    },
                  ],
                },
              ],

              LineExtensionAmount: [
                {
                  _:
                    lineItem.unit.count *
                    (lineItem.unit.price + EInvoiceService.sumLineItemAllowanceCharges(lineItem)),
                  currencyID: invoice.foreignCurrency?.currencyCode ?? defaultCurrencyCode,
                },
              ],

              // Optional
              InvoicedQuantity: [
                {
                  _: lineItem.unit.count,
                  unitCode: lineItem.unit.code ?? defaultUnitCode,
                },
              ],

              // Optional
              ...(lineItem.allowanceCharges && lineItem.allowanceCharges.length > 0
                ? {
                    AllowanceCharge: lineItem.allowanceCharges.map((allowanceCharge) => {
                      return {
                        ChargeIndicator: [
                          {
                            _: allowanceCharge.isCharge,
                          },
                        ],
                        AllowanceChargeReason: [
                          {
                            _: allowanceCharge.reason,
                          },
                        ],
                        MultiplierFactorNumeric: [
                          {
                            _: allowanceCharge.rate
                              ? allowanceCharge.rate
                              : allowanceCharge.amount! / lineItem.unit.price,
                          },
                        ],
                        Amount: [
                          {
                            _: allowanceCharge.amount,
                            currencyID:
                              invoice.foreignCurrency?.currencyCode ?? defaultCurrencyCode,
                          },
                        ],
                      }
                    }),
                  }
                : []),
            }
          }),
        },
      ],
    }
  }

  public static async LinkMyInvoisApi(
    company: Company,
    clientId: string,
    clientSecret: string
  ): Promise<AxiosResponse<any, any>> {
    try {
      const response = await axios.post(
        `${MYINVOIS_IDENTITY_URL}/connect/token`,
        {
          client_id: clientId,
          client_secret: clientSecret,
          grant_type: 'client_credentials',
          scope: 'InvoicingAPI',
        },
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'onbehalfof':
              company.tinCode && company.registrationNumber && company.registrationType === 'BRN'
                ? `${company.tinCode}:${company.registrationNumber}`
                : company.tinCode,
            // This is the company's (that is using our E-Invoice system) tin code
            // C25845632020 (in case of taxpayer identified by TIN only), or IG12345678912:************ (in case of taxpayer identified by TIN and has an ROB number)
          },
        }
      )
      return response
    } catch (error) {
      throw error
    }
  }

  public static async cancelSubmittedDocument(
    documentUuid: string,
    company: Company,
    cancelReason: string
  ): Promise<any> {
    const refreshTokenResponse = await EInvoiceService.refreshMyInvoisCompanyAccessToken(company)
    const response = await axios.put(
      `${MYINVOIS_API_URL}/documents/state/${documentUuid}/state`,
      {
        status: 'cancelled',
        reason: cancelReason,
      },
      {
        headers: {
          Authorization: `Bearer ${refreshTokenResponse ? refreshTokenResponse.data.access_token : company.accessToken}`,
        },
      }
    )

    return response
  }

  public static async validateTin(
    tinCode: string,
    accessToken: string,
    registrationType: string,
    registrationNumber: string
  ): Promise<void> {
    await axios.get(
      `${MYINVOIS_API_URL}/taxpayer/validate/${tinCode}?idType=${registrationType}&idValue=${registrationNumber}`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      }
    )
  }

  public static async retrieveDocumentDetails(
    uuid: string,
    company: Company
  ): Promise<AxiosResponse<any, any>> {
    try {
      const refreshTokenResponse = await EInvoiceService.refreshMyInvoisCompanyAccessToken(company)
      const response = await axios.get(`${MYINVOIS_API_URL}/documents/${uuid}/details`, {
        headers: {
          'Authorization': refreshTokenResponse
            ? refreshTokenResponse.data.access_token
            : company.accessToken,
          'Content-Type': 'application/json',
        },
      })
      return response.data
    } catch (error) {
      throw error
    }
  }
}

export class CountryCodeService {
  private readonly countryCodes: CountryCode[] = [
    {
      Code: 'ABW',
      Country: 'ARUBA',
    },
    {
      Code: 'AFG',
      Country: 'AFGHANISTAN',
    },
    {
      Code: 'AGO',
      Country: 'ANGOLA',
    },
    {
      Code: 'AIA',
      Country: 'ANGUILLA',
    },
    {
      Code: 'ALA',
      Country: 'ALAND ISLANDS',
    },
    {
      Code: 'ALB',
      Country: 'ALBANIA',
    },
    {
      Code: 'AND',
      Country: 'ANDORA',
    },
    {
      Code: 'ANT',
      Country: 'NETHERLANDS ANTILLE',
    },
    {
      Code: 'ARE',
      Country: 'UNITED ARAB EMIRATES',
    },
    {
      Code: 'ARG',
      Country: 'ARGENTINA',
    },
    {
      Code: 'ARM',
      Country: 'ARMENIA',
    },
    {
      Code: 'ASM',
      Country: 'AMERICAN SAMOA',
    },
    {
      Code: 'ATA',
      Country: 'ANTARCTICA',
    },
    {
      Code: 'ATF',
      Country: 'FRENCH SOUTHERN TERRITORIES',
    },
    {
      Code: 'ATG',
      Country: 'ANTIGUA AND BARBUDA',
    },
    {
      Code: 'AUS',
      Country: 'AUSTRALIA',
    },
    {
      Code: 'AUT',
      Country: 'AUSTRIA',
    },
    {
      Code: 'AZE',
      Country: 'AZERBAIDJAN',
    },
    {
      Code: 'BDI',
      Country: 'BURUNDI',
    },
    {
      Code: 'BEL',
      Country: 'BELGIUM',
    },
    {
      Code: 'BEN',
      Country: 'BENIN',
    },
    {
      Code: 'BES',
      Country: 'BONAIRE, SINT EUSTATIUS AND SABA',
    },
    {
      Code: 'BFA',
      Country: 'BURKINA FASO',
    },
    {
      Code: 'BGD',
      Country: 'BANGLADESH',
    },
    {
      Code: 'BGR',
      Country: 'BULGARIA',
    },
    {
      Code: 'BHR',
      Country: 'BAHRAIN',
    },
    {
      Code: 'BHS',
      Country: 'BAHAMAS',
    },
    {
      Code: 'BIH',
      Country: 'BOSNIA AND HERZEGOVINA',
    },
    {
      Code: 'BLM',
      Country: 'SAINT BARTHELEMY',
    },
    {
      Code: 'BLR',
      Country: 'BELARUS',
    },
    {
      Code: 'BLZ',
      Country: 'BELIZE',
    },
    {
      Code: 'BMU',
      Country: 'BERMUDA',
    },
    {
      Code: 'BOL',
      Country: 'BOLIVIA',
    },
    {
      Code: 'BRA',
      Country: 'BRAZIL',
    },
    {
      Code: 'BRB',
      Country: 'BARBADOS',
    },
    {
      Code: 'BRN',
      Country: 'BRUNEI DARUSSALAM',
    },
    {
      Code: 'BTN',
      Country: 'BHUTAN',
    },
    {
      Code: 'BVT',
      Country: 'BOUVET ISLAND',
    },
    {
      Code: 'BWA',
      Country: 'BOTSWANA',
    },
    {
      Code: 'CAF',
      Country: 'CENTRAL AFRICAN REPUBLIC',
    },
    {
      Code: 'CAN',
      Country: 'CANADA',
    },
    {
      Code: 'CCK',
      Country: 'COCOS ISLAND',
    },
    {
      Code: 'CHE',
      Country: 'SWITZERLAND',
    },
    {
      Code: 'CHL',
      Country: 'CHILE',
    },
    {
      Code: 'CHN',
      Country: 'CHINA',
    },
    {
      Code: 'CIV',
      Country: "COTE D'IVOIRE",
    },
    {
      Code: 'CMR',
      Country: 'CAMEROON',
    },
    {
      Code: 'COD',
      Country: 'CONGO, THE DEMOCRATIC REPUBLIC',
    },
    {
      Code: 'COG',
      Country: 'CONGO',
    },
    {
      Code: 'COK',
      Country: 'COOK ISLANDS ',
    },
    {
      Code: 'COL',
      Country: 'COLOMBIA',
    },
    {
      Code: 'COM',
      Country: 'COMOROS',
    },
    {
      Code: 'CPV',
      Country: 'CAPE VERDE',
    },
    {
      Code: 'CRI',
      Country: 'COSTA RICA',
    },
    {
      Code: 'CUB',
      Country: 'CUBA',
    },
    {
      Code: 'CUW',
      Country: 'CURACAO',
    },
    {
      Code: 'CXR',
      Country: 'CHRISTMAS ISLANDS',
    },
    {
      Code: 'CYM',
      Country: 'CAYMAN ISLANDS',
    },
    {
      Code: 'CYP',
      Country: 'CYPRUS',
    },
    {
      Code: 'CZE',
      Country: 'CZECH REPUBLIC',
    },
    {
      Code: 'DEU',
      Country: 'GERMANY',
    },
    {
      Code: 'DJI',
      Country: 'DJIBOUTI',
    },
    {
      Code: 'DMA',
      Country: 'DOMINICA',
    },
    {
      Code: 'DNK',
      Country: 'DENMARK',
    },
    {
      Code: 'DOM',
      Country: 'DOMINICAN REPUBLIC',
    },
    {
      Code: 'DZA',
      Country: 'ALGERIA',
    },
    {
      Code: 'ECU',
      Country: 'ECUADOR',
    },
    {
      Code: 'EGY',
      Country: 'EGYPT',
    },
    {
      Code: 'ERI',
      Country: 'ERITREA',
    },
    {
      Code: 'ESH',
      Country: 'WESTERN SAHARA',
    },
    {
      Code: 'ESP',
      Country: 'SPAIN',
    },
    {
      Code: 'EST',
      Country: 'ESTONIA',
    },
    {
      Code: 'ETH',
      Country: 'ETHIOPIA',
    },
    {
      Code: 'FIN',
      Country: 'FINLAND',
    },
    {
      Code: 'FJI',
      Country: 'FIJI',
    },
    {
      Code: 'FLK',
      Country: 'FALKLAND ISLANDS (MALVINAS)',
    },
    {
      Code: 'FRA',
      Country: 'FRANCE',
    },
    {
      Code: 'FRO',
      Country: 'FAEROE ISLANDS',
    },
    {
      Code: 'FSM',
      Country: 'MICRONESIA, FEDERATED STATES OF',
    },
    {
      Code: 'GAB',
      Country: 'GABON',
    },
    {
      Code: 'GBR',
      Country: 'UNITED KINGDOM',
    },
    {
      Code: 'GEO',
      Country: 'GEORGIA',
    },
    {
      Code: 'GGY',
      Country: 'GUERNSEY',
    },
    {
      Code: 'GHA',
      Country: 'GHANA',
    },
    {
      Code: 'GIB',
      Country: 'GIBRALTAR',
    },
    {
      Code: 'GIN',
      Country: 'GUINEA',
    },
    {
      Code: 'GLP',
      Country: 'GUADELOUPE',
    },
    {
      Code: 'GMB',
      Country: 'GAMBIA',
    },
    {
      Code: 'GNB',
      Country: 'GUINEA-BISSAU',
    },
    {
      Code: 'GNQ',
      Country: 'EQUATORIAL GUINEA',
    },
    {
      Code: 'GRC',
      Country: 'GREECE',
    },
    {
      Code: 'GRD',
      Country: 'GRENADA',
    },
    {
      Code: 'GRL',
      Country: 'GREENLAND',
    },
    {
      Code: 'GTM',
      Country: 'GUATEMALA',
    },
    {
      Code: 'GUF',
      Country: 'FRENCH GUIANA',
    },
    {
      Code: 'GUM',
      Country: 'GUAM',
    },
    {
      Code: 'GUY',
      Country: 'GUYANA',
    },
    {
      Code: 'HKG',
      Country: 'HONG KONG',
    },
    {
      Code: 'HMD',
      Country: 'HEARD AND MCDONALD ISLANDS',
    },
    {
      Code: 'HND',
      Country: 'HONDURAS',
    },
    {
      Code: 'HRV',
      Country: 'CROATIA',
    },
    {
      Code: 'HTI',
      Country: 'HAITI',
    },
    {
      Code: 'HUN',
      Country: 'HUNGARY',
    },
    {
      Code: 'IDN',
      Country: 'INDONESIA',
    },
    {
      Code: 'IMN',
      Country: 'ISLE OF MAN',
    },
    {
      Code: 'IND',
      Country: 'INDIA',
    },
    {
      Code: 'IOT',
      Country: 'BRITISH INDIAN OCEAN TERRITORY',
    },
    {
      Code: 'IRL',
      Country: 'IRELAND',
    },
    {
      Code: 'IRN',
      Country: 'IRAN',
    },
    {
      Code: 'IRQ',
      Country: 'IRAQ',
    },
    {
      Code: 'ISL',
      Country: 'ICELAND',
    },
    {
      Code: 'ISR',
      Country: 'ISRAEL',
    },
    {
      Code: 'ITA',
      Country: 'ITALY',
    },
    {
      Code: 'JAM',
      Country: 'JAMAICA',
    },
    {
      Code: 'JEY',
      Country: 'JERSEY (CHANNEL ISLANDS)',
    },
    {
      Code: 'JOR',
      Country: 'JORDAN ',
    },
    {
      Code: 'JPN',
      Country: 'JAPAN',
    },
    {
      Code: 'KAZ',
      Country: 'KAZAKHSTAN',
    },
    {
      Code: 'KEN',
      Country: 'KENYA',
    },
    {
      Code: 'KGZ',
      Country: 'KYRGYZSTAN',
    },
    {
      Code: 'KHM',
      Country: 'CAMBODIA',
    },
    {
      Code: 'KIR',
      Country: 'KIRIBATI',
    },
    {
      Code: 'KNA',
      Country: 'ST.KITTS AND NEVIS',
    },
    {
      Code: 'KOR',
      Country: 'THE REPUBLIC OF KOREA',
    },
    {
      Code: 'KWT',
      Country: 'KUWAIT',
    },
    {
      Code: 'LAO',
      Country: 'LAOS',
    },
    {
      Code: 'LBN',
      Country: 'LEBANON',
    },
    {
      Code: 'LBR',
      Country: 'LIBERIA',
    },
    {
      Code: 'LBY',
      Country: 'LIBYAN ARAB JAMAHIRIYA',
    },
    {
      Code: 'LCA',
      Country: 'SAINT LUCIA ',
    },
    {
      Code: 'LIE',
      Country: 'LIECHTENSTEIN ',
    },
    {
      Code: 'LKA',
      Country: 'SRI LANKA  ',
    },
    {
      Code: 'LSO',
      Country: 'LESOTHO',
    },
    {
      Code: 'LTU',
      Country: 'LITHUANIA',
    },
    {
      Code: 'LUX',
      Country: 'LUXEMBOURG',
    },
    {
      Code: 'LVA',
      Country: 'LATVIA ',
    },
    {
      Code: 'MAC',
      Country: 'MACAO',
    },
    {
      Code: 'MAF',
      Country: 'SAINT MARTIN (FRENCH PART)',
    },
    {
      Code: 'MAR',
      Country: 'MOROCCO',
    },
    {
      Code: 'MCO',
      Country: 'MONACO',
    },
    {
      Code: 'MDA',
      Country: 'MOLDOVA, REPUBLIC OF',
    },
    {
      Code: 'MDG',
      Country: 'MADAGASCAR',
    },
    {
      Code: 'MDV',
      Country: 'MALDIVES',
    },
    {
      Code: 'MEX',
      Country: 'MEXICO',
    },
    {
      Code: 'MHL',
      Country: 'MARSHALL ISLANDS ',
    },
    {
      Code: 'MKD',
      Country: 'MACEDONIA, THE FORMER YUGOSLAV REPUBLIC OF',
    },
    {
      Code: 'MLI',
      Country: 'MALI',
    },
    {
      Code: 'MLT',
      Country: 'MALTA',
    },
    {
      Code: 'MMR',
      Country: 'MYANMAR',
    },
    {
      Code: 'MNE',
      Country: 'MONTENEGRO',
    },
    {
      Code: 'MNG',
      Country: 'MONGOLIA ',
    },
    {
      Code: 'MNP',
      Country: 'NORTHERN MARIANA ISLANDS',
    },
    {
      Code: 'MOZ',
      Country: 'MOZAMBIQUE',
    },
    {
      Code: 'MRT',
      Country: 'MAURITANIA',
    },
    {
      Code: 'MSR',
      Country: 'MONTSERRAT',
    },
    {
      Code: 'MTQ',
      Country: 'MARTINIQUE',
    },
    {
      Code: 'MUS',
      Country: 'MAURITIUS',
    },
    {
      Code: 'MWI',
      Country: 'MALAWI',
    },
    {
      Code: 'MYS',
      Country: 'MALAYSIA',
    },
    {
      Code: 'MYT',
      Country: 'MAYOTTE',
    },
    {
      Code: 'NAM',
      Country: 'NAMIBIA',
    },
    {
      Code: 'NCL',
      Country: 'NEW CALEDONIA ',
    },
    {
      Code: 'NER',
      Country: 'NIGER',
    },
    {
      Code: 'NFK',
      Country: 'NORFOLK ISLAND',
    },
    {
      Code: 'NGA',
      Country: 'NIGERIA',
    },
    {
      Code: 'NIC',
      Country: 'NICARAGUA',
    },
    {
      Code: 'NIU',
      Country: 'NIUE',
    },
    {
      Code: 'NLD',
      Country: 'NETHERLANDS',
    },
    {
      Code: 'NOR',
      Country: 'NORWAY',
    },
    {
      Code: 'NPL',
      Country: 'NEPAL',
    },
    {
      Code: 'NRU',
      Country: 'NAURU',
    },
    {
      Code: 'NZL',
      Country: 'NEW ZEALAND ',
    },
    {
      Code: 'OMN',
      Country: 'OMAN',
    },
    {
      Code: 'PAK',
      Country: 'PAKISTAN',
    },
    {
      Code: 'PAN',
      Country: 'PANAMA',
    },
    {
      Code: 'PCN',
      Country: 'PITCAIRN',
    },
    {
      Code: 'PER',
      Country: 'PERU',
    },
    {
      Code: 'PHL',
      Country: 'PHILIPPINES',
    },
    {
      Code: 'PLW',
      Country: 'PALAU',
    },
    {
      Code: 'PNG',
      Country: 'PAPUA NEW GUINEA',
    },
    {
      Code: 'POL',
      Country: 'POLAND',
    },
    {
      Code: 'PRI',
      Country: 'PUERTO RICO',
    },
    {
      Code: 'PRK',
      Country: 'DEMOC.PEOPLES REP.OF KOREA',
    },
    {
      Code: 'PRT',
      Country: 'PORTUGAL',
    },
    {
      Code: 'PRY',
      Country: 'PARAGUAY',
    },
    {
      Code: 'PSE',
      Country: 'PALESTINIAN TERRITORY, OCCUPIED',
    },
    {
      Code: 'PYF',
      Country: 'FRENCH POLYNESIA',
    },
    {
      Code: 'QAT',
      Country: 'QATAR',
    },
    {
      Code: 'REU',
      Country: 'REUNION',
    },
    {
      Code: 'ROU',
      Country: 'ROMANIA',
    },
    {
      Code: 'RUS',
      Country: 'RUSSIAN FEDERATION (USSR)',
    },
    {
      Code: 'RWA',
      Country: 'RWANDA',
    },
    {
      Code: 'SAU',
      Country: 'SAUDI ARABIA',
    },
    {
      Code: 'SDN',
      Country: 'SUDAN',
    },
    {
      Code: 'SEN',
      Country: 'SENEGAL',
    },
    {
      Code: 'SGP',
      Country: 'SINGAPORE',
    },
    {
      Code: 'SGS',
      Country: 'SOUTH GEORGIA AND THE SOUTH SANDWICH ISLAND',
    },
    {
      Code: 'SHN',
      Country: 'ST. HELENA ',
    },
    {
      Code: 'SJM',
      Country: 'SVALBARD AND JAN MAYEN ISLANDS',
    },
    {
      Code: 'SLB',
      Country: 'SOLOMON ISLANDS',
    },
    {
      Code: 'SLE',
      Country: 'SIERRA LEONE',
    },
    {
      Code: 'SLV',
      Country: 'EL SALVADOR',
    },
    {
      Code: 'SMR',
      Country: 'SAN MARINO',
    },
    {
      Code: 'SOM',
      Country: 'SOMALIA',
    },
    {
      Code: 'SPM',
      Country: 'ST. PIERRE AND MIQUELON',
    },
    {
      Code: 'SRB',
      Country: 'SERBIA & MONTENEGRO ',
    },
    {
      Code: 'SSD',
      Country: 'SOUTH SUDAN',
    },
    {
      Code: 'STP',
      Country: 'SAO TOME AND PRINCIPE',
    },
    {
      Code: 'SUR',
      Country: 'SURINAME',
    },
    {
      Code: 'SVK',
      Country: 'SLOVAK REPUBLIC',
    },
    {
      Code: 'SVN',
      Country: 'SLOVENIA',
    },
    {
      Code: 'SWE',
      Country: 'SWEDEN',
    },
    {
      Code: 'SWZ',
      Country: 'ESWATINI, KINGDOM OF (SWAZILAND)',
    },
    {
      Code: 'SXM',
      Country: 'SINT MAARTEN (DUTCH PART)',
    },
    {
      Code: 'SYC',
      Country: 'SEYCHELLES',
    },
    {
      Code: 'SYR',
      Country: 'SYRIAN ARAB REPUBLIC',
    },
    {
      Code: 'TCA',
      Country: 'TURKS AND CAICOS ISLANDS',
    },
    {
      Code: 'TCD',
      Country: 'CHAD',
    },
    {
      Code: 'TGO',
      Country: 'TOGO',
    },
    {
      Code: 'THA',
      Country: 'THAILAND',
    },
    {
      Code: 'TJK',
      Country: 'TAJIKISTAN',
    },
    {
      Code: 'TKL',
      Country: 'TOKELAU',
    },
    {
      Code: 'TKM',
      Country: 'TURKMENISTAN',
    },
    {
      Code: 'TLS',
      Country: 'TIMOR-LESTE ',
    },
    {
      Code: 'TON',
      Country: 'TONGA',
    },
    {
      Code: 'TTO',
      Country: 'TRINIDAD AND TOBAGO',
    },
    {
      Code: 'TUN',
      Country: 'TUNISIA',
    },
    {
      Code: 'TUR',
      Country: 'TURKIYE ',
    },
    {
      Code: 'TUV',
      Country: 'TUVALU',
    },
    {
      Code: 'TWN',
      Country: 'TAIWAN',
    },
    {
      Code: 'TZA',
      Country: 'TANZANIA UNITED REPUBLIC',
    },
    {
      Code: 'UGA',
      Country: 'UGANDA',
    },
    {
      Code: 'UKR',
      Country: 'UKRAINE',
    },
    {
      Code: 'UMI',
      Country: 'UNITED STATES MINOR OUTLYING ISLANDS',
    },
    {
      Code: 'URY',
      Country: 'URUGUAY',
    },
    {
      Code: 'USA',
      Country: 'UNITED STATES OF AMERICA',
    },
    {
      Code: 'UZB',
      Country: 'UZBEKISTAN',
    },
    {
      Code: 'VAT',
      Country: 'VATICAN CITY STATE (HOLY SEE)',
    },
    {
      Code: 'VCT',
      Country: 'SAINT VINCENT AND GRENADINES',
    },
    {
      Code: 'VEN',
      Country: 'VENEZUELA',
    },
    {
      Code: 'VGB',
      Country: 'VIRGIN ISLANDS(BRITISH)',
    },
    {
      Code: 'VIR',
      Country: 'VIRGIN ISLANDS(US)',
    },
    {
      Code: 'VNM',
      Country: 'VIETNAM',
    },
    {
      Code: 'VUT',
      Country: 'VANUATU',
    },
    {
      Code: 'WLF',
      Country: 'WALLIS AND FUTUNA ISLANDS',
    },
    {
      Code: 'WSM',
      Country: 'SAMOA',
    },
    {
      Code: 'XKX',
      Country: 'KOSOVO',
    },
    {
      Code: 'YEM',
      Country: 'YEMEN',
    },
    {
      Code: 'ZAF',
      Country: 'SOUTH AFRICA',
    },
    {
      Code: 'ZMB',
      Country: 'ZAMBIA',
    },
    {
      Code: 'ZWE',
      Country: 'ZIMBABWE',
    },
  ]

  /**
   * Returns all country codes
   */
  public getAllCountryCodes(): CountryCode[] {
    return [...this.countryCodes]
  }

  public getAllCountryCodesCode(): string[] {
    return this.countryCodes.map((c) => c.Code)
  }

  /**
   * Returns a country code by its ISO code
   * @param code The ISO code to search for
   */
  public getCountryByCode(code: string): CountryCode | undefined {
    return this.countryCodes.find((c) => c.Code === code.toUpperCase())
  }

  /**
   * Returns a country code by country name
   * @param countryName The country name to search for
   */
  public getCountryByName(countryName: string): CountryCode | undefined {
    const normalizedName = countryName.toUpperCase().trim()
    return this.countryCodes.find((c) => c.Country === normalizedName)
  }

  /**
   * Filters country codes based on a search term
   * @param searchTerm The term to search for in code or country name
   * @param caseSensitive Whether the search should be case sensitive
   */
  public filterCountryCodes(searchTerm: string, caseSensitive = false): CountryCode[] {
    if (!searchTerm) {
      return this.getAllCountryCodes()
    }

    return this.countryCodes.filter((code) => {
      if (!caseSensitive) {
        const term = searchTerm.toLowerCase()
        return code.Code.toLowerCase().includes(term) || code.Country.toLowerCase().includes(term)
      }
      return code.Code.includes(searchTerm) || code.Country.includes(searchTerm)
    })
  }

  /**
   * Returns countries from a specific continent or region
   * @param region The region or continent to filter by
   */
  public filterCountriesByRegion(region: string): CountryCode[] {
    const lowercaseRegion = region.toLowerCase()
    return this.countryCodes.filter((code) => code.Country.toLowerCase().includes(lowercaseRegion))
  }
}

export class ClassificationCodeService {
  private readonly classificationCodes: ClassificationCode[] = [
    {
      Code: '001',
      Description: 'Breastfeeding equipment ',
    },
    {
      Code: '002',
      Description: 'Child care centres and kindergartens fees',
    },
    {
      Code: '003',
      Description: 'Computer, smartphone or tablet',
    },
    {
      Code: '004',
      Description: 'Consolidated e-Invoice ',
    },
    {
      Code: '005',
      Description:
        'Construction materials (as specified under Fourth Schedule of the Lembaga Pembangunan Industri Pembinaan Malaysia Act 1994)',
    },
    {
      Code: '006',
      Description: 'Disbursement ',
    },
    {
      Code: '007',
      Description: 'Donation',
    },
    {
      Code: '008',
      Description: 'e-Commerce - e-Invoice to buyer / purchaser',
    },
    {
      Code: '009',
      Description: 'e-Commerce - Self-billed e-Invoice to seller, logistics, etc. ',
    },
    {
      Code: '010',
      Description: 'Education fees',
    },
    {
      Code: '011',
      Description: 'Goods on consignment (Consignor)',
    },
    {
      Code: '012',
      Description: 'Goods on consignment (Consignee)',
    },
    {
      Code: '013',
      Description: 'Gym membership',
    },
    {
      Code: '014',
      Description: 'Insurance - Education and medical benefits',
    },
    {
      Code: '015',
      Description: 'Insurance - Takaful or life insurance',
    },
    {
      Code: '016',
      Description: 'Interest and financing expenses',
    },
    {
      Code: '017',
      Description: 'Internet subscription ',
    },
    {
      Code: '018',
      Description: 'Land and building',
    },
    {
      Code: '019',
      Description:
        'Medical examination for learning disabilities and early intervention or rehabilitation treatments of learning disabilities',
    },
    {
      Code: '020',
      Description: 'Medical examination or vaccination expenses',
    },
    {
      Code: '021',
      Description: 'Medical expenses for serious diseases',
    },
    {
      Code: '022',
      Description: 'Others',
    },
    {
      Code: '023',
      Description: 'Petroleum operations (as defined in Petroleum (Income Tax) Act 1967)',
    },
    {
      Code: '024',
      Description: 'Private retirement scheme or deferred annuity scheme ',
    },
    {
      Code: '025',
      Description: 'Motor vehicle',
    },
    {
      Code: '026',
      Description:
        'Subscription of books / journals / magazines / newspapers / other similar publications',
    },
    {
      Code: '027',
      Description: 'Reimbursement ',
    },
    {
      Code: '028',
      Description: 'Rental of motor vehicle',
    },
    {
      Code: '029',
      Description:
        'EV charging facilities (Installation, rental, sale / purchase or subscription fees) ',
    },
    {
      Code: '030',
      Description: 'Repair and maintenance',
    },
    {
      Code: '031',
      Description: 'Research and development ',
    },
    {
      Code: '032',
      Description: 'Foreign income ',
    },
    {
      Code: '033',
      Description: 'Self-billed - Betting and gaming ',
    },
    {
      Code: '034',
      Description: 'Self-billed - Importation of goods ',
    },
    {
      Code: '035',
      Description: 'Self-billed - Importation of services',
    },
    {
      Code: '036',
      Description: 'Self-billed - Others',
    },
    {
      Code: '037',
      Description: 'Self-billed - Monetary payment to agents, dealers or distributors ',
    },
    {
      Code: '038',
      Description:
        'Sports equipment, rental / entry fees for sports facilities, registration in sports competition or sports training fees imposed by associations / sports clubs / companies registered with the Sports Commissioner or Companies Commission of Malaysia and carrying out sports activities as listed under the Sports Development Act 1997',
    },
    {
      Code: '039',
      Description: 'Supporting equipment for disabled person',
    },
    {
      Code: '040',
      Description: 'Voluntary contribution to approved provident fund ',
    },
    {
      Code: '041',
      Description: 'Dental examination or treatment',
    },
    {
      Code: '042',
      Description: 'Fertility treatment',
    },
    {
      Code: '043',
      Description: 'Treatment and home care nursing, daycare centres and residential care centers',
    },
    {
      Code: '044',
      Description: 'Vouchers, gift cards, loyalty points, etc',
    },
    {
      Code: '045',
      Description: 'Self-billed - Non-monetary payment to agents, dealers or distributors',
    },
  ]

  /**
   * Returns all classification codes
   */
  public getAllCodes(): ClassificationCode[] {
    return [...this.classificationCodes]
  }

  public getAllCodesCode(): string[] {
    return this.classificationCodes.map((c) => c.Code)
  }

  /**
   * Returns a classification code by its code number
   * @param code The code to search for
   */
  public getCodeByNumber(code: string): ClassificationCode | undefined {
    return this.classificationCodes.find((c) => c.Code === code)
  }

  /**
   * Filters classification codes based on a search term
   * @param searchTerm The term to search for in code or description
   * @param caseSensitive Whether the search should be case sensitive
   */
  public filterCodes(searchTerm: string, caseSensitive: boolean = false): ClassificationCode[] {
    if (!searchTerm) {
      return this.getAllCodes()
    }

    return this.classificationCodes.filter((code) => {
      if (!caseSensitive) {
        const term = searchTerm.toLowerCase()
        return (
          code.Code.toLowerCase().includes(term) || code.Description.toLowerCase().includes(term)
        )
      }
      return code.Code.includes(searchTerm) || code.Description.includes(searchTerm)
    })
  }

  /**
   * Filters codes by category (e.g., "medical", "self-billed", etc.)
   * @param category The category to filter by
   */
  public filterByCategory(category: string): ClassificationCode[] {
    const lowercaseCategory = category.toLowerCase()
    return this.classificationCodes.filter((code) =>
      code.Description.toLowerCase().includes(lowercaseCategory)
    )
  }
}

export class InvoiceTypesService {
  private readonly invoiceTypes: InvoiceType[] = [
    {
      Code: '01',
      Description: 'Invoice',
    },
    {
      Code: '02',
      Description: 'Credit Note',
    },
    {
      Code: '03',
      Description: 'Debit Note',
    },
    {
      Code: '04',
      Description: 'Refund Note',
    },
    {
      Code: '11',
      Description: 'Self-billed Invoice',
    },
    {
      Code: '12',
      Description: 'Self-billed Credit Note',
    },
    {
      Code: '13',
      Description: 'Self-billed Debit Note',
    },
    {
      Code: '14',
      Description: 'Self-billed Refund Note',
    },
  ]

  /**
   * Returns all invoice types
   */
  public getAllTypes(): InvoiceType[] {
    return [...this.invoiceTypes]
  }

  public getAllTypesCode(): string[] {
    return this.invoiceTypes.map((type) => type.Code)
  }

  /**
   * Returns an invoice type by its code
   * @param code The code to search for
   */
  public getByCode(code: string): InvoiceType | undefined {
    return this.invoiceTypes.find((type) => type.Code === code)
  }

  /**
   * Filters invoice types based on a search term
   * @param searchTerm The term to search for in code or description
   * @param caseSensitive Whether the search should be case sensitive
   */
  public filterTypes(searchTerm: string, caseSensitive: boolean = false): InvoiceType[] {
    if (!searchTerm) {
      return this.getAllTypes()
    }

    return this.invoiceTypes.filter((type) => {
      if (!caseSensitive) {
        const term = searchTerm.toLowerCase()
        return (
          type.Code.toLowerCase().includes(term) || type.Description.toLowerCase().includes(term)
        )
      }
      return type.Code.includes(searchTerm) || type.Description.includes(searchTerm)
    })
  }

  /**
   * Returns all self-billed invoice types
   */
  public getSelfBilledTypes(): InvoiceType[] {
    return this.invoiceTypes.filter((type) =>
      type.Description.toLowerCase().includes('self-billed')
    )
  }

  /**
   * Returns all regular (non-self-billed) invoice types
   */
  public getRegularTypes(): InvoiceType[] {
    return this.invoiceTypes.filter(
      (type) => !type.Description.toLowerCase().includes('self-billed')
    )
  }

  /**
   * Checks if an invoice type is self-billed
   * @param code The invoice type code to check
   */
  public isSelfBilled(code: string): boolean {
    const type = this.getByCode(code)
    return type ? type.Description.toLowerCase().includes('self-billed') : false
  }
}

export class MSICService {
  private readonly msicCodes: MSICCode[] = [
    {
      'Code': '00000',
      'Description': 'NOT APPLICABLE',
      'MSIC Category Reference': '',
    },
    {
      'Code': '01111',
      'Description': 'Growing of maize',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01112',
      'Description': 'Growing of leguminous crops',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01113',
      'Description': 'Growing of oil seeds',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01119',
      'Description': 'Growing of other cereals n.e.c.',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01120',
      'Description': 'Growing of paddy',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01131',
      'Description': 'Growing of leafy or stem vegetables',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01132',
      'Description': 'Growing of fruits bearing vegetables',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01133',
      'Description': 'Growing of melons',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01134',
      'Description': 'Growing of mushrooms and truffles',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01135',
      'Description': 'Growing of vegetables seeds, except beet seeds',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01136',
      'Description': 'Growing of other vegetables',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01137',
      'Description': 'Growing of sugar beet',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01138',
      'Description': 'Growing of roots, tubers, bulb or tuberous vegetables',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01140',
      'Description': 'Growing of sugar cane',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01150',
      'Description': 'Growing of tobacco',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01160',
      'Description': 'Growing of fibre crops',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01191',
      'Description': 'Growing of flowers',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01192',
      'Description': 'Growing of flower seeds',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01193',
      'Description': 'Growing of sago (rumbia)',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01199',
      'Description': 'Growing of other non-perennial crops n.e.c.',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01210',
      'Description': 'Growing of grapes',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01221',
      'Description': 'Growing of banana',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01222',
      'Description': 'Growing of mango',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01223',
      'Description': 'Growing of durian',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01224',
      'Description': 'Growing of rambutan',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01225',
      'Description': 'Growing of star fruit',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01226',
      'Description': 'Growing of papaya',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01227',
      'Description': 'Growing of pineapple',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01228',
      'Description': 'Growing of pitaya (dragon fruit)',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01229',
      'Description': 'Growing of other tropical and subtropical fruits n.e.c.',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01231',
      'Description': 'Growing of pomelo',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01232',
      'Description': 'Growing of lemon and limes',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01233',
      'Description': 'Growing of tangerines and mandarin',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01239',
      'Description': 'Growing of other citrus fruits n.e.c.',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01241',
      'Description': 'Growing of guava',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01249',
      'Description': 'Growing of other pome fruits and stones fruits n.e.c.',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01251',
      'Description': 'Growing of berries',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01252',
      'Description': 'Growing of fruit seeds',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01253',
      'Description': 'Growing of edible nuts',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01259',
      'Description': 'Growing of other tree and bush fruits',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01261',
      'Description': 'Growing of oil palm (estate)',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01262',
      'Description': 'Growing of oil palm (smallholdings)',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01263',
      'Description': 'Growing of coconut (estate and smallholdings)',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01269',
      'Description': 'Growing of other oleaginous fruits n.e.c.',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01271',
      'Description': 'Growing of coffee',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01272',
      'Description': 'Growing of tea',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01273',
      'Description': 'Growing of cocoa',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01279',
      'Description': 'Growing of other beverage crops n.e.c.',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01281',
      'Description': 'Growing of pepper (piper nigrum)',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01282',
      'Description': 'Growing of chilies and pepper (capsicum spp.)',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01283',
      'Description': 'Growing of nutmeg',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01284',
      'Description': 'Growing of ginger',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01285',
      'Description':
        'Growing of plants used primarily in perfumery, in pharmacy or for insecticidal, fungicidal or similar purposes',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01289',
      'Description': 'Growing of other spices and aromatic crops n.e.c.',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01291',
      'Description': 'Growing  of rubber trees (estate)',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01292',
      'Description': 'Growing of rubber trees (smallholdings)',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01293',
      'Description': 'Growing of trees for extraction of sap',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01294',
      'Description': 'Growing of nipa palm',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01295',
      'Description': 'Growing of areca',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01296',
      'Description': 'Growing of roselle',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01299',
      'Description': 'Growing of other perennial crops n.e.c.',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01301',
      'Description': 'Growing of plants for planting',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01302',
      'Description': 'Growing of plants for ornamental purposes',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01303',
      'Description':
        'Growing of live plants for bulbs, tubers and roots; cuttings and slips; mushroom spawn',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01304',
      'Description': 'Operation of tree nurseries',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01411',
      'Description': 'Raising, breeding and production of cattle or buffaloes',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01412',
      'Description': 'Production of raw milk from cows or buffaloes',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01413',
      'Description': 'Production of bovine semen',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01420',
      'Description': 'Raising and breeding of horses, asses, mules or hinnes',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01430',
      'Description': 'Raising and breeding of camels (dromedary) and camelids',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01441',
      'Description': 'Raising, breeding and production of sheep and goats',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01442',
      'Description': 'Production of raw sheep or goat’s milk',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01443',
      'Description': 'Production of raw wool',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01450',
      'Description': 'Raising, breeding and production of swine/pigs',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01461',
      'Description': 'Raising, breeding and production of chicken, broiler',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01462',
      'Description': 'Raising, breeding and production of ducks',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01463',
      'Description': 'Raising, breeding and production of geese',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01464',
      'Description': 'Raising, breeding and production of quails',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01465',
      'Description': 'Raising and breeding of other poultry n.e.c.',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01466',
      'Description': 'Production of chicken eggs',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01467',
      'Description': 'Production of duck eggs',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01468',
      'Description': 'Production of other poultry eggs n.e.c.',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01469',
      'Description': 'Operation of poultry hatcheries',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01491',
      'Description': 'Raising, breeding and production of semi-domesticated',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01492',
      'Description': 'Production of fur skins, reptile or bird’s skin from ranching operation',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01493',
      'Description': 'Operation of worm farms, land mollusc farms, snail farms',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01494',
      'Description': 'Raising of silk worms and production of silk worm cocoons',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01495',
      'Description': 'Bee keeping and production of honey and beeswax',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01496',
      'Description': 'Raising and breeding of pet animals',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01497',
      'Description': 'Raising and breeding of swiflet',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01499',
      'Description': 'Raising of diverse/other animals n.e.c.',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01500',
      'Description': 'Mixed Farming',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01610',
      'Description': 'Agricultural activities for crops production on a fee or contract basis',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01620',
      'Description': 'Agricultural activities for animal production on a fee or contract basis',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01631',
      'Description': 'Preparation of crops for primary markets',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01632',
      'Description': 'Preparation of tobacco leaves',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01633',
      'Description': 'Preparation of cocoa beans',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01634',
      'Description': 'Sun-drying of fruits and vegetables',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01640',
      'Description': 'Seed processing for propagation',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01701',
      'Description': 'Hunting and trapping on a commercial basis',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '01702',
      'Description': 'Taking of animals (dead or alive)',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '02101',
      'Description':
        'Planting, replanting, transplanting, thinning and conserving of forests and timber tracts',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '02102',
      'Description': 'Growing of coppice, pulpwood and fire wood',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '02103',
      'Description': 'Operation of forest tree nurseries',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '02104',
      'Description': 'Collection and raising of wildings (peat swamp forest tree species)',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '02105',
      'Description': 'Forest plantation',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '02201',
      'Description': 'Production of round wood for forest-based manufacturing industries',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '02202',
      'Description': 'Production of round wood used in an unprocessed form',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '02203',
      'Description': 'Production of charcoal in the forest (using traditional methods)',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '02204',
      'Description': 'Rubber wood logging',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '02301',
      'Description': 'Collection of rattan, bamboo',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '02302',
      'Description': 'Bird’s nest collection',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '02303',
      'Description': 'Wild sago palm collection',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '02309',
      'Description': 'Gathering of non-wood forest products n.e.c.',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '02401',
      'Description':
        'Carrying out part of the forestry and forest plantation operation on a fee or contract basis for forestry service activities',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '02402',
      'Description':
        'Carrying out part of the forestry operation on a fee or contract basis for logging service activities',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '03111',
      'Description': 'Fishing on a commercial basis in ocean and coastal waters',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '03112',
      'Description': 'Collection of marine crustaceans and molluscs',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '03113',
      'Description': 'Taking of aquatic animals: sea squirts, tunicates, sea urchins',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '03114',
      'Description':
        'Activities of vessels engaged both in fishing and in processing and preserving of fish',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '03115',
      'Description':
        'Gathering of other marine organisms and materials (natural pearls, sponges, coral and algae)',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '03119',
      'Description': 'Marine fishing n.e.c.',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '03121',
      'Description': 'Fishing on a commercial basis in inland waters',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '03122',
      'Description': 'Taking of freshwater crustaceans and molluscs',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '03123',
      'Description': 'Taking of freshwater aquatic animals',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '03124',
      'Description': 'Gathering of freshwater flora and fauna',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '03129',
      'Description': 'Freshwater fishing n.e.c.',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '03211',
      'Description': 'Fish farming in sea water',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '03212',
      'Description':
        'Production of bivalve spat (oyster, mussel), lobster lings, shrimp post-larvae, fish fry and fingerlings',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '03213',
      'Description': 'Growing of laver and other edible seaweeds',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '03214',
      'Description':
        'Culture of crustaceans, bivalves, other molluscs and other aquatic animals in sea water',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '03215',
      'Description': 'Aquaculture activities in brackish water',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '03216',
      'Description': 'Aquaculture activities in salt water filled tanks or reservoirs',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '03217',
      'Description': 'Operation of  hatcheries (marine)',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '03218',
      'Description': 'Operation of marine worm farms for fish feed',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '03219',
      'Description': 'Marine aquaculture n.e.c.',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '03221',
      'Description': 'Fish farming in freshwater',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '03222',
      'Description': 'Shrimp farming in freshwater',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '03223',
      'Description':
        'Culture of freshwater crustaceans, bivalves, other molluscs and other aquatic animals',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '03224',
      'Description': 'Operation of hatcheries (freshwater)',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '03225',
      'Description': 'Farming of frogs',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '03229',
      'Description': 'Freshwater aquaculture n.e.c.',
      'MSIC Category Reference': 'A',
    },
    {
      'Code': '05100',
      'Description': 'Mining of hard coal',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '05200',
      'Description': 'Mining of lignite (brown coal)',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '06101',
      'Description': 'Extraction of crude petroleum oils',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '06102',
      'Description': 'Extraction of bituminous or oil shale and tar sand',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '06103',
      'Description': 'Production of crude petroleum from bituminous shale and sand',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '06104',
      'Description': 'Processes to obtain crude oils',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '06201',
      'Description': 'Production of crude gaseous hydrocarbon (natural gas)',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '06202',
      'Description': 'Extraction of condensates',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '06203',
      'Description': 'Draining and separation of liquid hydrocarbon fractions',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '06204',
      'Description': 'Gas desulphurization',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '06205',
      'Description': 'Mining of hydrocarbon liquids, obtain through liquefaction or pyrolysis',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '07101',
      'Description': 'Mining of ores valued chiefly for iron content',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '07102',
      'Description': 'Beneficiation and agglomeration of iron ores',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '07210',
      'Description': 'Mining of uranium and thorium ores',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '07291',
      'Description': 'Mining of tin ores',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '07292',
      'Description': 'Mining of copper',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '07293',
      'Description': 'Mining of bauxite (aluminium)',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '07294',
      'Description': 'Mining of ilmenite',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '07295',
      'Description': 'Mining of gold',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '07296',
      'Description': 'Mining of silver',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '07297',
      'Description': 'Mining of platinum',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '07298',
      'Description': 'Amang retreatment',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '07299',
      'Description': 'Mining of other non-ferrous metal ores n.e.c.',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '08101',
      'Description':
        'Quarrying, rough trimming and sawing of monumental and building stone such as marble, granite (dimension stone), sandstone',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '08102',
      'Description': 'Quarrying, crushing and breaking of limestone',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '08103',
      'Description': 'Mining of gypsum and anhydrite',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '08104',
      'Description': 'Mining of chalk and uncalcined dolomite',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '08105',
      'Description': 'Extraction and dredging of industrial sand, sand for construction and gravel',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '08106',
      'Description': 'Breaking and crushing of stone and gravel',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '08107',
      'Description': 'Quarrying of sand',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '08108',
      'Description': 'Mining of clays, refractory clays and kaolin',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '08109',
      'Description': 'Quarrying, crushing and breaking of granite',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '08911',
      'Description': 'Mining of natural phosphates',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '08912',
      'Description': 'Mining of natural potassium salts',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '08913',
      'Description': 'Mining of native sulphur',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '08914',
      'Description': 'Extraction and preparation of pyrites and pyrrhotite, except roasting',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '08915',
      'Description': 'Mining of natural barium sulphate and carbonate (barytes and witherite)',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '08916',
      'Description': 'Mining of natural borates, natural magnesium sulphates (kieserite)',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '08917',
      'Description':
        'Mining of earth colours, fluorspar and other minerals valued chiefly as a source of chemicals',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '08918',
      'Description': 'Guano mining',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '08921',
      'Description': 'Peat digging',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '08922',
      'Description': 'Peat agglomeration',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '08923',
      'Description': 'Preparation of peat to improve quality or facilitate transport or storage',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '08931',
      'Description': 'Extraction of salt from underground',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '08932',
      'Description': 'Salt production by evaporation of sea water or other saline waters',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '08933',
      'Description': 'Crushing, purification and refining of salt by the producer',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '08991',
      'Description': 'Mining and quarrying of abrasive materials',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '08992',
      'Description': 'Mining and quarrying of asbestos',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '08993',
      'Description': 'Mining and quarrying of siliceous fossil meals',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '08994',
      'Description': 'Mining and quarrying of natural graphite',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '08995',
      'Description': 'Mining and quarrying of steatite (talc)',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '08996',
      'Description': 'Mining and quarrying of gemstones',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '08999',
      'Description': 'Other mining and quarrying n.e.c.',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '09101',
      'Description':
        'Oil and gas extraction service activities provided on a fee or contract basis',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '09102',
      'Description': 'Oil and gas field fire fighting services',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '09900',
      'Description': 'Support activities for other mining and quarrying',
      'MSIC Category Reference': 'B',
    },
    {
      'Code': '10101',
      'Description': 'Processing and preserving of meat and production of meat products',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10102',
      'Description': 'Processing and preserving of poultry and poultry products',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10103',
      'Description': 'Production of hides and skins originating from slaughterhouses',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10104',
      'Description':
        'Operation of slaughterhouses engaged in killing, houses dressing or packing meat',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10109',
      'Description': 'Processing and preserving of meat n.e.c.',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10201',
      'Description': 'Canning of fish, crustaceans and mollusks',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10202',
      'Description': 'Processing, curing and preserving of fish, crustacean and molluscs',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10203',
      'Description': 'Production of fish meals for human consumption or animal feed',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10204',
      'Description': 'Production of keropok including keropok lekor',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10205',
      'Description': 'Processing of seaweed',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10301',
      'Description': 'Manufacture of fruits and vegetable food products',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10302',
      'Description': 'Manufacture of fruit and vegetable juices',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10303',
      'Description': 'Pineapple canning',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10304',
      'Description': 'Manufacture of jams, marmalades and table jellies',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10305',
      'Description': 'Manufacture of nuts and nut products',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10306',
      'Description': 'Manufacture of bean curd products',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10401',
      'Description': 'Manufacture of crude palm oil',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10402',
      'Description': 'Manufacture of refined palm oil',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10403',
      'Description': 'Manufacture of palm kernel oil',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10404',
      'Description': 'Manufacture of crude and refined vegetable oil',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10405',
      'Description': 'Manufacture of coconut oil',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10406',
      'Description': 'Manufacture of compound cooking fats',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10407',
      'Description': 'Manufacture of animal oils and fats',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10501',
      'Description': 'Manufacture of ice cream and other edible ice such as sorbet',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10502',
      'Description': 'Manufacture of condensed, powdered and evaporated milk',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10509',
      'Description': 'Manufacture of other dairy products n.e.c.',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10611',
      'Description': 'Rice milling',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10612',
      'Description': 'Provision of milling services',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10613',
      'Description': 'Flour milling',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10619',
      'Description': 'Manufacture of grain mill products n.e.c.',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10621',
      'Description': 'Manufacture of starches and starch products',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10622',
      'Description': 'Manufacture of glucose, glucose syrup, maltose, inulin',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10623',
      'Description': 'Manufacture of sago and tapioca flour/products',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10711',
      'Description': 'Manufacture of biscuits and cookies',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10712',
      'Description': 'Manufacture of bread, cakes and other bakery products',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10713',
      'Description': 'Manufacture of snack products',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10714',
      'Description': 'Manufacture of frozen bakery products',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10721',
      'Description': 'Manufacture of sugar',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10722',
      'Description': 'Manufacture of sugar products',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10731',
      'Description': 'Manufacture of cocoa products',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10732',
      'Description': 'Manufacture of chocolate and chocolate products',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10733',
      'Description': 'Manufacture of sugar confectionery',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10741',
      'Description': 'Manufacture of meehoon, noodles and other related products',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10742',
      'Description': 'Manufacture of pastas',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10750',
      'Description': 'Manufacture of prepared meals and dishes',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10791',
      'Description': 'Manufacture of coffee',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10792',
      'Description': 'Manufacture of tea',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10793',
      'Description': 'Manufacture of sauces and condiments',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10794',
      'Description': 'Manufacture of spices and curry powder',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10795',
      'Description': 'Manufacture of egg products',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10799',
      'Description': 'Manufacture of other food products n.e.c.',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '10800',
      'Description': 'Manufacture of prepared animal feeds',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '11010',
      'Description': 'Distilling, rectifying and blending of spirits',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '11020',
      'Description': 'Manufacture of wines',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '11030',
      'Description': 'Manufacture of malt liquors and malt',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '11041',
      'Description': 'Manufacture of soft drinks',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '11042',
      'Description': 'Production of natural mineral water and other bottled water',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '12000',
      'Description': 'MANUFACTURE OF TOBACCO PRODUCTS',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '13110',
      'Description': 'Preparation and spinning of textile fibres',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '13120',
      'Description': 'Weaving of textiles',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '13131',
      'Description': 'Batik making',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '13132',
      'Description': 'Dyeing, bleaching, printing and finishing of yarns and fabrics',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '13139',
      'Description': 'Other finishing textiles',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '13910',
      'Description': 'Manufacture of knitted and crocheted fabrics',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '13921',
      'Description':
        'Manufacture of made-up articles of any textile materials, including of knitted or crocheted fabrics',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '13922',
      'Description': 'Manufacture of made-up furnishing articles',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '13930',
      'Description': 'Manufacture of carpets and rugs',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '13940',
      'Description': 'Manufacture of cordage, rope, twine and netting',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '13990',
      'Description': 'Manufacture of other textiles n.e.c.',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '14101',
      'Description': 'Manufacture of specific wearing apparel',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '14102',
      'Description': 'Manufacture of clothings',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '14103',
      'Description': 'Custom tailoring',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '14109',
      'Description': 'Manufacture of other clothing accessories',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '14200',
      'Description': 'Manufacture of articles made of fur skins',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '14300',
      'Description': 'Manufacture of knitted and crocheted apparel',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '15110',
      'Description': 'Tanning and dressing of leather; dressing and dyeing of fur',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '15120',
      'Description': 'Manufacture of luggage, handbags and the like, saddlery and harness',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '15201',
      'Description': 'Manufacture of leather footwear',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '15202',
      'Description': 'Manufacture of plastic footwear',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '15203',
      'Description': 'Manufacture of rubber footwear',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '15209',
      'Description': 'Manufacture of other footwear n.e.c.',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '16211',
      'Description': 'Manufacture of veneer sheets and plywood',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '16221',
      'Description': "Manufacture of builders' carpentry",
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '16222',
      'Description': 'Manufacture of joinery wood products',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '16230',
      'Description': 'Manufacture of wooden containers',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '16291',
      'Description': 'Manufacture of wood charcoal',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '16292',
      'Description':
        'Manufacture of other products of wood, cane, articles of cork, straw and plaiting materials',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '17010',
      'Description': 'Manufacture of pulp, paper and paperboard',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '17020',
      'Description':
        'Manufacture of corrugated paper and paperboard and of containers of paper and paperboard',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '17091',
      'Description': 'Manufacture of envelopes and letter-card',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '17092',
      'Description': 'Manufacture of household and personal hygiene paper',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '17093',
      'Description':
        'Manufacture of gummed or adhesive paper in strips or rolls and labels and wall paper',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '17094',
      'Description': 'Manufacture of effigies, funeral paper goods, joss paper',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '17099',
      'Description': 'Manufacture of other articles of paper and paperboard n.e.c.',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '18110',
      'Description': 'Printing',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '18120',
      'Description': 'Service activities related to printing',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '18200',
      'Description': 'Reproduction of recorded media',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '19100',
      'Description': 'Manufacture of coke oven products',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '19201',
      'Description': 'Manufacture of refined petroleum products',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '19202',
      'Description': 'Manufacture of bio-diesel products',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '20111',
      'Description': 'Manufacture of liquefied or compressed inorganic industrial or medical gases',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '20112',
      'Description': 'Manufacture of basic organic chemicals',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '20113',
      'Description': 'Manufacture of inorganic compounds',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '20119',
      'Description': 'Manufacture of other basic chemicals n.e.c.',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '20121',
      'Description': 'Manufacture of fertilizers',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '20129',
      'Description': 'Manufacture of associated nitrogen products',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '20131',
      'Description': 'Manufacture of plastic in primary forms',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '20132',
      'Description': 'Manufacture of synthetic rubber in primary forms: synthetic rubber, factice',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '20133',
      'Description':
        'Manufacture of mixtures of synthetic rubber and natural rubber or rubber - like gums',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '20210',
      'Description': 'Manufacture of pesticides and other agrochemical products',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '20221',
      'Description': 'Manufacture of paints, varnishes and similar coatings ink and mastics',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '20222',
      'Description': 'Manufacture of printing ink',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '20231',
      'Description': 'Manufacture of soap and detergents, cleaning and polishing preparations',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '20232',
      'Description': 'Manufacture of perfumes and toilet preparations',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '20291',
      'Description':
        'Manufacture of photographic plates, films, sensitized paper and other sensitized unexposed materials',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '20292',
      'Description': 'Manufacture of writing and drawing ink',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '20299',
      'Description': 'Manufacture of other chemical products n.e.c.',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '20300',
      'Description': 'Manufacture of man-made fibres',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '21001',
      'Description':
        'Manufacture of medicinal active substances to be used for their pharmacological properties in the manufacture of medicaments',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '21002',
      'Description': 'Processing of blood',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '21003',
      'Description': 'Manufacture of medicaments',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '21004',
      'Description': 'Manufacture of chemical contraceptive products',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '21005',
      'Description': 'Manufacture of medical diagnostic preparation',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '21006',
      'Description': 'Manufacture of radioactive in-vivo diagnostic substances',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '21007',
      'Description': 'Manufacture of biotech pharmaceuticals',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '21009',
      'Description':
        'Manufacture of other pharmaceuticals, medicinal chemical and botanical products n.e.c.',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '22111',
      'Description': 'Manufacture of rubber tyres for vehicles',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '22112',
      'Description': 'Manufacture of interchangeable tyre treads and retreading rubber tyres',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '22191',
      'Description':
        'Manufacture of other products of natural or synthetic rubber, unvulcanized, vulcanized or hardened',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '22192',
      'Description': 'Manufacture of rubber gloves',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '22193',
      'Description': 'Rubber remilling and latex processing',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '22199',
      'Description': 'Manufacture of other rubber products n.e.c',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '22201',
      'Description': 'Manufacture of semi-manufactures of plastic products',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '22202',
      'Description': 'Manufacture of finished plastic products',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '22203',
      'Description': 'Manufacture of plastic articles for the packing of goods',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '22204',
      'Description': "Manufacture of builders' plastics ware",
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '22205',
      'Description': 'Manufacture of plastic tableware, kitchenware and toilet articles',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '22209',
      'Description': 'Manufacture of diverse plastic products n.e.c.',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '23101',
      'Description': 'Manufacture of flat glass, including wired, coloured or tinted flat glass',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '23102',
      'Description': 'Manufacture of laboratory, hygienic or pharmaceutical glassware',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '23109',
      'Description': 'Manufacture of other glass products n.e.c.',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '23911',
      'Description': 'Manufacture of refractory mortars and concretes',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '23912',
      'Description': 'Manufacture of refractory ceramic goods',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '23921',
      'Description': 'Manufacture of non-refractory ceramic',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '23929',
      'Description': 'Manufacture of other clay building materials',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '23930',
      'Description': 'Manufacture of other porcelain and ceramic products',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '23941',
      'Description': 'Manufacture of hydraulic cement',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '23942',
      'Description': 'Manufacture of lime and plaster',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '23951',
      'Description': 'Manufacture of ready-mix and dry-mix concrete and mortars',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '23952',
      'Description':
        'Manufacture of precast concrete, cement or artificial stone articles for use in construction',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '23953',
      'Description':
        'Manufacture of prefabricated structural components for building or civil engineering of cement, concrete or artificial stone',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '23959',
      'Description': 'Manufacture of other articles of concrete, cement and plaster n.e.c.',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '23960',
      'Description': 'Cutting, shaping and finishing of stone',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '23990',
      'Description': 'Manufacture of other non-metallic mineral products n.e.c.',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '24101',
      'Description':
        'Production of pig iron and spiegeleisen in pigs, blocks or other primary forms',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '24102',
      'Description': 'Production of bars and rods of stainless steel or other alloy steel',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '24103',
      'Description':
        'Manufacture of seamless tubes, by hot rolling, hot extrusion or hot drawing, or by cold drawing or cold rolling',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '24104',
      'Description': 'Manufacture of steel tube fittings',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '24109',
      'Description': 'Manufacture of other basic iron and steel products n.e.c.',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '24201',
      'Description': 'Tin smelting',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '24202',
      'Description': 'Production of aluminium from alumina',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '24209',
      'Description': 'Manufacture of other basic precious and other non-ferrous metals n.e.c.',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '24311',
      'Description': 'Casting of iron',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '24312',
      'Description': 'Casting of steel',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '24320',
      'Description': 'Casting of non-ferrous metals',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '25111',
      'Description': 'Manufacture of industrial frameworks in metal',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '25112',
      'Description': 'Manufacture of prefabricated buildings mainly of metal',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '25113',
      'Description': 'Manufacture of metal doors, windows and their frames, shutters and gates',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '25119',
      'Description': 'Manufacture of other structural metal products',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '25120',
      'Description': 'Manufacture of tanks, reservoirs and containers of metal',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '25130',
      'Description': 'Manufacture of steam generators, except central heating hot water boilers',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '25200',
      'Description': 'Manufacture of weapons and ammunition',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '25910',
      'Description': 'Forging, pressing, stamping and roll-forming of metal; powder metallurgy',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '25920',
      'Description': 'Treatment and coating of metals; machining',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '25930',
      'Description': 'Manufacture of cutlery, hand tools and general hardware',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '25991',
      'Description': 'Manufacture of tins and cans for food products, collapsible tubes and boxes',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '25992',
      'Description': 'Manufacture of metal cable, plaited bands and similar articles',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '25993',
      'Description': 'Manufacture of bolts, screws, nuts and similar threaded products',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '25994',
      'Description': 'Manufacture of metal household articles',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '25999',
      'Description': 'Manufacture of any other fabricated metal products n.e.c.',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '26101',
      'Description': 'Manufacture of diodes, transistors and similar semiconductor devices',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '26102',
      'Description': 'Manufacture electronic integrated circuits micro assemblies',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '26103',
      'Description': 'Manufacture of electrical capacitors and resistors',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '26104',
      'Description': 'Manufacture of printed circuit boards',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '26105',
      'Description': 'Manufacture of display components',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '26109',
      'Description': 'Manufacture of other components for electronic applications',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '26201',
      'Description': 'Manufacture of computers',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '26202',
      'Description': 'Manufacture of peripheral equipment',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '26300',
      'Description': 'Manufacture of communication equipment',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '26400',
      'Description': 'Manufacture of consumer electronics',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '26511',
      'Description': 'Manufacture of measuring, testing, navigating and control equipment',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '26512',
      'Description': 'Manufacture of industrial process control equipment',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '26520',
      'Description': 'Manufacture of watches and clocks and parts',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '26600',
      'Description': 'Manufacture of irradiation, electro medical and electrotherapeutic equipment',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '26701',
      'Description': 'Manufacture of optical instruments and equipment',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '26702',
      'Description': 'Manufacture of photographic equipment',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '26800',
      'Description': 'Manufacture of magnetic and optical recording media',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '27101',
      'Description': 'Manufacture of electric motors, generators and transformers',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '27102',
      'Description': 'Manufacture of electricity distribution and control apparatus',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '27200',
      'Description': 'Manufacture of batteries and accumulators',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '27310',
      'Description': 'Manufacture of fibre optic cables',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '27320',
      'Description': 'Manufacture of other electronic and electric wires and cables',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '27330',
      'Description':
        'Manufacture of current-carrying and non current-carrying wiring devices for electrical circuits regardless of material',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '27400',
      'Description': 'Manufacture of electric lighting equipment',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '27500',
      'Description': 'Manufacture of domestic appliances',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '27900',
      'Description':
        'Manufacture of miscellaneous electrical equipment other than motors, generators and transformers, batteries and accumulators, wires and wiring devices, lighting equipment or domestic appliances',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '28110',
      'Description':
        'Manufacture of engines and turbines, except aircraft, vehicle and cycle engines',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '28120',
      'Description': 'Manufacture of fluid power equipment',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '28130',
      'Description': 'Manufacture of other pumps, compressors, taps and valves',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '28140',
      'Description': 'Manufacture of bearings, gears, gearing and driving elements',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '28150',
      'Description': 'Manufacture of ovens, furnaces and furnace burners',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '28160',
      'Description': 'Manufacture of lifting and handling equipment',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '28170',
      'Description':
        'Manufacture of office machinery and equipment (except computers and peripheral equipment)',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '28180',
      'Description':
        'Manufacture of power-driven hand tools with self-contained electric or non-electric motor or pneumatic drives',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '28191',
      'Description': 'Manufacture of refrigerating or freezing industrial equipment',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '28192',
      'Description': 'Manufacture of air-conditioning machines, including for motor vehicles',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '28199',
      'Description': 'Manufacture of other general-purpose machinery n.e.c.',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '28210',
      'Description': 'Manufacture of agricultural and forestry machinery',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '28220',
      'Description': 'Manufacture of metal-forming machinery and machine tools',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '28230',
      'Description': 'Manufacture of machinery for metallurgy',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '28240',
      'Description': 'Manufacture of machinery for mining, quarrying and construction',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '28250',
      'Description': 'Manufacture of machinery for food, beverage and tobacco processing',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '28260',
      'Description': 'Manufacture of machinery for textile, apparel and leather production',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '28290',
      'Description': 'Manufacture of other special-purpose machinery n.e.c.',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '29101',
      'Description': 'Manufacture of passenger cars',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '29102',
      'Description': 'Manufacture of commercial vehicles',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '29200',
      'Description':
        'Manufacture of bodies (coachwork) for motor vehicles; manufacture of trailers and semi- trailers',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '29300',
      'Description': 'Manufacture of parts and accessories for motor vehicles',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '30110',
      'Description': 'Building of ships and floating structures',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '30120',
      'Description': 'Building of pleasure and sporting boats',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '30200',
      'Description': 'Manufacture of railway locomotives and rolling stock',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '30300',
      'Description': 'Manufacture of air and spacecraft and related machinery',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '30400',
      'Description': 'Manufacture of military fighting vehicles',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '30910',
      'Description': 'Manufacture of motorcycles',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '30920',
      'Description': 'Manufacture of bicycles and invalid carriages',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '30990',
      'Description': 'Manufacture of other transport equipments n.e.c.',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '31001',
      'Description': 'Manufacture of wooden and cane furniture',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '31002',
      'Description': 'Manufacture of metal furniture',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '31003',
      'Description': 'Manufacture of mattress',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '31009',
      'Description': 'Manufacture of other furniture, except of stone, concrete or ceramic',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '32110',
      'Description': 'Manufacture of jewellery and related articles',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '32120',
      'Description': 'Manufacture of imitation jewellery and related articles',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '32200',
      'Description': 'Manufacture of musical instruments',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '32300',
      'Description': 'Manufacture of sports goods',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '32400',
      'Description': 'Manufacture of games and toys',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '32500',
      'Description': 'Manufacture of medical and dental instrument and supplies',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '32901',
      'Description': 'Manufacture of stationery',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '32909',
      'Description': 'Other manufacturing n.e.c.',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '33110',
      'Description': 'Repair of fabricated metal products',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '33120',
      'Description': 'Repair and maintenance of industrial machinery and equipment',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '33131',
      'Description':
        'Repair and maintenance of the measuring, testing, navigating and control equipment',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '33132',
      'Description':
        'Repair and maintenance of irradiation, electro medical and electrotherapeutic equipment',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '33133',
      'Description': 'Repair of optical instruments and photographic equipment',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '33140',
      'Description': 'Repair and maintenance of electrical equipment except domestic appliances',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '33150',
      'Description':
        'Repair and maintenance of transport equipment except motorcycles and bicycles',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '33190',
      'Description': 'Repair and maintenance of other equipment n.e.c.',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '33200',
      'Description': 'Installation of industrial machinery and equipment',
      'MSIC Category Reference': 'C',
    },
    {
      'Code': '35101',
      'Description': 'Operation of generation facilities that produce electric energy',
      'MSIC Category Reference': 'D',
    },
    {
      'Code': '35102',
      'Description': 'Operation of transmission, distribution and sales of electricity',
      'MSIC Category Reference': 'D',
    },
    {
      'Code': '35201',
      'Description':
        'Manufacture of gaseous fuels with a specified calorific value, by purification, blending and other processes from gases of various types including natural gas',
      'MSIC Category Reference': 'D',
    },
    {
      'Code': '35202',
      'Description':
        'Transportation, distribution and supply of gaseous fuels of all kinds through a system of mains',
      'MSIC Category Reference': 'D',
    },
    {
      'Code': '35203',
      'Description': 'Sale of gas to the user through mains',
      'MSIC Category Reference': 'D',
    },
    {
      'Code': '35301',
      'Description':
        'Production, collection and distribution of steam and hot water for heating, power and other purposes',
      'MSIC Category Reference': 'D',
    },
    {
      'Code': '35302',
      'Description':
        'Production and distribution of cooled air, chilled water for cooling purposes',
      'MSIC Category Reference': 'D',
    },
    {
      'Code': '35303',
      'Description':
        'Production of ice, including ice for food and non-food (e.g. cooling) purposes',
      'MSIC Category Reference': 'D',
    },
    {
      'Code': '36001',
      'Description': 'Purification and distribution of water for water supply purposes',
      'MSIC Category Reference': 'E',
    },
    {
      'Code': '36002',
      'Description':
        'Desalting of sea or ground water to produce water as the principal product of interest',
      'MSIC Category Reference': 'E',
    },
    {
      'Code': '37000',
      'Description': 'Sewerage and similar activities',
      'MSIC Category Reference': 'E',
    },
    {
      'Code': '38111',
      'Description': 'Collection of non-hazardous solid waste (i.e. garbage) within a local area',
      'MSIC Category Reference': 'E',
    },
    {
      'Code': '38112',
      'Description': 'Collection of recyclable materials',
      'MSIC Category Reference': 'E',
    },
    {
      'Code': '38113',
      'Description': 'Collection of refuse in litter-bins in public places',
      'MSIC Category Reference': 'E',
    },
    {
      'Code': '38114',
      'Description': 'Collection of construction and demolition waste',
      'MSIC Category Reference': 'E',
    },
    {
      'Code': '38115',
      'Description': 'Operation of waste transfer stations for non-hazardous waste',
      'MSIC Category Reference': 'E',
    },
    {
      'Code': '38121',
      'Description': 'Collection of hazardous waste',
      'MSIC Category Reference': 'E',
    },
    {
      'Code': '38122',
      'Description': 'Operation of waste transfer stations for hazardous waste',
      'MSIC Category Reference': 'E',
    },
    {
      'Code': '38210',
      'Description': 'Treatment and disposal of non-hazardous waste',
      'MSIC Category Reference': 'E',
    },
    {
      'Code': '38220',
      'Description': 'Treatment and disposal of hazardous waste',
      'MSIC Category Reference': 'E',
    },
    {
      'Code': '38301',
      'Description': 'Mechanical crushing of metal waste',
      'MSIC Category Reference': 'E',
    },
    {
      'Code': '38302',
      'Description':
        'Dismantling of automobiles, computers, televisions and other equipment for material recover',
      'MSIC Category Reference': 'E',
    },
    {
      'Code': '38303',
      'Description': 'Reclaiming of rubber such as used tires to produce secondary raw material',
      'MSIC Category Reference': 'E',
    },
    {
      'Code': '38304',
      'Description': 'Reuse of rubber products',
      'MSIC Category Reference': 'E',
    },
    {
      'Code': '38309',
      'Description': 'Materials recovery n.e.c.',
      'MSIC Category Reference': 'E',
    },
    {
      'Code': '39000',
      'Description': 'Remediation activities and other waste management services',
      'MSIC Category Reference': 'E',
    },
    {
      'Code': '41001',
      'Description': 'Residential buildings',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '41002',
      'Description': 'Non-residential buildings',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '41003',
      'Description': 'Assembly and erection of prefabricated constructions on the site',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '41009',
      'Description': 'Construction of buildings n.e.c.',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '42101',
      'Description':
        'Construction of motorways, streets, roads, other vehicular and pedestrian ways',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '42102',
      'Description': 'Surface work on streets, roads, highways, bridges or tunnels',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '42103',
      'Description': 'Construction of bridges, including those for elevated highways',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '42104',
      'Description': 'Construction of tunnels',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '42105',
      'Description': 'Construction of railways and subways',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '42106',
      'Description': 'Construction of airfield/airports runways',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '42109',
      'Description': 'Construction of roads and railways n.e.c.',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '42201',
      'Description': 'Long-distance pipelines, communication and power lines',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '42202',
      'Description': 'Urban pipelines, urban communication and power lines; ancillary urban works',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '42203',
      'Description': 'Water main and line construction',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '42204',
      'Description': 'Reservoirs',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '42205',
      'Description': 'Construction of irrigation systems (canals)',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '42206',
      'Description': 'Construction of sewer systems (including repair) and sewage disposal plants',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '42207',
      'Description': 'Construction of power plants',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '42209',
      'Description': 'Construction of utility projects n.e.c.',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '42901',
      'Description': 'Construction of refineries',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '42902',
      'Description':
        'Construction of waterways, harbour and river works, pleasure ports (marinas), locks',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '42903',
      'Description': 'Construction of dams and dykes',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '42904',
      'Description': 'Dredging of waterways',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '42905',
      'Description': 'Outdoor sports facilities',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '42906',
      'Description': 'Land subdivision with land improvement',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '42909',
      'Description': 'Construction of other engineering projects n.e.c.',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43110',
      'Description': 'Demolition or wrecking of buildings and other structures',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43121',
      'Description': 'Clearing of building sites',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43122',
      'Description': 'Earth moving',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43123',
      'Description':
        'Drilling, boring and core sampling for construction, geophysical, geological or similar purposes',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43124',
      'Description': 'Site preparation for mining',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43125',
      'Description': 'Drainage of agricultural or forestry land',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43126',
      'Description': 'Land reclamation work',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43129',
      'Description': 'Other site preparation activities n.e.c.',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43211',
      'Description': 'Electrical wiring and fittings',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43212',
      'Description': 'Telecommunications wiring',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43213',
      'Description': 'Computer network and cable television wiring',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43214',
      'Description': 'Satellite dishes',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43215',
      'Description': 'Lighting systems',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43216',
      'Description': 'Security systems',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43219',
      'Description': 'Electrical installation n.e.c.',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43221',
      'Description': 'Installation of heating systems (electric, gas and oil)',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43222',
      'Description': 'Installation of furnaces, cooling towers',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43223',
      'Description': 'Installation of non-electric solar energy collectors',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43224',
      'Description': 'Installation of plumbing and sanitary equipment',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43225',
      'Description':
        'Installation of ventilation, refrigeration or air-conditioning equipment and ducts',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43226',
      'Description': 'Installation of gas fittings',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43227',
      'Description': 'Installation of fire and lawn sprinkler systems',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43228',
      'Description': 'Steam piping',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43229',
      'Description': 'Plumbing, heat and air-conditioning installation n.e.c.',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43291',
      'Description':
        'Installation of elevators, escalators in buildings or other construction projects',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43292',
      'Description':
        'Installation of automated and revolving doors in buildings or other construction projects',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43293',
      'Description':
        'Installation of lighting conductors in buildings or other construction projects',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43294',
      'Description':
        'Installation vacuum cleaning systems in buildings or other construction projects',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43295',
      'Description':
        'Installation thermal, sound or vibration insulation in buildings or other construction projects',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43299',
      'Description': 'Other construction installation n.e.c.',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43301',
      'Description':
        'Installation of doors, windows, door and window frames of wood or other materials, fitted kitchens, staircases, shop fittings and furniture',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43302',
      'Description':
        'Laying, tiling, hanging or fitting in buildings or other construction projects of various types of materials',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43303',
      'Description': 'Interior and exterior painting of buildings',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43304',
      'Description': 'Painting of civil engineering structures',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43305',
      'Description': 'Installation of glass, mirrors',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43306',
      'Description': 'Interior completion',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43307',
      'Description': 'Cleaning of new buildings after construction',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43309',
      'Description': 'Other building completion and finishing work n.e.c.',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43901',
      'Description': 'Construction of foundations, including pile driving',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43902',
      'Description': 'Erection of non-self-manufactured steel elements',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43903',
      'Description': 'Scaffolds and work platform erecting and dismantling',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43904',
      'Description': 'Bricklaying and stone setting',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43905',
      'Description': 'Construction of outdoor swimming pools',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43906',
      'Description': 'Steam cleaning, sand blasting and similar activities for building exteriors',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43907',
      'Description': 'Renting of construction machinery and equipment with operator (e.g. cranes)',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '43909',
      'Description': 'Other specialized construction activities, n.e.c.',
      'MSIC Category Reference': 'F',
    },
    {
      'Code': '45101',
      'Description': 'Wholesale and retail of new motor vehicles',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '45102',
      'Description': 'Wholesale and retail of used motor vehicles',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '45103',
      'Description': 'Sale of industrial, commercial and agriculture vehicles – new',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '45104',
      'Description': 'Sale of industrial, commercial and agriculture vehicles – used',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '45105',
      'Description': 'Sale by commission agents',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '45106',
      'Description': 'Car auctions',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '45109',
      'Description': 'Sale of other motor vehicles n.e.c.',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '45201',
      'Description': 'Maintenance and repair of motor vehicles',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '45202',
      'Description': 'Spraying and painting',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '45203',
      'Description': 'Washing and polishing (car wash)',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '45204',
      'Description': 'Repair of motor vehicle seats',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '45205',
      'Description':
        'Installation of parts and accessories not as part of the manufacturing process',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '45300',
      'Description':
        'Wholesale and retail sale of all kinds of parts, components, supplies, tools and accessories for motor vehicles',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '45401',
      'Description': 'Wholesale and retail sale of motorcycles',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '45402',
      'Description': 'Wholesale and retail sale of parts and accessories for motorcycles',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '45403',
      'Description': 'Repair and maintenance of motorcycles',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46100',
      'Description': 'Wholesale on a fee or contract basis',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46201',
      'Description': 'Wholesale of rubber',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46202',
      'Description': 'Wholesale of palm oil',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46203',
      'Description': 'Wholesale of lumber and timber',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46204',
      'Description': 'Wholesale of flowers and plants',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46205',
      'Description': 'Wholesale of livestock',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46209',
      'Description': 'Wholesale of agricultural raw material and live animal n.e.c.',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46311',
      'Description': 'Wholesale of meat, poultry and eggs',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46312',
      'Description': 'Wholesale of fish and other seafood',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46313',
      'Description': 'Wholesale of fruits',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46314',
      'Description': 'Wholesale of vegetables',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46319',
      'Description': 'Wholesale of meat, fish, fruits and vegetables n.e.c.',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46321',
      'Description': 'Wholesale of rice, other grains, flour and sugars',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46322',
      'Description': 'Wholesale of dairy products',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46323',
      'Description': 'Wholesale of confectionary',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46324',
      'Description': 'Wholesale of  biscuits, cakes, breads and other bakery products',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46325',
      'Description': 'Wholesale of coffee, tea, cocoa and other beverages',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46326',
      'Description': 'Wholesale of beer, wine and spirits',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46327',
      'Description': 'Wholesale of tobacco, cigar, cigarettes',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46329',
      'Description': 'Wholesale of other foodstuffs',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46411',
      'Description': 'Wholesale of yarn and fabrics',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46412',
      'Description': 'Wholesale of household linen, towels, blankets',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46413',
      'Description': 'Wholesale of clothing',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46414',
      'Description': 'Wholesale of clothing accessories',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46415',
      'Description': 'Wholesale of fur articles',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46416',
      'Description': 'Wholesale of footwear',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46417',
      'Description': 'Wholesale of haberdashery',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46419',
      'Description': 'Wholesale of textiles, clothing n.e.c.',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46421',
      'Description': 'Wholesale of pharmaceutical and medical goods',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46422',
      'Description': 'Wholesale of perfumeries, cosmetics, soap and toiletries',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46431',
      'Description': 'Wholesale of bicycles and their parts and accessories',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46432',
      'Description': 'Wholesale of photographic and optical goods',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46433',
      'Description': 'Wholesale of leather goods and travel accessories',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46434',
      'Description': 'Wholesale of musical instruments, games and toys, sports goods',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46441',
      'Description': 'Wholesale of handicrafts and artificial flowers',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46442',
      'Description': 'Wholesale of cut flowers and plants',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46443',
      'Description': 'Wholesale of watches and clocks',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46444',
      'Description': 'Wholesale of jewellery',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46491',
      'Description': 'Wholesale of household furniture',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46492',
      'Description': 'Wholesale of household appliances',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46493',
      'Description': 'Wholesale of lighting equipment',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46494',
      'Description':
        'Wholesale of household utensils and cutlery, crockery, glassware, chinaware and pottery',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46495',
      'Description': 'Wholesale of woodenware, wickerwork and corkware',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46496',
      'Description': 'Wholesale of electrical and electronic goods',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46497',
      'Description': 'Wholesale of stationery, books, magazines and newspapers',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46499',
      'Description': 'Wholesale of other household goods n.e.c.',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46510',
      'Description': 'Wholesale of computer hardware, software and peripherals',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46521',
      'Description': 'Wholesale of telephone and telecommunications equipment, cell phones, pagers',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46522',
      'Description': 'Wholesale of electronic components and wiring accessories',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46531',
      'Description': 'Wholesale of agricultural machinery, equipment and supplies',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46532',
      'Description': 'Wholesale of lawn mowers however operated',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46591',
      'Description':
        'Wholesale of office machinery and business equipment, except computers and computer peripheral equipment',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46592',
      'Description': 'Wholesale of office furniture',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46593',
      'Description': 'Wholesale of computer-controlled machines tools',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46594',
      'Description': 'Wholesale of industrial machinery, equipment and supplies',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46595',
      'Description': 'Wholesale of construction and civil engineering machinery and equipment',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46596',
      'Description':
        'Wholesale of lift escalators, air-conditioning, security and fire fighting equipment',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46599',
      'Description':
        'Wholesale of other machinery for use in industry, trade and navigation and other services n.e.c.',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46611',
      'Description': 'Wholesale of petrol, diesel, lubricants',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46612',
      'Description': 'Wholesale of liquefied petroleum gas',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46619',
      'Description':
        'Wholesale of other solid, liquid and gaseous fuels and related products n.e.c.',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46621',
      'Description': 'Wholesale of ferrous and non-ferrous metal ores and metals',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46622',
      'Description':
        'Wholesale of ferrous and non-ferrous semi-finished metal ores and products n.e.c.',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46631',
      'Description': 'Wholesale of logs, sawn timber, plywood, veneer and related products',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46632',
      'Description': 'Wholesale of paints and varnish',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46633',
      'Description': 'Wholesale of construction materials',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46634',
      'Description': 'Wholesale of fittings and fixtures',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46635',
      'Description': 'Wholesale of hot water heaters',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46636',
      'Description': 'Wholesale of sanitary installation and equipment',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46637',
      'Description': 'Wholesale of tools',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46639',
      'Description':
        'Wholesale of other construction materials, hardware, plumbing and heating equipment and supplies n.e.c.',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46691',
      'Description': 'Wholesale of industrial chemicals',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46692',
      'Description': 'Wholesale of fertilizers and agrochemical products',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46693',
      'Description': 'Wholesale of plastic materials in primary forms',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46694',
      'Description': 'Wholesale of rubber scrap',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46695',
      'Description': 'Wholesale of textile fibres',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46696',
      'Description': 'Wholesale of paper in bulk, packaging materials',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46697',
      'Description': 'Wholesale of precious stones',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46698',
      'Description': 'Wholesale of metal and non-metal waste and scrap and materials for recycling',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46699',
      'Description':
        'Dismantling of automobiles, computer, televisions and other equipment to obtain and re-sell usable parts',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46901',
      'Description': 'Wholesale of aquarium fishes, pet birds and animals',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46902',
      'Description': 'Wholesale of animal/pet food',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '46909',
      'Description': 'Wholesale of a variety of goods without any particular specialization n.e.c.',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47111',
      'Description': 'Provision stores',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47112',
      'Description': 'Supermarket',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47113',
      'Description': 'Mini market',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47114',
      'Description': 'Convenience stores',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47191',
      'Description': 'Department stores',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47192',
      'Description': 'Department stores and supermarket',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47193',
      'Description': 'Hypermarket',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47194',
      'Description': 'News agent and miscellaneous goods store',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47199',
      'Description': 'Other retail sale in non-specialized stores n.e.c.',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47211',
      'Description': 'Retail sale of rice, flour, other grains and sugars',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47212',
      'Description': 'Retail sale of fresh or preserved vegetables and fruits',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47213',
      'Description': 'Retail sale of dairy products and eggs',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47214',
      'Description': 'Retail sale of meat and meat products (including poultry)',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47215',
      'Description': 'Retail sale of fish, other seafood and products thereof',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47216',
      'Description': 'Retail sale of bakery products and sugar confectionery',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47217',
      'Description':
        'Retail sale of mee, kuey teow, mee hoon, wantan skins and other food products made from flour or soya',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47219',
      'Description': 'Retail sale of other food products n.e.c.',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47221',
      'Description': 'Retail sale of beer, wine and spirits',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47222',
      'Description': 'Retail sale of tea, coffee, soft drinks, mineral water and other beverages',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47230',
      'Description': 'Retail sale of tobacco products in specialized store',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47300',
      'Description': 'Retail sale of automotive fuel in specialized stores',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47412',
      'Description': 'Retail sale of video game consoles and non-customized software',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47413',
      'Description': 'Retail sale of telecommunication equipment',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47420',
      'Description': 'Retail sale of audio and video equipment in specialized store',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47510',
      'Description': 'Retail sale of textiles in specialized stores',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47531',
      'Description': 'Retail sale of carpets and rugs',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47532',
      'Description': 'Retail sale of curtains and net curtains',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47533',
      'Description': 'Retail sale of wallpaper and floor coverings',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47591',
      'Description': 'Retail sale of household furniture',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47592',
      'Description': 'Retail sale of articles for lighting',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47593',
      'Description':
        'Retail sale of household utensils and cutlery, crockery, glassware, chinaware and pottery',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47594',
      'Description': 'Retail sale of wood, cork goods and wickerwork goods',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47595',
      'Description': 'Retail sale of household appliances',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47596',
      'Description': 'Retail sale of musical instruments and scores',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47597',
      'Description': 'Retail sale of security systems',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47598',
      'Description': 'Retail sale of household articles and equipment n.e.c.',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47611',
      'Description': 'Retail sale of office supplies and equipment',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47612',
      'Description': 'Retail sale of books, newspapers and stationary',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47631',
      'Description': 'Retail sale of sports goods and equipments',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47632',
      'Description': 'Retail sale of fishing equipment',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47633',
      'Description': 'Retail sale of camping goods',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47634',
      'Description': 'Retail sale of boats and equipments',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47635',
      'Description': 'Retail sale of bicycles and related parts and accessories',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47640',
      'Description': 'Retail sale of games and toys, made of all materials',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47711',
      'Description':
        'Retail sale of articles of clothing, articles of fur and clothing accessories',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47712',
      'Description': 'Retail sale of footwear',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47713',
      'Description': 'Retail sale of leather goods, accessories of leather and leather substitutes',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47721',
      'Description':
        'Stores specialized in retail sale of pharmaceuticals, medical and orthopaedic goods',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47722',
      'Description': 'Stores specialized in retail sale of perfumery, cosmetic and toilet articles',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47731',
      'Description': 'Retail sale of photographic and precision equipment',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47732',
      'Description': 'Retail sale of watches and clocks',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47733',
      'Description': 'Retail sale of jewellery',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47734',
      'Description': 'Retail sale of flowers, plants, seeds, fertilizers',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47735',
      'Description': 'Retail sale of souvenirs, craftwork and religious articles',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47736',
      'Description': 'Retail sale of household fuel oil, cooking gas, coal and fuel wood',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47737',
      'Description': 'Retail sale of spectacles and other optical goods',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47738',
      'Description': 'Retail sale of aquarium fishes, pet animals and pet food',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47739',
      'Description': 'Other retail sale of new goods in specialized stores n.e.c.',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47741',
      'Description': 'Retail sale of second-hand books',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47742',
      'Description': 'Retail sale of second-hand electrical and electronic goods',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47743',
      'Description': 'Retail sale of antiques',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47744',
      'Description': 'Activities of auctioning houses (retail)',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47749',
      'Description': 'Retail sale of second-hand goods n.e.c.',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47810',
      'Description': 'Retail sale of food, beverages and tobacco products via stalls or markets',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47820',
      'Description': 'Retail sale of textiles, clothing and footwear via stalls or markets',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47891',
      'Description': 'Retail sale of carpets and rugs via stalls or markets',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47893',
      'Description': 'Retail sale of games and toys via stalls or markets',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47894',
      'Description':
        'Retail sale of household appliances and consumer electronics via stall or markets',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47895',
      'Description': 'Retail sale of music and video recordings via stall or markets',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47911',
      'Description': 'Retail sale of any kind of product by mail order',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47912',
      'Description': 'Retail sale of any kind of product over the Internet',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47913',
      'Description': 'Direct sale via television, radio and telephone',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47914',
      'Description': 'Internet retail auctions',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47992',
      'Description': 'Retail sale of any kind of product through vending machines',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '47999',
      'Description': 'Other retail sale not in stores, stalls or markets n.e.c.',
      'MSIC Category Reference': 'G',
    },
    {
      'Code': '49110',
      'Description': 'Passenger transport by inter-urban railways',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '49120',
      'Description': 'Freight transport by inter-urban, suburban and urban railways',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '49211',
      'Description': 'City bus services',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '49212',
      'Description': 'Urban and suburban railway passenger transport service',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '49221',
      'Description': 'Express bus services',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '49222',
      'Description': 'Employees bus services',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '49223',
      'Description': 'School bus services',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '49224',
      'Description': 'Taxi operation and limousine services',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '49225',
      'Description': 'Rental of cars with driver',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '49229',
      'Description': 'Other passenger land transport n.e.c.',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '49230',
      'Description': 'Freight transport by road',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '49300',
      'Description': 'Transport via pipeline',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '50111',
      'Description': 'Operation of excursion, cruise or sightseeing boats',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '50112',
      'Description': 'Operation of ferries, water taxis',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '50113',
      'Description': 'Rental of pleasure boats with crew for sea and coastal water transport',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '50121',
      'Description': 'Transport of freight overseas and coastal waters, whether scheduled or not',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '50122',
      'Description': 'Transport by towing or pushing of barges, oil rigs',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '50211',
      'Description': 'Transport of passenger via rivers, canals, lakes and other inland waterways',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '50212',
      'Description': 'Rental of pleasure boats with crew for inland water transport',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '50220',
      'Description': 'Transport of freight via rivers, canals, lakes and other inland waterways',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '51101',
      'Description': 'Transport of passengers by air over regular routes and on regular schedules',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '51102',
      'Description': 'Non-scheduled transport of passenger by air',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '51103',
      'Description':
        'Renting of air-transport equipment with operator for the purpose of passenger transportation',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '51201',
      'Description': 'Transport freight by air over regular routes and on regular schedules',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '51202',
      'Description': 'Non-scheduled transport of freight by air',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '51203',
      'Description':
        'Renting of air-transport equipment with operator for the purpose of freight transportation',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '52100',
      'Description': 'Warehousing and storage services',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '52211',
      'Description': 'Operation of terminal facilities',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '52212',
      'Description': 'Towing and road side assistance',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '52213',
      'Description': 'Operation of parking facilities for motor vehicles (parking lots)',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '52214',
      'Description': 'Highway, bridge and tunnel operation services',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '52219',
      'Description': 'Other service activities incidental to land transportation n.e.c.',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '52221',
      'Description': 'Port, harbours and piers operation services',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '52222',
      'Description': 'Vessel salvage and refloating services',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '52229',
      'Description': 'Other service activities incidental to water transportation n.e.c.',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '52231',
      'Description': 'Operation of terminal facilities',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '52232',
      'Description': 'Airport and air-traffic-control activities',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '52233',
      'Description': 'Ground service activities on airfields',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '52234',
      'Description': 'Fire fighting and fire-prevention services at airports',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '52239',
      'Description': 'Other service activities incidental to air transportation n.e.c.',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '52241',
      'Description': 'Stevedoring services',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '52249',
      'Description': 'Other cargo handling activities n.e.c.',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '52291',
      'Description': 'Forwarding of freight',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '52292',
      'Description': 'Brokerage for ship and aircraft space',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '52299',
      'Description': 'Other transportation support activities n.e.c.',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '53100',
      'Description': 'National postal services',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '53200',
      'Description': 'Courier activities other than national post activities',
      'MSIC Category Reference': 'H',
    },
    {
      'Code': '55101',
      'Description': 'Hotels and resort hotels',
      'MSIC Category Reference': 'I',
    },
    {
      'Code': '55102',
      'Description': 'Motels',
      'MSIC Category Reference': 'I',
    },
    {
      'Code': '55103',
      'Description': 'Apartment hotels',
      'MSIC Category Reference': 'I',
    },
    {
      'Code': '55104',
      'Description': 'Chalets',
      'MSIC Category Reference': 'I',
    },
    {
      'Code': '55105',
      'Description': 'Rest house/guest house',
      'MSIC Category Reference': 'I',
    },
    {
      'Code': '55106',
      'Description': 'Bed and breakfast units',
      'MSIC Category Reference': 'I',
    },
    {
      'Code': '55107',
      'Description': 'Hostels',
      'MSIC Category Reference': 'I',
    },
    {
      'Code': '55108',
      'Description': 'Home stay',
      'MSIC Category Reference': 'I',
    },
    {
      'Code': '55109',
      'Description': 'Other short term accommodation activities n.e.c.',
      'MSIC Category Reference': 'I',
    },
    {
      'Code': '55200',
      'Description': 'Camping grounds, recreational vehicle parks and trailer parks',
      'MSIC Category Reference': 'I',
    },
    {
      'Code': '55900',
      'Description': 'Other accommodation',
      'MSIC Category Reference': 'I',
    },
    {
      'Code': '56103',
      'Description': 'Fast-food restaurants',
      'MSIC Category Reference': 'I',
    },
    {
      'Code': '56104',
      'Description': 'Ice cream truck vendors and parlours',
      'MSIC Category Reference': 'I',
    },
    {
      'Code': '56105',
      'Description': 'Mobile food carts',
      'MSIC Category Reference': 'I',
    },
    {
      'Code': '56106',
      'Description': 'Food stalls/hawkers',
      'MSIC Category Reference': 'I',
    },
    {
      'Code': '56107',
      'Description': 'Food or beverage, food and beverage preparation in market stalls/hawkers',
      'MSIC Category Reference': 'I',
    },
    {
      'Code': '56210',
      'Description': 'Event/food caterers',
      'MSIC Category Reference': 'I',
    },
    {
      'Code': '56290',
      'Description': 'Other food service activities',
      'MSIC Category Reference': 'I',
    },
    {
      'Code': '56301',
      'Description': 'Pubs, bars, discotheques, coffee houses, cocktail lounges and karaoke',
      'MSIC Category Reference': 'I',
    },
    {
      'Code': '56302',
      'Description': 'Coffee shops',
      'MSIC Category Reference': 'I',
    },
    {
      'Code': '56303',
      'Description': 'Drink stalls/hawkers',
      'MSIC Category Reference': 'I',
    },
    {
      'Code': '56304',
      'Description': 'Mobile beverage',
      'MSIC Category Reference': 'I',
    },
    {
      'Code': '56309',
      'Description': 'Others drinking places n.e.c.',
      'MSIC Category Reference': 'I',
    },
    {
      'Code': '58110',
      'Description': 'Publishing of books, brochures and other publications',
      'MSIC Category Reference': 'J',
    },
    {
      'Code': '58120',
      'Description': 'Publishing of mailing lists, telephone book, other directories',
      'MSIC Category Reference': 'J',
    },
    {
      'Code': '58130',
      'Description':
        'Publishing of newspapers, journals, magazines and periodicals in print or electronic form',
      'MSIC Category Reference': 'J',
    },
    {
      'Code': '58190',
      'Description':
        'Publishing of catalogues, photos, engraving and postcards, greeting cards, forms, posters, reproduction of works of art, advertising material and other printed matter n.e.c.',
      'MSIC Category Reference': 'J',
    },
    {
      'Code': '58201',
      'Description': 'Business and other applications',
      'MSIC Category Reference': 'J',
    },
    {
      'Code': '58202',
      'Description': 'Computer games for all platforms',
      'MSIC Category Reference': 'J',
    },
    {
      'Code': '58203',
      'Description': 'Operating systems',
      'MSIC Category Reference': 'J',
    },
    {
      'Code': '59110',
      'Description': 'Motion picture, video and television programme production activities',
      'MSIC Category Reference': 'J',
    },
    {
      'Code': '59120',
      'Description': 'Motion picture, video and television programme post-production activities',
      'MSIC Category Reference': 'J',
    },
    {
      'Code': '59130',
      'Description': 'Motion picture, video and television programme distribution activities',
      'MSIC Category Reference': 'J',
    },
    {
      'Code': '59140',
      'Description': 'Motion picture projection activities',
      'MSIC Category Reference': 'J',
    },
    {
      'Code': '59200',
      'Description': 'Sound recording and music publishing activities',
      'MSIC Category Reference': 'J',
    },
    {
      'Code': '60100',
      'Description': 'Radio broadcasting',
      'MSIC Category Reference': 'J',
    },
    {
      'Code': '60200',
      'Description': 'Television programming and broadcasting activities',
      'MSIC Category Reference': 'J',
    },
    {
      'Code': '61101',
      'Description': 'Wired telecommunications services',
      'MSIC Category Reference': 'J',
    },
    {
      'Code': '61102',
      'Description': 'Internet access providers by the operator of the wired infrastructure',
      'MSIC Category Reference': 'J',
    },
    {
      'Code': '61201',
      'Description': 'Wireless telecommunications services',
      'MSIC Category Reference': 'J',
    },
    {
      'Code': '61202',
      'Description': 'Internet access providers by the operator of the wireless infrastructure',
      'MSIC Category Reference': 'J',
    },
    {
      'Code': '61300',
      'Description': 'Satellite telecommunications services',
      'MSIC Category Reference': 'J',
    },
    {
      'Code': '61901',
      'Description':
        'Provision of Internet access over networks between the client and the ISP not owned or controlled by the ISP',
      'MSIC Category Reference': 'J',
    },
    {
      'Code': '61902',
      'Description': 'Provision of telecommunications services over existing telecom connection',
      'MSIC Category Reference': 'J',
    },
    {
      'Code': '61903',
      'Description': 'Telecommunications resellers',
      'MSIC Category Reference': 'J',
    },
    {
      'Code': '61904',
      'Description':
        'Provision of telecommunications services over existing telecom connections VOIP (Voice Over Internet Protocol) provision',
      'MSIC Category Reference': 'J',
    },
    {
      'Code': '61905',
      'Description': 'Provision of specialized telecommunications applications',
      'MSIC Category Reference': 'J',
    },
    {
      'Code': '61909',
      'Description': 'Other telecommunications activities n.e.c.',
      'MSIC Category Reference': 'J',
    },
    {
      'Code': '62010',
      'Description': 'Computer programming activities',
      'MSIC Category Reference': 'J',
    },
    {
      'Code': '62021',
      'Description': 'Computer consultancy',
      'MSIC Category Reference': 'J',
    },
    {
      'Code': '62022',
      'Description': 'Computer facilities management activities',
      'MSIC Category Reference': 'J',
    },
    {
      'Code': '62091',
      'Description': 'Information Communication Technology (ICT) system security',
      'MSIC Category Reference': 'J',
    },
    {
      'Code': '62099',
      'Description': 'Other information technology service activities n.e.c.',
      'MSIC Category Reference': 'J',
    },
    {
      'Code': '63111',
      'Description':
        'Activities of providing infrastructure for hosting, data processing services and related activities',
      'MSIC Category Reference': 'J',
    },
    {
      'Code': '63112',
      'Description': 'Data processing activities',
      'MSIC Category Reference': 'J',
    },
    {
      'Code': '63120',
      'Description': 'Web portals',
      'MSIC Category Reference': 'J',
    },
    {
      'Code': '63910',
      'Description': 'News syndicate and news agency activities',
      'MSIC Category Reference': 'J',
    },
    {
      'Code': '63990',
      'Description': 'Other information service activities n.e.c.',
      'MSIC Category Reference': 'J',
    },
    {
      'Code': '64110',
      'Description': 'Central banking',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '64191',
      'Description': 'Commercial Banks',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '64192',
      'Description': 'Islamic Banks',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '64193',
      'Description': 'Offshore Banks',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '64194',
      'Description': 'Investment Banks',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '64195',
      'Description': 'Development financial institutions (with deposit taking functions)',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '64199',
      'Description': 'Other monetary intermediation (with deposit taking functions) n.e.c.',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '64200',
      'Description': 'Activities of holding companies',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '64301',
      'Description': 'Venture capital companies',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '64302',
      'Description': 'Unit trust fund excludes REITs',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '64303',
      'Description': 'Property unit trust (REITs)',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '64304',
      'Description': 'Other administration of trusts accounts',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '64309',
      'Description': 'Trusts, funds and similar financial entities n.e.c.',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '64910',
      'Description': 'Financial leasing activities',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '64921',
      'Description': 'Development financial institutions (without deposit taking functions)',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '64922',
      'Description': 'Credit card services',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '64923',
      'Description': 'Licensed money lending activities',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '64924',
      'Description': 'Pawnshops and pawnbrokers includes Ar-Rahnu',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '64925',
      'Description': 'Co-operative with credits functions',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '64929',
      'Description': 'Other credit granting n.e.c.',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '64991',
      'Description': 'Factoring companies',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '64992',
      'Description': 'Representative office of foreign banks',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '64993',
      'Description': 'Nominee companies',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '64999',
      'Description':
        'Other financial service activities, except insurance/takaful and pension funding n.e.c.',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '65111',
      'Description': 'Life insurance',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '65112',
      'Description': 'Family takaful',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '65121',
      'Description': 'General insurance',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '65122',
      'Description': 'General takaful',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '65123',
      'Description': 'Composite insurance',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '65124',
      'Description': 'Offshore insurance',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '65125',
      'Description': 'Offshore takaful',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '65201',
      'Description': 'Life reinsurance',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '65202',
      'Description': 'Family retakaful',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '65203',
      'Description': 'General reinsurance',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '65204',
      'Description': 'General retakaful',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '65205',
      'Description': 'Composite retakaful',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '65206',
      'Description': 'Offshore reinsurance',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '65207',
      'Description': 'Offshore retakaful',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '65301',
      'Description': 'Pension funding',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '65302',
      'Description': 'Provident funding',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '66111',
      'Description': 'Stock exchanges',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '66112',
      'Description': 'Exchanges for commodity contracts',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '66113',
      'Description': 'Securities exchange',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '66114',
      'Description': 'Exchanges for commodity futures contracts',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '66119',
      'Description': 'Administration of financial markets n.e.c.',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '66121',
      'Description': 'Stock, share and bond brokers',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '66122',
      'Description': 'Commodity brokers and dealers',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '66123',
      'Description': 'Gold bullion dealers',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '66124',
      'Description': 'Foreign exchange broker and dealers (Bureaux de change)',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '66125',
      'Description': 'Money-changing services',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '66129',
      'Description': 'Other financial and commodity futures brokers and dealers',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '66191',
      'Description': 'Investment advisory services',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '66192',
      'Description': 'Financial consultancy services',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '66199',
      'Description': 'Activities auxiliary to finance n.e.c.',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '66211',
      'Description': 'Insurance adjusting service',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '66212',
      'Description': 'Takaful adjusting service',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '66221',
      'Description': 'Insurance agents',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '66222',
      'Description': 'Takaful agents',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '66223',
      'Description': 'Insurance brokers',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '66224',
      'Description': 'Takaful brokers',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '66290',
      'Description': 'Other activities auxiliary to insurance, takaful and pension funding',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '66301',
      'Description': 'Management of pension funds',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '66302',
      'Description': 'Assets/portfolio management',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '66303',
      'Description': 'Unit trust management companies',
      'MSIC Category Reference': 'K',
    },
    {
      'Code': '68101',
      'Description':
        'Buying, selling, renting and operating of self-owned or leased real estate – residential buildings',
      'MSIC Category Reference': 'L',
    },
    {
      'Code': '68102',
      'Description':
        'Buying, selling, renting and operating of self-owned or leased real estate – non-residential buildings',
      'MSIC Category Reference': 'L',
    },
    {
      'Code': '68103',
      'Description':
        'Buying, selling, renting and operating of self-owned or leased real estate – land',
      'MSIC Category Reference': 'L',
    },
    {
      'Code': '68104',
      'Description':
        'Development of building projects for own operation, i.e. for renting of space in these buildings',
      'MSIC Category Reference': 'L',
    },
    {
      'Code': '68109',
      'Description': 'Real estate activities with own or leased property n.e.c.',
      'MSIC Category Reference': 'L',
    },
    {
      'Code': '68201',
      'Description':
        'Activities of real estate agents and brokers for buying, selling and renting of real estate',
      'MSIC Category Reference': 'L',
    },
    {
      'Code': '68202',
      'Description': 'Management of real estate on a fee or contract basis',
      'MSIC Category Reference': 'L',
    },
    {
      'Code': '68203',
      'Description': 'Appraisal services for real estate',
      'MSIC Category Reference': 'L',
    },
    {
      'Code': '68209',
      'Description': 'Real estate activities on a fee or contract basis n.e.c.',
      'MSIC Category Reference': 'L',
    },
    {
      'Code': '69100',
      'Description': 'Legal activities',
      'MSIC Category Reference': 'M',
    },
    {
      'Code': '69200',
      'Description': 'Accounting, bookkeeping and auditing activities; tax consultancy',
      'MSIC Category Reference': 'M',
    },
    {
      'Code': '70100',
      'Description': 'Activities of head offices',
      'MSIC Category Reference': 'M',
    },
    {
      'Code': '70201',
      'Description': 'Business management consultancy services',
      'MSIC Category Reference': 'M',
    },
    {
      'Code': '70202',
      'Description': 'Human resource consultancy services',
      'MSIC Category Reference': 'M',
    },
    {
      'Code': '70203',
      'Description': 'Consultancy services in public relation and communications',
      'MSIC Category Reference': 'M',
    },
    {
      'Code': '70209',
      'Description': 'Other management consultancy activities n.e.c',
      'MSIC Category Reference': 'M',
    },
    {
      'Code': '71101',
      'Description': 'Architectural services',
      'MSIC Category Reference': 'M',
    },
    {
      'Code': '71102',
      'Description': 'Engineering services',
      'MSIC Category Reference': 'M',
    },
    {
      'Code': '71103',
      'Description': 'Land surveying services',
      'MSIC Category Reference': 'M',
    },
    {
      'Code': '71109',
      'Description':
        'Other architectural and engineering activities and related technical consultancy n.e.c.',
      'MSIC Category Reference': 'M',
    },
    {
      'Code': '71200',
      'Description': 'Technical testing and analysis',
      'MSIC Category Reference': 'M',
    },
    {
      'Code': '72101',
      'Description': 'Research and development on natural sciences',
      'MSIC Category Reference': 'M',
    },
    {
      'Code': '72102',
      'Description': 'Research and development on engineering and technology',
      'MSIC Category Reference': 'M',
    },
    {
      'Code': '72103',
      'Description': 'Research and development on medical sciences',
      'MSIC Category Reference': 'M',
    },
    {
      'Code': '72104',
      'Description': 'Research and development on biotechnology',
      'MSIC Category Reference': 'M',
    },
    {
      'Code': '72105',
      'Description': 'Research and development on agricultural sciences',
      'MSIC Category Reference': 'M',
    },
    {
      'Code': '72106',
      'Description': 'Research and development on Information Communication Technology (ICT)',
      'MSIC Category Reference': 'M',
    },
    {
      'Code': '72109',
      'Description': 'Research and development on other natural science and engineering n.e.c.',
      'MSIC Category Reference': 'M',
    },
    {
      'Code': '72201',
      'Description': 'Research and development on social sciences',
      'MSIC Category Reference': 'M',
    },
    {
      'Code': '72202',
      'Description': 'Research and development on humanities',
      'MSIC Category Reference': 'M',
    },
    {
      'Code': '72209',
      'Description': 'Research and development of other social sciences and humanities n.e.c.',
      'MSIC Category Reference': 'M',
    },
    {
      'Code': '73100',
      'Description': 'Advertising',
      'MSIC Category Reference': 'M',
    },
    {
      'Code': '73200',
      'Description': 'Market research and public opinion polling',
      'MSIC Category Reference': 'M',
    },
    {
      'Code': '74101',
      'Description': 'Activities of interior decorators',
      'MSIC Category Reference': 'M',
    },
    {
      'Code': '74102',
      'Description': 'Services of graphic designers',
      'MSIC Category Reference': 'M',
    },
    {
      'Code': '74103',
      'Description': 'Fashion design services',
      'MSIC Category Reference': 'M',
    },
    {
      'Code': '74109',
      'Description': 'Specialized design activities n.e.c.',
      'MSIC Category Reference': 'M',
    },
    {
      'Code': '74200',
      'Description': 'Photographic activities',
      'MSIC Category Reference': 'M',
    },
    {
      'Code': '74901',
      'Description': 'Translation and interpretation activities',
      'MSIC Category Reference': 'M',
    },
    {
      'Code': '74902',
      'Description': 'Business brokerage activities',
      'MSIC Category Reference': 'M',
    },
    {
      'Code': '74903',
      'Description': 'Security consulting',
      'MSIC Category Reference': 'M',
    },
    {
      'Code': '74904',
      'Description': 'Activities of quantity surveyors',
      'MSIC Category Reference': 'M',
    },
    {
      'Code': '74905',
      'Description':
        'Activities of consultants other than architecture, engineering and management consultants',
      'MSIC Category Reference': 'M',
    },
    {
      'Code': '74909',
      'Description': 'Any other professional, scientific and technical activities n.e.c.',
      'MSIC Category Reference': 'M',
    },
    {
      'Code': '75000',
      'Description': 'VETERINARY ACTIVITIES',
      'MSIC Category Reference': 'M',
    },
    {
      'Code': '77101',
      'Description': 'Renting and operational leasing of passenger cars (without driver)',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '77102',
      'Description':
        'Renting and operational leasing of trucks, utility trailers and recreational vehicles',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '77211',
      'Description': 'Renting and leasing of pleasure boats, canoes, sailboats',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '77212',
      'Description': 'Renting and leasing of bicycles',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '77213',
      'Description': 'Renting and leasing of beach chairs and umbrellas',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '77219',
      'Description': 'Renting and leasing of other sports equipment n.e.c.',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '77220',
      'Description': 'Renting of video tapes, records, CDs, DVDs',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '77291',
      'Description': 'Renting and leasing of textiles, wearing apparel and footwear',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '77292',
      'Description':
        'Renting and leasing of furniture, pottery and glass, kitchen and tableware, electrical appliances and house wares',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '77293',
      'Description': 'Renting and leasing of jewellery, musical instruments, scenery and costumes',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '77294',
      'Description': 'Renting and leasing of books, journals and magazines',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '77295',
      'Description':
        'Renting and leasing of machinery and equipment used by amateurs or as a hobby',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '77296',
      'Description': 'Renting of flowers and plants',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '77297',
      'Description': 'Renting and leasing of electronic equipment for household use',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '77299',
      'Description': 'Renting and leasing of other personal and household goods n.e.c.',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '77301',
      'Description':
        'Renting and operational leasing, without operator, of other machinery and equipment that are generally used as capital goods by industries',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '77302',
      'Description':
        'Renting and operational leasing of land-transport equipment (other than motor vehicles) without drivers',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '77303',
      'Description':
        'Renting and operational leasing of water-transport equipment without operator',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '77304',
      'Description': 'Renting and operational leasing of air transport equipment without operator',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '77305',
      'Description':
        'Renting and operational leasing of agricultural and forestry machinery and equipment without operator',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '77306',
      'Description':
        'Renting and operational leasing of construction and civil-engineering machinery and equipment without operator',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '77307',
      'Description':
        'Rental and operational leasing of office machinery and equipment without operator',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '77309',
      'Description': 'Renting and leasing of other machinery, equipment and tangible goods n.e.c.',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '77400',
      'Description':
        'Leasing of intellectual property and similar products, except copyrighted works',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '78100',
      'Description': 'Activities of employment placement agencies',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '78200',
      'Description': 'Temporary employment agency activities',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '78300',
      'Description': 'Provision of human resources for client businesses',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '79110',
      'Description': 'Travel agency activities',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '79120',
      'Description': 'Tour operator activities',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '79900',
      'Description': 'Other reservation service and related activities',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '80100',
      'Description': 'Private security activities',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '80200',
      'Description': 'Security systems service activities',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '80300',
      'Description': 'Investigation and detective activities',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '81100',
      'Description': 'Combined facilities support activities',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '81210',
      'Description': 'General cleaning of buildings',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '81291',
      'Description': 'Cleaning of buildings of all types',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '81292',
      'Description': 'Swimming pool cleaning and maintenance services',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '81293',
      'Description': 'Cleaning of industrial machinery',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '81294',
      'Description': 'Cleaning of trains, buses, planes',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '81295',
      'Description': 'Cleaning of pest control services not in connection with agriculture',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '81296',
      'Description': 'Disinfecting and exterminating activities',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '81297',
      'Description': 'Cleaning of sea tankers',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '81299',
      'Description': 'Other building and industrial cleaning activities, n.e.c.',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '81300',
      'Description': 'Landscape care and maintenance service activities',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '82110',
      'Description': 'Combined office administrative service activities',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '82191',
      'Description': 'Document preparation, editing and/or proofreading',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '82192',
      'Description': 'Typing, word processing or desktop publishing',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '82193',
      'Description': 'Secretarial support services',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '82194',
      'Description': 'Transcription of documents and other secretarial services',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '82195',
      'Description': 'Provision of mailbox rental and other postal and mailing services',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '82196',
      'Description': 'Photocopying, duplicating, blueprinting',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '82199',
      'Description':
        'Photocopying, document preparation and other specialized office support activities n.e.c.',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '82200',
      'Description': 'Activities of call centres',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '82301',
      'Description': 'Organization, promotions and/or management of event',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '82302',
      'Description': 'Meeting, incentive, convention, exhibition (MICE)',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '82910',
      'Description': 'Activities of collection agencies and credit bureaus',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '82920',
      'Description':
        'Packaging activities on a fee or contract basis, whether or not these involve an automated process',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '82990',
      'Description': 'Other business support service activities n.e.c.',
      'MSIC Category Reference': 'N',
    },
    {
      'Code': '84111',
      'Description': 'General (overall) public administration activities',
      'MSIC Category Reference': 'O',
    },
    {
      'Code': '84112',
      'Description': 'Ancillary service activities for the government as a whole',
      'MSIC Category Reference': 'O',
    },
    {
      'Code': '84121',
      'Description': 'Administrative educational services',
      'MSIC Category Reference': 'O',
    },
    {
      'Code': '84122',
      'Description': 'Administrative health care services',
      'MSIC Category Reference': 'O',
    },
    {
      'Code': '84123',
      'Description': 'Administrative housing and local government services',
      'MSIC Category Reference': 'O',
    },
    {
      'Code': '84124',
      'Description': 'Administrative recreational, cultural, arts and sports services',
      'MSIC Category Reference': 'O',
    },
    {
      'Code': '84125',
      'Description': 'Administrative religious affairs services',
      'MSIC Category Reference': 'O',
    },
    {
      'Code': '84126',
      'Description': 'Administrative welfare services',
      'MSIC Category Reference': 'O',
    },
    {
      'Code': '84129',
      'Description': 'Other community and social affairs services',
      'MSIC Category Reference': 'O',
    },
    {
      'Code': '84131',
      'Description': 'Domestic and international trade affairs',
      'MSIC Category Reference': 'O',
    },
    {
      'Code': '84132',
      'Description': 'Agriculture and rural development affairs',
      'MSIC Category Reference': 'O',
    },
    {
      'Code': '84133',
      'Description': 'Primary industries affairs',
      'MSIC Category Reference': 'O',
    },
    {
      'Code': '84134',
      'Description': 'Public works affairs',
      'MSIC Category Reference': 'O',
    },
    {
      'Code': '84135',
      'Description': 'Transport affairs',
      'MSIC Category Reference': 'O',
    },
    {
      'Code': '84136',
      'Description': 'Energy, telecommunication and postal affairs',
      'MSIC Category Reference': 'O',
    },
    {
      'Code': '84137',
      'Description': 'Tourism affairs',
      'MSIC Category Reference': 'O',
    },
    {
      'Code': '84138',
      'Description': 'Human resource affairs',
      'MSIC Category Reference': 'O',
    },
    {
      'Code': '84139',
      'Description':
        'Other regulation of and contribution to more efficient operation of businesses n.e.c.',
      'MSIC Category Reference': 'O',
    },
    {
      'Code': '84210',
      'Description': 'Foreign affairs',
      'MSIC Category Reference': 'O',
    },
    {
      'Code': '84220',
      'Description': 'Military and civil defence services',
      'MSIC Category Reference': 'O',
    },
    {
      'Code': '84231',
      'Description': 'Police service',
      'MSIC Category Reference': 'O',
    },
    {
      'Code': '84232',
      'Description': 'Prison service',
      'MSIC Category Reference': 'O',
    },
    {
      'Code': '84233',
      'Description': 'Immigration service',
      'MSIC Category Reference': 'O',
    },
    {
      'Code': '84234',
      'Description': 'National registration service',
      'MSIC Category Reference': 'O',
    },
    {
      'Code': '84235',
      'Description': 'Judiciary and legal service',
      'MSIC Category Reference': 'O',
    },
    {
      'Code': '84236',
      'Description': 'Firefighting and fire prevention',
      'MSIC Category Reference': 'O',
    },
    {
      'Code': '84239',
      'Description': 'Other public order and safety affairs related services',
      'MSIC Category Reference': 'O',
    },
    {
      'Code': '84300',
      'Description': 'Compulsory social security activities e.g. SOCSO',
      'MSIC Category Reference': 'O',
    },
    {
      'Code': '85101',
      'Description': 'Pre-primary education (Public)',
      'MSIC Category Reference': 'P',
    },
    {
      'Code': '85102',
      'Description': 'Pre-primary education (Private)',
      'MSIC Category Reference': 'P',
    },
    {
      'Code': '85103',
      'Description': 'Primary education (Public)',
      'MSIC Category Reference': 'P',
    },
    {
      'Code': '85104',
      'Description': 'Primary education (Private)',
      'MSIC Category Reference': 'P',
    },
    {
      'Code': '85211',
      'Description': 'General school secondary education (Public)',
      'MSIC Category Reference': 'P',
    },
    {
      'Code': '85212',
      'Description': 'General school secondary education (Private)',
      'MSIC Category Reference': 'P',
    },
    {
      'Code': '85221',
      'Description':
        'Technical and vocational education below the level of higher education (Public)',
      'MSIC Category Reference': 'P',
    },
    {
      'Code': '85222',
      'Description':
        'Technical and vocational education below the level of higher education (Private)',
      'MSIC Category Reference': 'P',
    },
    {
      'Code': '85301',
      'Description': 'College and university education (Public)',
      'MSIC Category Reference': 'P',
    },
    {
      'Code': '85302',
      'Description': 'College and university education (Private)',
      'MSIC Category Reference': 'P',
    },
    {
      'Code': '85411',
      'Description': 'Sports instruction',
      'MSIC Category Reference': 'P',
    },
    {
      'Code': '85412',
      'Description': 'Martial arts instruction',
      'MSIC Category Reference': 'P',
    },
    {
      'Code': '85419',
      'Description': 'Any other sports and recreation education n.e.c',
      'MSIC Category Reference': 'P',
    },
    {
      'Code': '85421',
      'Description': 'Music and dancing school',
      'MSIC Category Reference': 'P',
    },
    {
      'Code': '85429',
      'Description': 'Any other cultural education n.e.c.',
      'MSIC Category Reference': 'P',
    },
    {
      'Code': '85491',
      'Description': 'Tuition centre',
      'MSIC Category Reference': 'P',
    },
    {
      'Code': '85492',
      'Description': 'Driving school',
      'MSIC Category Reference': 'P',
    },
    {
      'Code': '85493',
      'Description': 'Religious instruction',
      'MSIC Category Reference': 'P',
    },
    {
      'Code': '85494',
      'Description': 'Computer training',
      'MSIC Category Reference': 'P',
    },
    {
      'Code': '85499',
      'Description': 'Others education n.e.c',
      'MSIC Category Reference': 'P',
    },
    {
      'Code': '85500',
      'Description': 'Educational support services for provision of non-instructional services',
      'MSIC Category Reference': 'P',
    },
    {
      'Code': '86101',
      'Description': 'Hospital activities',
      'MSIC Category Reference': 'Q',
    },
    {
      'Code': '86102',
      'Description': 'Maternity home services (outside hospital)',
      'MSIC Category Reference': 'Q',
    },
    {
      'Code': '86201',
      'Description': 'General medical services',
      'MSIC Category Reference': 'Q',
    },
    {
      'Code': '86202',
      'Description': 'Specialized medical services',
      'MSIC Category Reference': 'Q',
    },
    {
      'Code': '86203',
      'Description': 'Dental services',
      'MSIC Category Reference': 'Q',
    },
    {
      'Code': '86901',
      'Description': 'Dialysis Centres',
      'MSIC Category Reference': 'Q',
    },
    {
      'Code': '86902',
      'Description': 'Medical laboratories',
      'MSIC Category Reference': 'Q',
    },
    {
      'Code': '86903',
      'Description': 'Physiotherapy and occupational therapy service',
      'MSIC Category Reference': 'Q',
    },
    {
      'Code': '86904',
      'Description': 'Acupuncture services',
      'MSIC Category Reference': 'Q',
    },
    {
      'Code': '86905',
      'Description': 'Herbalist and homeopathy services',
      'MSIC Category Reference': 'Q',
    },
    {
      'Code': '86906',
      'Description': 'Ambulance services',
      'MSIC Category Reference': 'Q',
    },
    {
      'Code': '86909',
      'Description': 'Other human health services n.e.c.',
      'MSIC Category Reference': 'Q',
    },
    {
      'Code': '87101',
      'Description': 'Homes for the elderly with nursing care',
      'MSIC Category Reference': 'Q',
    },
    {
      'Code': '87102',
      'Description': 'Nursing homes',
      'MSIC Category Reference': 'Q',
    },
    {
      'Code': '87103',
      'Description': 'Palliative or hospices',
      'MSIC Category Reference': 'Q',
    },
    {
      'Code': '87201',
      'Description': 'Drug rehabilitation centres',
      'MSIC Category Reference': 'Q',
    },
    {
      'Code': '87209',
      'Description': 'Other residential care activities for mental retardation n.e.c.',
      'MSIC Category Reference': 'Q',
    },
    {
      'Code': '87300',
      'Description': 'Residential care activities for the elderly and disabled',
      'MSIC Category Reference': 'Q',
    },
    {
      'Code': '87901',
      'Description': 'Orphanages',
      'MSIC Category Reference': 'Q',
    },
    {
      'Code': '87902',
      'Description': 'Welfare homes services',
      'MSIC Category Reference': 'Q',
    },
    {
      'Code': '87909',
      'Description': 'Other residential care activities n.e.c.',
      'MSIC Category Reference': 'Q',
    },
    {
      'Code': '88101',
      'Description': 'Day-care activities for the elderly or for handicapped adults',
      'MSIC Category Reference': 'Q',
    },
    {
      'Code': '88109',
      'Description':
        'Others social work activities without accommodation for the elderly and disabled',
      'MSIC Category Reference': 'Q',
    },
    {
      'Code': '88901',
      'Description': 'Counselling service',
      'MSIC Category Reference': 'Q',
    },
    {
      'Code': '88902',
      'Description': 'Child day-care activities',
      'MSIC Category Reference': 'Q',
    },
    {
      'Code': '88909',
      'Description': 'Other social work activities without accommodation n.e.c.',
      'MSIC Category Reference': 'Q',
    },
    {
      'Code': '90001',
      'Description': 'Theatrical producer, singer group band and orchestra entertainment services',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '90002',
      'Description': 'Operation of concert and theatre halls and other arts facilities',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '90003',
      'Description': 'Activities of sculptors, painters, cartoonists, engravers, etchers',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '90004',
      'Description': 'Activities of individual writers, for all subjects',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '90005',
      'Description': 'Activities of independent journalists',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '90006',
      'Description': 'Restoring of works of art such as painting',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '90007',
      'Description':
        'Activities of producers or entrepreneurs of arts live events, with or without facilities',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '90009',
      'Description': 'Creative, arts and entertainment activities n.e.c.',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '91011',
      'Description': 'Documentation and information activities of libraries of all kinds',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '91012',
      'Description': 'Stock photo libraries and services',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '91021',
      'Description': 'Operation of museums of all kinds',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '91022',
      'Description': 'Operation of historical sites and buildings',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '91031',
      'Description': 'Operation of botanical and zoological gardens',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '91032',
      'Description': 'Operation of nature reserves, including wildlife preservation',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '92000',
      'Description': 'GAMBLING AND BETTING ACTIVITIES',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '93111',
      'Description': 'Football, hockey, cricket, baseball, badminton, futsal, paintball',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '93112',
      'Description': 'Racetracks for auto',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '93113',
      'Description': 'Equestrian clubs',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '93114',
      'Description': 'Swimming pools and stadiums, ice-skating arenas',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '93115',
      'Description': 'Track and field stadium',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '93116',
      'Description': 'Golf courses',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '93117',
      'Description': 'Bowling centre',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '93118',
      'Description': 'Fitness centres',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '93119',
      'Description':
        'Organization and operation of outdoor or indoor sports events for professionals or amateurs by organizations with own facilities',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '93120',
      'Description':
        'The operation of sports clubs such as football club, bowling club, swimming club',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '93191',
      'Description':
        'Activities of producers or promoters of sports events, with or without facilities',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '93192',
      'Description': 'Activities of sports leagues and regulating bodies',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '93193',
      'Description': 'Activities of related to promotion of sporting events',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '93199',
      'Description': 'Other sports activities n.e.c.',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '93210',
      'Description': 'Activities of amusement parks and theme parks',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '93291',
      'Description': 'Activities of recreation parks and beaches',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '93292',
      'Description': 'Operation of recreational transport facilities',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '93293',
      'Description':
        'Renting of leisure and pleasure equipment as an integral part of recreational facilities',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '93294',
      'Description': 'Operation of fairs and shows of a recreational nature',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '93295',
      'Description': 'Operation of discotheques and dance floors',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '93296',
      'Description':
        'Activities of producers or entrepreneurs of live events other than arts or sports events, with or without facilities',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '93297',
      'Description': 'Cyber Café/Internet Centre',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '93299',
      'Description': 'Any other amusement and recreation activities n.e.c.',
      'MSIC Category Reference': 'R',
    },
    {
      'Code': '94110',
      'Description': 'Activities of business and employers membership organizations',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '94120',
      'Description': 'Activities of professional membership organizations',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '94200',
      'Description': 'Activities of trade unions',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '94910',
      'Description': 'Activities of religious organizations',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '94920',
      'Description': 'Activities of political organizations',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '94990',
      'Description': 'Activities of other membership organizations n.e.c.',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '95111',
      'Description': 'Repair of electronic equipment',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '95112',
      'Description': 'Repair and maintenance of computer terminals',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '95113',
      'Description': "Repair and maintenance of hand-held computers (PDA's)",
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '95121',
      'Description': 'Repair and maintenance of cordless telephones',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '95122',
      'Description': 'Repair and maintenance of cellular phones',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '95123',
      'Description': 'Repair and maintenance of carrier equipment modems',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '95124',
      'Description': 'Repair and maintenance of fax machines',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '95125',
      'Description': 'Repair and maintenance of communications transmission equipment',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '95126',
      'Description': 'Repair and maintenance of two-way radios',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '95127',
      'Description': 'Repair and maintenance of commercial TV and video cameras',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '95211',
      'Description': 'Repair and maintenance of television, radio receivers',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '95212',
      'Description': 'Repair and maintenance of VCR/DVD/VCD',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '95213',
      'Description': 'Repair and maintenance of CD players',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '95214',
      'Description': 'Repair and maintenance of household-type video cameras',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '95221',
      'Description': 'Repair and servicing of household appliances',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '95222',
      'Description': 'Repair and servicing of home and garden equipment',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '95230',
      'Description': 'Repair of footwear and leather goods',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '95240',
      'Description': 'Repair of furniture and home furnishings',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '95291',
      'Description': 'Repair of bicycles',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '95292',
      'Description': 'Repair and alteration of clothing',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '95293',
      'Description': 'Repair and alteration of jewellery',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '95294',
      'Description': 'Repair of watches, clocks and their parts',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '95295',
      'Description': 'Repair of sporting goods',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '95296',
      'Description': 'Repair of musical instruments',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '95299',
      'Description': 'Repair of other personal and household goods n.e.c.',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '96011',
      'Description': 'Laundering and dry-cleaning, pressing',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '96012',
      'Description':
        "Carpet and rug shampooing, and drapery and curtain cleaning, whether on clients' premises or not",
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '96013',
      'Description': 'Provision of linens, work uniforms and related items by laundries',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '96014',
      'Description': 'Diaper supply services',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '96020',
      'Description': 'Hairdressing and other beauty treatment',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '96031',
      'Description':
        "Preparing the dead for burial or cremation and embalming and morticians' services",
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '96032',
      'Description': 'Providing burial or cremation services',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '96033',
      'Description': 'Rental of equipped space in funeral parlours',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '96034',
      'Description': 'Rental or sale of graves',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '96035',
      'Description': 'Maintenance of graves and mausoleums',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '96091',
      'Description': 'Activities of sauna, steam baths, massage salons',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '96092',
      'Description': "Astrological and spiritualists' activities",
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '96093',
      'Description':
        'Social activities such as escort services, dating services, services of marriage bureaux',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '96094',
      'Description': 'Pet care services',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '96095',
      'Description': 'Genealogical organizations',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '96096',
      'Description': 'Shoe shiners, porters, valet car parkers',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '96097',
      'Description': 'Concession operation of coin-operated personal service machines',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '96099',
      'Description': 'Other service activities n.e.c.',
      'MSIC Category Reference': 'S',
    },
    {
      'Code': '97000',
      'Description': 'Activities of households as employers of domestic personnel',
      'MSIC Category Reference': 'T',
    },
    {
      'Code': '98100',
      'Description':
        'Undifferentiated goods-producing activities of private households for own use',
      'MSIC Category Reference': 'T',
    },
    {
      'Code': '98200',
      'Description':
        'Undifferentiated service-producing activities of private households for own use',
      'MSIC Category Reference': 'T',
    },
    {
      'Code': '99000',
      'Description': 'Activities of extraterritorial organization and bodies',
      'MSIC Category Reference': 'U',
    },
  ]

  /**
   * Returns all MSIC codes
   */
  public getAllCodes(): MSICCode[] {
    return [...this.msicCodes]
  }

  public getAllCodesOnly(): string[] {
    return this.msicCodes.map((msic) => msic.Code)
  }

  /**
   * Returns a specific MSIC code by its code number
   * @param code The MSIC code to search for
   */
  public getByCode(code: string): MSICCode | undefined {
    return this.msicCodes.find((c) => c.Code === code)
  }

  /**
   * Filters MSIC codes based on a search term
   * @param searchTerm The term to search for in code or description
   * @param caseSensitive Whether the search should be case sensitive
   */
  public filterCodes(searchTerm: string, caseSensitive: boolean = false): MSICCode[] {
    if (!searchTerm) {
      return this.getAllCodes()
    }

    return this.msicCodes.filter((code) => {
      if (!caseSensitive) {
        const term = searchTerm.toLowerCase()
        return (
          code.Code.toLowerCase().includes(term) || code.Description.toLowerCase().includes(term)
        )
      }
      return code.Code.includes(searchTerm) || code.Description.includes(searchTerm)
    })
  }

  /**
   * Returns all MSIC codes for a specific category
   * @param categoryRef The category reference (e.g., "A", "B", "C")
   */
  public getByCategory(categoryRef: string): MSICCode[] {
    return this.msicCodes.filter(
      (code) => code['MSIC Category Reference'] === categoryRef.toUpperCase()
    )
  }

  /**
   * Returns all unique category references
   */
  public getAllCategories(): string[] {
    const categories = new Set(
      this.msicCodes
        .map((code) => code['MSIC Category Reference'])
        .filter((category) => category !== '')
    )
    return Array.from(categories).sort()
  }

  /**
   * Returns all codes within a specific division (first two digits)
   * @param division The two-digit division code
   */
  public getByDivision(division: string): MSICCode[] {
    const divisionPrefix = division.padStart(2, '0')
    return this.msicCodes.filter((code) => code.Code.startsWith(divisionPrefix))
  }

  /**
   * Checks if a code is valid
   * @param code The code to validate
   */
  public isValidCode(code: string): boolean {
    return this.msicCodes.some((c) => c.Code === code)
  }

  /**
   * Returns the division number for a given code
   * @param code The MSIC code
   */
  public getDivisionFromCode(code: string): string | undefined {
    if (code.length >= 2) {
      return code.substring(0, 2)
    }
    return undefined
  }

  /**
   * Returns codes that are sub-categories of a given code
   * @param parentCode The parent MSIC code
   */
  public getSubcategories(parentCode: string): MSICCode[] {
    return this.msicCodes.filter(
      (code) => code.Code.startsWith(parentCode) && code.Code !== parentCode
    )
  }
}

export class PaymentMethodsService {
  private readonly paymentMethods: PaymentMethod[] = [
    {
      'Code': '01',
      'Payment Method': 'Cash',
    },
    {
      'Code': '02',
      'Payment Method': 'Cheque',
    },
    {
      'Code': '03',
      'Payment Method': 'Bank Transfer',
    },
    {
      'Code': '04',
      'Payment Method': 'Credit Card',
    },
    {
      'Code': '05',
      'Payment Method': 'Debit Card',
    },
    {
      'Code': '06',
      'Payment Method': 'e-Wallet / Digital Wallet',
    },
    {
      'Code': '07',
      'Payment Method': 'Digital Bank',
    },
    {
      'Code': '08',
      'Payment Method': 'Others',
    },
  ]

  /**
   * Returns all payment methods
   */
  public getAllMethods(): PaymentMethod[] {
    return [...this.paymentMethods]
  }

  public getAllMethodsCode(): string[] {
    return this.paymentMethods.map((method) => method.Code)
  }

  /**
   * Returns a payment method by its code
   * @param code The code to search for
   */
  public getByCode(code: string): PaymentMethod | undefined {
    return this.paymentMethods.find((method) => method.Code === code)
  }

  /**
   * Returns a payment method by its name
   * @param name The payment method name to search for
   */
  public getByName(name: string): PaymentMethod | undefined {
    const normalizedName = name.toLowerCase()
    return this.paymentMethods.find(
      (method) => method['Payment Method'].toLowerCase() === normalizedName
    )
  }

  /**
   * Filters payment methods based on a search term
   * @param searchTerm The term to search for
   * @param caseSensitive Whether the search should be case sensitive
   */
  public filterMethods(searchTerm: string, caseSensitive = false): PaymentMethod[] {
    if (!searchTerm) {
      return this.getAllMethods()
    }

    return this.paymentMethods.filter((method) => {
      if (!caseSensitive) {
        const term = searchTerm.toLowerCase()
        return (
          method.Code.toLowerCase().includes(term) ||
          method['Payment Method'].toLowerCase().includes(term)
        )
      }
      return method.Code.includes(searchTerm) || method['Payment Method'].includes(searchTerm)
    })
  }

  /**
   * Checks if a payment method code is valid
   * @param code The code to validate
   */
  public isValidCode(code: string): boolean {
    return this.paymentMethods.some((method) => method.Code === code)
  }

  /**
   * Checks if a payment method is digital (e-Wallet, Digital Bank, etc.)
   * @param code The payment method code
   */
  public isDigitalPayment(code: string): boolean {
    const method = this.getByCode(code)
    if (!method) return false

    const digitalMethods = ['06', '07'] // e-Wallet and Digital Bank codes
    return digitalMethods.includes(method.Code)
  }

  /**
   * Checks if a payment method is card-based (Credit Card or Debit Card)
   * @param code The payment method code
   */
  public isCardPayment(code: string): boolean {
    const method = this.getByCode(code)
    if (!method) return false

    const cardMethods = ['04', '05'] // Credit Card and Debit Card codes
    return cardMethods.includes(method.Code)
  }
}

export class StateCodesService {
  private readonly stateCodes: StateCode[] = [
    {
      Code: '01',
      State: 'Johor',
    },
    {
      Code: '02',
      State: 'Kedah',
    },
    {
      Code: '03',
      State: 'Kelantan',
    },
    {
      Code: '04',
      State: 'Melaka',
    },
    {
      Code: '05',
      State: 'Negeri Sembilan',
    },
    {
      Code: '06',
      State: 'Pahang',
    },
    {
      Code: '07',
      State: 'Pulau Pinang',
    },
    {
      Code: '08',
      State: 'Perak',
    },
    {
      Code: '09',
      State: 'Perlis',
    },
    {
      Code: '10',
      State: 'Selangor',
    },
    {
      Code: '11',
      State: 'Terengganu',
    },
    {
      Code: '12',
      State: 'Sabah',
    },
    {
      Code: '13',
      State: 'Sarawak',
    },
    {
      Code: '14',
      State: 'Wilayah Persekutuan Kuala Lumpur',
    },
    {
      Code: '15',
      State: 'Wilayah Persekutuan Labuan',
    },
    {
      Code: '16',
      State: 'Wilayah Persekutuan Putrajaya',
    },
    {
      Code: '17',
      State: 'Not Applicable',
    },
  ]

  /**
   * Returns all state codes
   */
  public getAllStates(): StateCode[] {
    return [...this.stateCodes]
  }

  public getAllStatesCode(): string[] {
    return this.stateCodes.map((state) => state.Code)
  }

  /**
   * Returns all valid states (excluding "Not Applicable")
   */
  public getValidStates(): StateCode[] {
    return this.stateCodes.filter((state) => state.Code !== '17')
  }

  /**
   * Returns a state by its code
   * @param code The code to search for
   */
  public getByCode(code: string): StateCode | undefined {
    return this.stateCodes.find((state) => state.Code === code)
  }

  /**
   * Returns a state by its name
   * @param name The state name to search for
   */
  public getByName(name: string): StateCode | undefined {
    const normalizedName = name.toLowerCase()
    return this.stateCodes.find((state) => state.State.toLowerCase() === normalizedName)
  }

  /**
   * Filters states based on a search term
   * @param searchTerm The term to search for
   * @param caseSensitive Whether the search should be case sensitive
   */
  public filterStates(searchTerm: string, caseSensitive = false): StateCode[] {
    if (!searchTerm) {
      return this.getAllStates()
    }

    return this.stateCodes.filter((state) => {
      if (!caseSensitive) {
        const term = searchTerm.toLowerCase()
        return state.Code.toLowerCase().includes(term) || state.State.toLowerCase().includes(term)
      }
      return state.Code.includes(searchTerm) || state.State.includes(searchTerm)
    })
  }

  /**
   * Returns all Federal Territories
   */
  public getFederalTerritories(): StateCode[] {
    return this.stateCodes.filter((state) => state.State.startsWith('Wilayah Persekutuan'))
  }

  /**
   * Returns all states (excluding Federal Territories)
   */
  public getStatesOnly(): StateCode[] {
    return this.stateCodes.filter(
      (state) => !state.State.startsWith('Wilayah Persekutuan') && state.Code !== '17'
    )
  }

  /**
   * Returns East Malaysian states (Sabah, Sarawak)
   */
  public getEastMalaysianStates(): StateCode[] {
    return this.stateCodes.filter((state) => ['12', '13'].includes(state.Code))
  }

  /**
   * Returns Peninsular Malaysian states
   */
  public getPeninsularStates(): StateCode[] {
    return this.stateCodes.filter((state) => !['12', '13', '15', '17'].includes(state.Code))
  }

  /**
   * Checks if a state code is valid
   * @param code The code to validate
   */
  public isValidCode(code: string): boolean {
    return this.stateCodes.some((state) => state.Code === code)
  }

  /**
   * Checks if a state is a Federal Territory
   * @param code The state code
   */
  public isFederalTerritory(code: string): boolean {
    const state = this.getByCode(code)
    return state ? state.State.startsWith('Wilayah Persekutuan') : false
  }

  /**
   * Checks if a state is in East Malaysia
   * @param code The state code
   */
  public isEastMalaysian(code: string): boolean {
    return ['12', '13'].includes(code)
  }
}

export class TaxTypesService {
  private readonly taxTypes: TaxType[] = [
    {
      Code: '01',
      Description: 'Sales Tax',
    },
    {
      Code: '02',
      Description: 'Service Tax',
    },
    {
      Code: '03',
      Description: 'Tourism Tax',
    },
    {
      Code: '04',
      Description: 'High-Value Goods Tax',
    },
    {
      Code: '05',
      Description: 'Sales Tax on Low Value Goods',
    },
    {
      Code: '06',
      Description: 'Not Applicable',
    },
    {
      Code: 'E',
      Description: 'Tax exemption (where applicable)',
    },
  ]

  /**
   * Returns all tax types
   */
  public getAllTypes(): TaxType[] {
    return [...this.taxTypes]
  }

  /**
   * Returns all code
   */
  public getAllCodes(): string[] {
    return [...this.taxTypes.map((tax) => tax.Code)]
  }

  /**
   * Returns all active tax types (excluding "Not Applicable")
   */
  public getActiveTaxTypes(): TaxType[] {
    return this.taxTypes.filter((tax) => tax.Code !== '06')
  }

  /**
   * Returns all sales-related tax types
   */
  public getSalesTaxTypes(): TaxType[] {
    return this.taxTypes.filter((tax) => tax.Description.toLowerCase().includes('sales tax'))
  }

  /**
   * Returns a tax type by its code
   * @param code The code to search for
   */
  public getByCode(code: string): TaxType | undefined {
    return this.taxTypes.find((tax) => tax.Code.toLowerCase() === code.toLowerCase())
  }

  /**
   * Returns a tax type by its description
   * @param description The tax description to search for
   */
  public getByDescription(description: string): TaxType | undefined {
    const normalizedDesc = description.toLowerCase()
    return this.taxTypes.find((tax) => tax.Description.toLowerCase() === normalizedDesc)
  }

  /**
   * Filters tax types based on a search term
   * @param searchTerm The term to search for
   * @param caseSensitive Whether the search should be case sensitive
   */
  public filterTypes(searchTerm: string, caseSensitive = false): TaxType[] {
    if (!searchTerm) {
      return this.getAllTypes()
    }

    return this.taxTypes.filter((tax) => {
      if (!caseSensitive) {
        const term = searchTerm.toLowerCase()
        return tax.Code.toLowerCase().includes(term) || tax.Description.toLowerCase().includes(term)
      }
      return tax.Code.includes(searchTerm) || tax.Description.includes(searchTerm)
    })
  }

  /**
   * Checks if a tax code is valid
   * @param code The code to validate
   */
  public isValidCode(code: string): boolean {
    return this.taxTypes.some((tax) => tax.Code.toLowerCase() === code.toLowerCase())
  }

  /**
   * Checks if a tax type is a sales tax
   * @param code The tax code
   */
  public isSalesTax(code: string): boolean {
    const tax = this.getByCode(code)
    return tax ? tax.Description.toLowerCase().includes('sales tax') : false
  }

  /**
   * Checks if a tax type is exempt
   * @param code The tax code
   */
  public isExempt(code: string): boolean {
    return code.toLowerCase() === 'e'
  }

  /**
   * Checks if a tax type is applicable
   * @param code The tax code
   */
  public isApplicable(code: string): boolean {
    return code !== '06'
  }

  /**
   * Returns all goods-related tax types
   */
  public getGoodsRelatedTaxTypes(): TaxType[] {
    return this.taxTypes.filter((tax) => tax.Description.toLowerCase().includes('goods'))
  }
}

export class UnitTypesService {
  private readonly unitTypes: UnitType[] = [
    {
      Code: '10',
      Name: 'group',
    },
    {
      Code: '11',
      Name: 'outfit',
    },
    {
      Code: '13',
      Name: 'ration',
    },
    {
      Code: '14',
      Name: 'shot',
    },
    {
      Code: '15',
      Name: 'stick, military',
    },
    {
      Code: '1I',
      Name: 'fixed rate',
    },
    {
      Code: '20',
      Name: 'twenty foot container',
    },
    {
      Code: '21',
      Name: 'forty foot container',
    },
    {
      Code: '22',
      Name: 'decilitre per gram',
    },
    {
      Code: '23',
      Name: 'gram per cubic centimetre',
    },
    {
      Code: '24',
      Name: 'theoretical pound',
    },
    {
      Code: '25',
      Name: 'gram per square centimetre',
    },
    {
      Code: '27',
      Name: 'theoretical ton',
    },
    {
      Code: '28',
      Name: 'kilogram per square metre',
    },
    {
      Code: '2A',
      Name: 'radian per second',
    },
    {
      Code: '2B',
      Name: 'radian per second squared',
    },
    {
      Code: '2C',
      Name: 'roentgen',
    },
    {
      Code: '2G',
      Name: 'volt AC',
    },
    {
      Code: '2H',
      Name: 'volt DC',
    },
    {
      Code: '2I',
      Name: 'British thermal unit (international table) per hour',
    },
    {
      Code: '2J',
      Name: 'cubic centimetre per second',
    },
    {
      Code: '2K',
      Name: 'cubic foot per hour',
    },
    {
      Code: '2L',
      Name: 'cubic foot per minute',
    },
    {
      Code: '2M',
      Name: 'centimetre per second',
    },
    {
      Code: '2N',
      Name: 'decibel',
    },
    {
      Code: '2P',
      Name: 'kilobyte',
    },
    {
      Code: '2Q',
      Name: 'kilobecquerel',
    },
    {
      Code: '2R',
      Name: 'kilocurie',
    },
    {
      Code: '2U',
      Name: 'megagram',
    },
    {
      Code: '2X',
      Name: 'metre per minute',
    },
    {
      Code: '2Y',
      Name: 'milliroentgen',
    },
    {
      Code: '2Z',
      Name: 'millivolt',
    },
    {
      Code: '33',
      Name: 'kilopascal square metre per gram',
    },
    {
      Code: '34',
      Name: 'kilopascal per millimetre',
    },
    {
      Code: '35',
      Name: 'millilitre per square centimetre second',
    },
    {
      Code: '37',
      Name: 'ounce per square foot',
    },
    {
      Code: '38',
      Name: 'ounce per square foot per 0,01inch',
    },
    {
      Code: '3B',
      Name: 'megajoule',
    },
    {
      Code: '3C',
      Name: 'manmonth',
    },
    {
      Code: '40',
      Name: 'millilitre per second',
    },
    {
      Code: '41',
      Name: 'millilitre per minute',
    },
    {
      Code: '4C',
      Name: 'centistokes',
    },
    {
      Code: '4G',
      Name: 'microlitre',
    },
    {
      Code: '4H',
      Name: 'micrometre (micron)',
    },
    {
      Code: '4K',
      Name: 'milliampere',
    },
    {
      Code: '4L',
      Name: 'megabyte',
    },
    {
      Code: '4M',
      Name: 'milligram per hour',
    },
    {
      Code: '4N',
      Name: 'megabecquerel',
    },
    {
      Code: '4O',
      Name: 'microfarad',
    },
    {
      Code: '4P',
      Name: 'newton per metre',
    },
    {
      Code: '4Q',
      Name: 'ounce inch',
    },
    {
      Code: '4R',
      Name: 'ounce foot',
    },
    {
      Code: '4T',
      Name: 'picofarad',
    },
    {
      Code: '4U',
      Name: 'pound per hour',
    },
    {
      Code: '4W',
      Name: 'ton (US) per hour',
    },
    {
      Code: '4X',
      Name: 'kilolitre per hour',
    },
    {
      Code: '56',
      Name: 'sitas',
    },
    {
      Code: '57',
      Name: 'mesh',
    },
    {
      Code: '58',
      Name: 'net kilogram',
    },
    {
      Code: '59',
      Name: 'part per million',
    },
    {
      Code: '5A',
      Name: 'barrel (US) per minute',
    },
    {
      Code: '5B',
      Name: 'batch',
    },
    {
      Code: '5E',
      Name: 'MMSCF\/day',
    },
    {
      Code: '5J',
      Name: 'hydraulic horse power',
    },
    {
      Code: '60',
      Name: 'percent weight',
    },
    {
      Code: '61',
      Name: 'part per billion (US)',
    },
    {
      Code: '74',
      Name: 'millipascal',
    },
    {
      Code: '77',
      Name: 'milli-inch',
    },
    {
      Code: '80',
      Name: 'pound per square inch absolute',
    },
    {
      Code: '81',
      Name: 'henry',
    },
    {
      Code: '85',
      Name: 'foot pound-force',
    },
    {
      Code: '87',
      Name: 'pound per cubic foot',
    },
    {
      Code: '89',
      Name: 'poise',
    },
    {
      Code: '91',
      Name: 'stokes',
    },
    {
      Code: 'A10',
      Name: 'ampere square metre per joule second',
    },
    {
      Code: 'A11',
      Name: 'angstrom',
    },
    {
      Code: 'A12',
      Name: 'astronomical unit',
    },
    {
      Code: 'A13',
      Name: 'attojoule',
    },
    {
      Code: 'A14',
      Name: 'barn',
    },
    {
      Code: 'A15',
      Name: 'barn per electronvolt',
    },
    {
      Code: 'A16',
      Name: 'barn per steradian electronvolt',
    },
    {
      Code: 'A17',
      Name: 'barn per steradian',
    },
    {
      Code: 'A18',
      Name: 'becquerel per kilogram',
    },
    {
      Code: 'A19',
      Name: 'becquerel per cubic metre',
    },
    {
      Code: 'A2',
      Name: 'ampere per centimetre',
    },
    {
      Code: 'A20',
      Name: 'British thermal unit (international table) per second square foot degree\n\t\t\t\tRankine',
    },
    {
      Code: 'A21',
      Name: 'British thermal unit (international table) per pound degree Rankine',
    },
    {
      Code: 'A22',
      Name: 'British thermal unit (international table) per second foot degree Rankine',
    },
    {
      Code: 'A23',
      Name: 'British thermal unit (international table) per hour square foot degree Rankine',
    },
    {
      Code: 'A24',
      Name: 'candela per square metre',
    },
    {
      Code: 'A26',
      Name: 'coulomb metre',
    },
    {
      Code: 'A27',
      Name: 'coulomb metre squared per volt',
    },
    {
      Code: 'A28',
      Name: 'coulomb per cubic centimetre',
    },
    {
      Code: 'A29',
      Name: 'coulomb per cubic metre',
    },
    {
      Code: 'A3',
      Name: 'ampere per millimetre',
    },
    {
      Code: 'A30',
      Name: 'coulomb per cubic millimetre',
    },
    {
      Code: 'A31',
      Name: 'coulomb per kilogram second',
    },
    {
      Code: 'A32',
      Name: 'coulomb per mole',
    },
    {
      Code: 'A33',
      Name: 'coulomb per square centimetre',
    },
    {
      Code: 'A34',
      Name: 'coulomb per square metre',
    },
    {
      Code: 'A35',
      Name: 'coulomb per square millimetre',
    },
    {
      Code: 'A36',
      Name: 'cubic centimetre per mole',
    },
    {
      Code: 'A37',
      Name: 'cubic decimetre per mole',
    },
    {
      Code: 'A38',
      Name: 'cubic metre per coulomb',
    },
    {
      Code: 'A39',
      Name: 'cubic metre per kilogram',
    },
    {
      Code: 'A4',
      Name: 'ampere per square centimetre',
    },
    {
      Code: 'A40',
      Name: 'cubic metre per mole',
    },
    {
      Code: 'A41',
      Name: 'ampere per square metre',
    },
    {
      Code: 'A42',
      Name: 'curie per kilogram',
    },
    {
      Code: 'A43',
      Name: 'deadweight tonnage',
    },
    {
      Code: 'A44',
      Name: 'decalitre',
    },
    {
      Code: 'A45',
      Name: 'decametre',
    },
    {
      Code: 'A47',
      Name: 'decitex',
    },
    {
      Code: 'A48',
      Name: 'degree Rankine',
    },
    {
      Code: 'A49',
      Name: 'denier',
    },
    {
      Code: 'A5',
      Name: 'ampere square metre',
    },
    {
      Code: 'A53',
      Name: 'electronvolt',
    },
    {
      Code: 'A54',
      Name: 'electronvolt per metre',
    },
    {
      Code: 'A55',
      Name: 'electronvolt square metre',
    },
    {
      Code: 'A56',
      Name: 'electronvolt square metre per kilogram',
    },
    {
      Code: 'A59',
      Name: '8-part cloud cover',
    },
    {
      Code: 'A6',
      Name: 'ampere per square metre kelvin squared',
    },
    {
      Code: 'A68',
      Name: 'exajoule',
    },
    {
      Code: 'A69',
      Name: 'farad per metre',
    },
    {
      Code: 'A7',
      Name: 'ampere per square millimetre',
    },
    {
      Code: 'A70',
      Name: 'femtojoule',
    },
    {
      Code: 'A71',
      Name: 'femtometre',
    },
    {
      Code: 'A73',
      Name: 'foot per second squared',
    },
    {
      Code: 'A74',
      Name: 'foot pound-force per second',
    },
    {
      Code: 'A75',
      Name: 'freight ton',
    },
    {
      Code: 'A76',
      Name: 'gal',
    },
    {
      Code: 'A8',
      Name: 'ampere second',
    },
    {
      Code: 'A84',
      Name: 'gigacoulomb per cubic metre',
    },
    {
      Code: 'A85',
      Name: 'gigaelectronvolt',
    },
    {
      Code: 'A86',
      Name: 'gigahertz',
    },
    {
      Code: 'A87',
      Name: 'gigaohm',
    },
    {
      Code: 'A88',
      Name: 'gigaohm metre',
    },
    {
      Code: 'A89',
      Name: 'gigapascal',
    },
    {
      Code: 'A9',
      Name: 'rate',
    },
    {
      Code: 'A90',
      Name: 'gigawatt',
    },
    {
      Code: 'A91',
      Name: 'gon',
    },
    {
      Code: 'A93',
      Name: 'gram per cubic metre',
    },
    {
      Code: 'A94',
      Name: 'gram per mole',
    },
    {
      Code: 'A95',
      Name: 'gray',
    },
    {
      Code: 'A96',
      Name: 'gray per second',
    },
    {
      Code: 'A97',
      Name: 'hectopascal',
    },
    {
      Code: 'A98',
      Name: 'henry per metre',
    },
    {
      Code: 'A99',
      Name: 'bit',
    },
    {
      Code: 'AA',
      Name: 'ball',
    },
    {
      Code: 'AB',
      Name: 'bulk pack',
    },
    {
      Code: 'ACR',
      Name: 'acre',
    },
    {
      Code: 'ACT',
      Name: 'activity',
    },
    {
      Code: 'AD',
      Name: 'byte',
    },
    {
      Code: 'AE',
      Name: 'ampere per metre',
    },
    {
      Code: 'AH',
      Name: 'additional minute',
    },
    {
      Code: 'AI',
      Name: 'average minute per call',
    },
    {
      Code: 'AK',
      Name: 'fathom',
    },
    {
      Code: 'AL',
      Name: 'access line',
    },
    {
      Code: 'AMH',
      Name: 'ampere hour',
    },
    {
      Code: 'AMP',
      Name: 'ampere',
    },
    {
      Code: 'ANN',
      Name: 'year',
    },
    {
      Code: 'APZ',
      Name: 'troy ounce or apothecary ounce',
    },
    {
      Code: 'AQ',
      Name: 'anti-hemophilic factor (AHF) unit',
    },
    {
      Code: 'AS',
      Name: 'assortment',
    },
    {
      Code: 'ASM',
      Name: 'alcoholic strength by mass',
    },
    {
      Code: 'ASU',
      Name: 'alcoholic strength by volume',
    },
    {
      Code: 'ATM',
      Name: 'standard atmosphere',
    },
    {
      Code: 'AWG',
      Name: 'american wire gauge',
    },
    {
      Code: 'AY',
      Name: 'assembly',
    },
    {
      Code: 'AZ',
      Name: 'British thermal unit (international table) per pound',
    },
    {
      Code: 'B1',
      Name: 'barrel (US) per day',
    },
    {
      Code: 'B10',
      Name: 'bit per second',
    },
    {
      Code: 'B11',
      Name: 'joule per kilogram kelvin',
    },
    {
      Code: 'B12',
      Name: 'joule per metre',
    },
    {
      Code: 'B13',
      Name: 'joule per square metre',
    },
    {
      Code: 'B14',
      Name: 'joule per metre to the fourth power',
    },
    {
      Code: 'B15',
      Name: 'joule per mole',
    },
    {
      Code: 'B16',
      Name: 'joule per mole kelvin',
    },
    {
      Code: 'B17',
      Name: 'credit',
    },
    {
      Code: 'B18',
      Name: 'joule second',
    },
    {
      Code: 'B19',
      Name: 'digit',
    },
    {
      Code: 'B20',
      Name: 'joule square metre per kilogram',
    },
    {
      Code: 'B21',
      Name: 'kelvin per watt',
    },
    {
      Code: 'B22',
      Name: 'kiloampere',
    },
    {
      Code: 'B23',
      Name: 'kiloampere per square metre',
    },
    {
      Code: 'B24',
      Name: 'kiloampere per metre',
    },
    {
      Code: 'B25',
      Name: 'kilobecquerel per kilogram',
    },
    {
      Code: 'B26',
      Name: 'kilocoulomb',
    },
    {
      Code: 'B27',
      Name: 'kilocoulomb per cubic metre',
    },
    {
      Code: 'B28',
      Name: 'kilocoulomb per square metre',
    },
    {
      Code: 'B29',
      Name: 'kiloelectronvolt',
    },
    {
      Code: 'B3',
      Name: 'batting pound',
    },
    {
      Code: 'B30',
      Name: 'gibibit',
    },
    {
      Code: 'B31',
      Name: 'kilogram metre per second',
    },
    {
      Code: 'B32',
      Name: 'kilogram metre squared',
    },
    {
      Code: 'B33',
      Name: 'kilogram metre squared per second',
    },
    {
      Code: 'B34',
      Name: 'kilogram per cubic decimetre',
    },
    {
      Code: 'B35',
      Name: 'kilogram per litre',
    },
    {
      Code: 'B4',
      Name: 'barrel, imperial',
    },
    {
      Code: 'B41',
      Name: 'kilojoule per kelvin',
    },
    {
      Code: 'B42',
      Name: 'kilojoule per kilogram',
    },
    {
      Code: 'B43',
      Name: 'kilojoule per kilogram kelvin',
    },
    {
      Code: 'B44',
      Name: 'kilojoule per mole',
    },
    {
      Code: 'B45',
      Name: 'kilomole',
    },
    {
      Code: 'B46',
      Name: 'kilomole per cubic metre',
    },
    {
      Code: 'B47',
      Name: 'kilonewton',
    },
    {
      Code: 'B48',
      Name: 'kilonewton metre',
    },
    {
      Code: 'B49',
      Name: 'kiloohm',
    },
    {
      Code: 'B50',
      Name: 'kiloohm metre',
    },
    {
      Code: 'B52',
      Name: 'kilosecond',
    },
    {
      Code: 'B53',
      Name: 'kilosiemens',
    },
    {
      Code: 'B54',
      Name: 'kilosiemens per metre',
    },
    {
      Code: 'B55',
      Name: 'kilovolt per metre',
    },
    {
      Code: 'B56',
      Name: 'kiloweber per metre',
    },
    {
      Code: 'B57',
      Name: 'light year',
    },
    {
      Code: 'B58',
      Name: 'litre per mole',
    },
    {
      Code: 'B59',
      Name: 'lumen hour',
    },
    {
      Code: 'B60',
      Name: 'lumen per square metre',
    },
    {
      Code: 'B61',
      Name: 'lumen per watt',
    },
    {
      Code: 'B62',
      Name: 'lumen second',
    },
    {
      Code: 'B63',
      Name: 'lux hour',
    },
    {
      Code: 'B64',
      Name: 'lux second',
    },
    {
      Code: 'B66',
      Name: 'megaampere per square metre',
    },
    {
      Code: 'B67',
      Name: 'megabecquerel per kilogram',
    },
    {
      Code: 'B68',
      Name: 'gigabit',
    },
    {
      Code: 'B69',
      Name: 'megacoulomb per cubic metre',
    },
    {
      Code: 'B7',
      Name: 'cycle',
    },
    {
      Code: 'B70',
      Name: 'megacoulomb per square metre',
    },
    {
      Code: 'B71',
      Name: 'megaelectronvolt',
    },
    {
      Code: 'B72',
      Name: 'megagram per cubic metre',
    },
    {
      Code: 'B73',
      Name: 'meganewton',
    },
    {
      Code: 'B74',
      Name: 'meganewton metre',
    },
    {
      Code: 'B75',
      Name: 'megaohm',
    },
    {
      Code: 'B76',
      Name: 'megaohm metre',
    },
    {
      Code: 'B77',
      Name: 'megasiemens per metre',
    },
    {
      Code: 'B78',
      Name: 'megavolt',
    },
    {
      Code: 'B79',
      Name: 'megavolt per metre',
    },
    {
      Code: 'B8',
      Name: 'joule per cubic metre',
    },
    {
      Code: 'B80',
      Name: 'gigabit per second',
    },
    {
      Code: 'B81',
      Name: 'reciprocal metre squared reciprocal second',
    },
    {
      Code: 'B82',
      Name: 'inch per linear foot',
    },
    {
      Code: 'B83',
      Name: 'metre to the fourth power',
    },
    {
      Code: 'B84',
      Name: 'microampere',
    },
    {
      Code: 'B85',
      Name: 'microbar',
    },
    {
      Code: 'B86',
      Name: 'microcoulomb',
    },
    {
      Code: 'B87',
      Name: 'microcoulomb per cubic metre',
    },
    {
      Code: 'B88',
      Name: 'microcoulomb per square metre',
    },
    {
      Code: 'B89',
      Name: 'microfarad per metre',
    },
    {
      Code: 'B90',
      Name: 'microhenry',
    },
    {
      Code: 'B91',
      Name: 'microhenry per metre',
    },
    {
      Code: 'B92',
      Name: 'micronewton',
    },
    {
      Code: 'B93',
      Name: 'micronewton metre',
    },
    {
      Code: 'B94',
      Name: 'microohm',
    },
    {
      Code: 'B95',
      Name: 'microohm metre',
    },
    {
      Code: 'B96',
      Name: 'micropascal',
    },
    {
      Code: 'B97',
      Name: 'microradian',
    },
    {
      Code: 'B98',
      Name: 'microsecond',
    },
    {
      Code: 'B99',
      Name: 'microsiemens',
    },
    {
      Code: 'BAR',
      Name: 'bar [unit of pressure]',
    },
    {
      Code: 'BB',
      Name: 'base box',
    },
    {
      Code: 'BFT',
      Name: 'board foot',
    },
    {
      Code: 'BHP',
      Name: 'brake horse power',
    },
    {
      Code: 'BIL',
      Name: 'billion (EUR)',
    },
    {
      Code: 'BLD',
      Name: 'dry barrel (US)',
    },
    {
      Code: 'BLL',
      Name: 'barrel (US)',
    },
    {
      Code: 'BP',
      Name: 'hundred board foot',
    },
    {
      Code: 'BPM',
      Name: 'beats per minute',
    },
    {
      Code: 'BQL',
      Name: 'becquerel',
    },
    {
      Code: 'BTU',
      Name: 'British thermal unit (international table)',
    },
    {
      Code: 'BUA',
      Name: 'bushel (US)',
    },
    {
      Code: 'BUI',
      Name: 'bushel (UK)',
    },
    {
      Code: 'C0',
      Name: 'call',
    },
    {
      Code: 'C10',
      Name: 'millifarad',
    },
    {
      Code: 'C11',
      Name: 'milligal',
    },
    {
      Code: 'C12',
      Name: 'milligram per metre',
    },
    {
      Code: 'C13',
      Name: 'milligray',
    },
    {
      Code: 'C14',
      Name: 'millihenry',
    },
    {
      Code: 'C15',
      Name: 'millijoule',
    },
    {
      Code: 'C16',
      Name: 'millimetre per second',
    },
    {
      Code: 'C17',
      Name: 'millimetre squared per second',
    },
    {
      Code: 'C18',
      Name: 'millimole',
    },
    {
      Code: 'C19',
      Name: 'mole per kilogram',
    },
    {
      Code: 'C20',
      Name: 'millinewton',
    },
    {
      Code: 'C21',
      Name: 'kibibit',
    },
    {
      Code: 'C22',
      Name: 'millinewton per metre',
    },
    {
      Code: 'C23',
      Name: 'milliohm metre',
    },
    {
      Code: 'C24',
      Name: 'millipascal second',
    },
    {
      Code: 'C25',
      Name: 'milliradian',
    },
    {
      Code: 'C26',
      Name: 'millisecond',
    },
    {
      Code: 'C27',
      Name: 'millisiemens',
    },
    {
      Code: 'C28',
      Name: 'millisievert',
    },
    {
      Code: 'C29',
      Name: 'millitesla',
    },
    {
      Code: 'C3',
      Name: 'microvolt per metre',
    },
    {
      Code: 'C30',
      Name: 'millivolt per metre',
    },
    {
      Code: 'C31',
      Name: 'milliwatt',
    },
    {
      Code: 'C32',
      Name: 'milliwatt per square metre',
    },
    {
      Code: 'C33',
      Name: 'milliweber',
    },
    {
      Code: 'C34',
      Name: 'mole',
    },
    {
      Code: 'C35',
      Name: 'mole per cubic decimetre',
    },
    {
      Code: 'C36',
      Name: 'mole per cubic metre',
    },
    {
      Code: 'C37',
      Name: 'kilobit',
    },
    {
      Code: 'C38',
      Name: 'mole per litre',
    },
    {
      Code: 'C39',
      Name: 'nanoampere',
    },
    {
      Code: 'C40',
      Name: 'nanocoulomb',
    },
    {
      Code: 'C41',
      Name: 'nanofarad',
    },
    {
      Code: 'C42',
      Name: 'nanofarad per metre',
    },
    {
      Code: 'C43',
      Name: 'nanohenry',
    },
    {
      Code: 'C44',
      Name: 'nanohenry per metre',
    },
    {
      Code: 'C45',
      Name: 'nanometre',
    },
    {
      Code: 'C46',
      Name: 'nanoohm metre',
    },
    {
      Code: 'C47',
      Name: 'nanosecond',
    },
    {
      Code: 'C48',
      Name: 'nanotesla',
    },
    {
      Code: 'C49',
      Name: 'nanowatt',
    },
    {
      Code: 'C50',
      Name: 'neper',
    },
    {
      Code: 'C51',
      Name: 'neper per second',
    },
    {
      Code: 'C52',
      Name: 'picometre',
    },
    {
      Code: 'C53',
      Name: 'newton metre second',
    },
    {
      Code: 'C54',
      Name: 'newton metre squared per kilogram squared',
    },
    {
      Code: 'C55',
      Name: 'newton per square metre',
    },
    {
      Code: 'C56',
      Name: 'newton per square millimetre',
    },
    {
      Code: 'C57',
      Name: 'newton second',
    },
    {
      Code: 'C58',
      Name: 'newton second per metre',
    },
    {
      Code: 'C59',
      Name: 'octave',
    },
    {
      Code: 'C60',
      Name: 'ohm centimetre',
    },
    {
      Code: 'C61',
      Name: 'ohm metre',
    },
    {
      Code: 'C62',
      Name: 'one',
    },
    {
      Code: 'C63',
      Name: 'parsec',
    },
    {
      Code: 'C64',
      Name: 'pascal per kelvin',
    },
    {
      Code: 'C65',
      Name: 'pascal second',
    },
    {
      Code: 'C66',
      Name: 'pascal second per cubic metre',
    },
    {
      Code: 'C67',
      Name: 'pascal second per metre',
    },
    {
      Code: 'C68',
      Name: 'petajoule',
    },
    {
      Code: 'C69',
      Name: 'phon',
    },
    {
      Code: 'C7',
      Name: 'centipoise',
    },
    {
      Code: 'C70',
      Name: 'picoampere',
    },
    {
      Code: 'C71',
      Name: 'picocoulomb',
    },
    {
      Code: 'C72',
      Name: 'picofarad per metre',
    },
    {
      Code: 'C73',
      Name: 'picohenry',
    },
    {
      Code: 'C74',
      Name: 'kilobit per second',
    },
    {
      Code: 'C75',
      Name: 'picowatt',
    },
    {
      Code: 'C76',
      Name: 'picowatt per square metre',
    },
    {
      Code: 'C78',
      Name: 'pound-force',
    },
    {
      Code: 'C79',
      Name: 'kilovolt ampere hour',
    },
    {
      Code: 'C8',
      Name: 'millicoulomb per kilogram',
    },
    {
      Code: 'C80',
      Name: 'rad',
    },
    {
      Code: 'C81',
      Name: 'radian',
    },
    {
      Code: 'C82',
      Name: 'radian square metre per mole',
    },
    {
      Code: 'C83',
      Name: 'radian square metre per kilogram',
    },
    {
      Code: 'C84',
      Name: 'radian per metre',
    },
    {
      Code: 'C85',
      Name: 'reciprocal angstrom',
    },
    {
      Code: 'C86',
      Name: 'reciprocal cubic metre',
    },
    {
      Code: 'C87',
      Name: 'reciprocal cubic metre per second',
    },
    {
      Code: 'C88',
      Name: 'reciprocal electron volt per cubic metre',
    },
    {
      Code: 'C89',
      Name: 'reciprocal henry',
    },
    {
      Code: 'C9',
      Name: 'coil group',
    },
    {
      Code: 'C90',
      Name: 'reciprocal joule per cubic metre',
    },
    {
      Code: 'C91',
      Name: 'reciprocal kelvin or kelvin to the power minus one',
    },
    {
      Code: 'C92',
      Name: 'reciprocal metre',
    },
    {
      Code: 'C93',
      Name: 'reciprocal square metre',
    },
    {
      Code: 'C94',
      Name: 'reciprocal minute',
    },
    {
      Code: 'C95',
      Name: 'reciprocal mole',
    },
    {
      Code: 'C96',
      Name: 'reciprocal pascal or pascal to the power minus one',
    },
    {
      Code: 'C97',
      Name: 'reciprocal second',
    },
    {
      Code: 'C99',
      Name: 'reciprocal second per metre squared',
    },
    {
      Code: 'CCT',
      Name: 'carrying capacity in metric ton',
    },
    {
      Code: 'CDL',
      Name: 'candela',
    },
    {
      Code: 'CEL',
      Name: 'degree Celsius',
    },
    {
      Code: 'CEN',
      Name: 'hundred',
    },
    {
      Code: 'CG',
      Name: 'card',
    },
    {
      Code: 'CGM',
      Name: 'centigram',
    },
    {
      Code: 'CKG',
      Name: 'coulomb per kilogram',
    },
    {
      Code: 'CLF',
      Name: 'hundred leave',
    },
    {
      Code: 'CLT',
      Name: 'centilitre',
    },
    {
      Code: 'CMK',
      Name: 'square centimetre',
    },
    {
      Code: 'CMQ',
      Name: 'cubic centimetre',
    },
    {
      Code: 'CMT',
      Name: 'centimetre',
    },
    {
      Code: 'CNP',
      Name: 'hundred pack',
    },
    {
      Code: 'CNT',
      Name: 'cental (UK)',
    },
    {
      Code: 'COU',
      Name: 'coulomb',
    },
    {
      Code: 'CTG',
      Name: 'content gram',
    },
    {
      Code: 'CTM',
      Name: 'metric carat',
    },
    {
      Code: 'CTN',
      Name: 'content ton (metric)',
    },
    {
      Code: 'CUR',
      Name: 'curie',
    },
    {
      Code: 'CWA',
      Name: 'hundred pound (cwt) \/ hundred weight (US)',
    },
    {
      Code: 'CWI',
      Name: 'hundred weight (UK)',
    },
    {
      Code: 'D03',
      Name: 'kilowatt hour per hour',
    },
    {
      Code: 'D04',
      Name: 'lot [unit of weight]',
    },
    {
      Code: 'D1',
      Name: 'reciprocal second per steradian',
    },
    {
      Code: 'D10',
      Name: 'siemens per metre',
    },
    {
      Code: 'D11',
      Name: 'mebibit',
    },
    {
      Code: 'D12',
      Name: 'siemens square metre per mole',
    },
    {
      Code: 'D13',
      Name: 'sievert',
    },
    {
      Code: 'D15',
      Name: 'sone',
    },
    {
      Code: 'D16',
      Name: 'square centimetre per erg',
    },
    {
      Code: 'D17',
      Name: 'square centimetre per steradian erg',
    },
    {
      Code: 'D18',
      Name: 'metre kelvin',
    },
    {
      Code: 'D19',
      Name: 'square metre kelvin per watt',
    },
    {
      Code: 'D2',
      Name: 'reciprocal second per steradian metre squared',
    },
    {
      Code: 'D20',
      Name: 'square metre per joule',
    },
    {
      Code: 'D21',
      Name: 'square metre per kilogram',
    },
    {
      Code: 'D22',
      Name: 'square metre per mole',
    },
    {
      Code: 'D23',
      Name: 'pen gram (protein)',
    },
    {
      Code: 'D24',
      Name: 'square metre per steradian',
    },
    {
      Code: 'D25',
      Name: 'square metre per steradian joule',
    },
    {
      Code: 'D26',
      Name: 'square metre per volt second',
    },
    {
      Code: 'D27',
      Name: 'steradian',
    },
    {
      Code: 'D29',
      Name: 'terahertz',
    },
    {
      Code: 'D30',
      Name: 'terajoule',
    },
    {
      Code: 'D31',
      Name: 'terawatt',
    },
    {
      Code: 'D32',
      Name: 'terawatt hour',
    },
    {
      Code: 'D33',
      Name: 'tesla',
    },
    {
      Code: 'D34',
      Name: 'tex',
    },
    {
      Code: 'D36',
      Name: 'megabit',
    },
    {
      Code: 'D41',
      Name: 'tonne per cubic metre',
    },
    {
      Code: 'D42',
      Name: 'tropical year',
    },
    {
      Code: 'D43',
      Name: 'unified atomic mass unit',
    },
    {
      Code: 'D44',
      Name: 'var',
    },
    {
      Code: 'D45',
      Name: 'volt squared per kelvin squared',
    },
    {
      Code: 'D46',
      Name: 'volt - ampere',
    },
    {
      Code: 'D47',
      Name: 'volt per centimetre',
    },
    {
      Code: 'D48',
      Name: 'volt per kelvin',
    },
    {
      Code: 'D49',
      Name: 'millivolt per kelvin',
    },
    {
      Code: 'D5',
      Name: 'kilogram per square centimetre',
    },
    {
      Code: 'D50',
      Name: 'volt per metre',
    },
    {
      Code: 'D51',
      Name: 'volt per millimetre',
    },
    {
      Code: 'D52',
      Name: 'watt per kelvin',
    },
    {
      Code: 'D53',
      Name: 'watt per metre kelvin',
    },
    {
      Code: 'D54',
      Name: 'watt per square metre',
    },
    {
      Code: 'D55',
      Name: 'watt per square metre kelvin',
    },
    {
      Code: 'D56',
      Name: 'watt per square metre kelvin to the fourth power',
    },
    {
      Code: 'D57',
      Name: 'watt per steradian',
    },
    {
      Code: 'D58',
      Name: 'watt per steradian square metre',
    },
    {
      Code: 'D59',
      Name: 'weber per metre',
    },
    {
      Code: 'D6',
      Name: 'roentgen per second',
    },
    {
      Code: 'D60',
      Name: 'weber per millimetre',
    },
    {
      Code: 'D61',
      Name: 'minute [unit of angle]',
    },
    {
      Code: 'D62',
      Name: 'second [unit of angle]',
    },
    {
      Code: 'D63',
      Name: 'book',
    },
    {
      Code: 'D65',
      Name: 'round',
    },
    {
      Code: 'D68',
      Name: 'number of words',
    },
    {
      Code: 'D69',
      Name: 'inch to the fourth power',
    },
    {
      Code: 'D73',
      Name: 'joule square metre',
    },
    {
      Code: 'D74',
      Name: 'kilogram per mole',
    },
    {
      Code: 'D77',
      Name: 'megacoulomb',
    },
    {
      Code: 'D78',
      Name: 'megajoule per second',
    },
    {
      Code: 'D80',
      Name: 'microwatt',
    },
    {
      Code: 'D81',
      Name: 'microtesla',
    },
    {
      Code: 'D82',
      Name: 'microvolt',
    },
    {
      Code: 'D83',
      Name: 'millinewton metre',
    },
    {
      Code: 'D85',
      Name: 'microwatt per square metre',
    },
    {
      Code: 'D86',
      Name: 'millicoulomb',
    },
    {
      Code: 'D87',
      Name: 'millimole per kilogram',
    },
    {
      Code: 'D88',
      Name: 'millicoulomb per cubic metre',
    },
    {
      Code: 'D89',
      Name: 'millicoulomb per square metre',
    },
    {
      Code: 'D91',
      Name: 'rem',
    },
    {
      Code: 'D93',
      Name: 'second per cubic metre',
    },
    {
      Code: 'D94',
      Name: 'second per cubic metre radian',
    },
    {
      Code: 'D95',
      Name: 'joule per gram',
    },
    {
      Code: 'DAA',
      Name: 'decare',
    },
    {
      Code: 'DAD',
      Name: 'ten day',
    },
    {
      Code: 'DAY',
      Name: 'day',
    },
    {
      Code: 'DB',
      Name: 'dry pound',
    },
    {
      Code: 'DBM',
      Name: 'Decibel-milliwatts',
    },
    {
      Code: 'DBW',
      Name: 'Decibel watt',
    },
    {
      Code: 'DD',
      Name: 'degree [unit of angle]',
    },
    {
      Code: 'DEC',
      Name: 'decade',
    },
    {
      Code: 'DG',
      Name: 'decigram',
    },
    {
      Code: 'DJ',
      Name: 'decagram',
    },
    {
      Code: 'DLT',
      Name: 'decilitre',
    },
    {
      Code: 'DMA',
      Name: 'cubic decametre',
    },
    {
      Code: 'DMK',
      Name: 'square decimetre',
    },
    {
      Code: 'DMO',
      Name: 'standard kilolitre',
    },
    {
      Code: 'DMQ',
      Name: 'cubic decimetre',
    },
    {
      Code: 'DMT',
      Name: 'decimetre',
    },
    {
      Code: 'DN',
      Name: 'decinewton metre',
    },
    {
      Code: 'DPC',
      Name: 'dozen piece',
    },
    {
      Code: 'DPR',
      Name: 'dozen pair',
    },
    {
      Code: 'DPT',
      Name: 'displacement tonnage',
    },
    {
      Code: 'DRA',
      Name: 'dram (US)',
    },
    {
      Code: 'DRI',
      Name: 'dram (UK)',
    },
    {
      Code: 'DRL',
      Name: 'dozen roll',
    },
    {
      Code: 'DT',
      Name: 'dry ton',
    },
    {
      Code: 'DTN',
      Name: 'decitonne',
    },
    {
      Code: 'DWT',
      Name: 'pennyweight',
    },
    {
      Code: 'DZN',
      Name: 'dozen',
    },
    {
      Code: 'DZP',
      Name: 'dozen pack',
    },
    {
      Code: 'E01',
      Name: 'newton per square centimetre',
    },
    {
      Code: 'E07',
      Name: 'megawatt hour per hour',
    },
    {
      Code: 'E08',
      Name: 'megawatt per hertz',
    },
    {
      Code: 'E09',
      Name: 'milliampere hour',
    },
    {
      Code: 'E10',
      Name: 'degree day',
    },
    {
      Code: 'E12',
      Name: 'mille',
    },
    {
      Code: 'E14',
      Name: 'kilocalorie (international table)',
    },
    {
      Code: 'E15',
      Name: 'kilocalorie (thermochemical) per hour',
    },
    {
      Code: 'E16',
      Name: 'million Btu(IT) per hour',
    },
    {
      Code: 'E17',
      Name: 'cubic foot per second',
    },
    {
      Code: 'E18',
      Name: 'tonne per hour',
    },
    {
      Code: 'E19',
      Name: 'ping',
    },
    {
      Code: 'E20',
      Name: 'megabit per second',
    },
    {
      Code: 'E21',
      Name: 'shares',
    },
    {
      Code: 'E22',
      Name: 'TEU',
    },
    {
      Code: 'E23',
      Name: 'tyre',
    },
    {
      Code: 'E25',
      Name: 'active unit',
    },
    {
      Code: 'E27',
      Name: 'dose',
    },
    {
      Code: 'E28',
      Name: 'air dry ton',
    },
    {
      Code: 'E30',
      Name: 'strand',
    },
    {
      Code: 'E31',
      Name: 'square metre per litre',
    },
    {
      Code: 'E32',
      Name: 'litre per hour',
    },
    {
      Code: 'E33',
      Name: 'foot per thousand',
    },
    {
      Code: 'E34',
      Name: 'gigabyte',
    },
    {
      Code: 'E35',
      Name: 'terabyte',
    },
    {
      Code: 'E36',
      Name: 'petabyte',
    },
    {
      Code: 'E37',
      Name: 'pixel',
    },
    {
      Code: 'E38',
      Name: 'megapixel',
    },
    {
      Code: 'E39',
      Name: 'dots per inch',
    },
    {
      Code: 'E4',
      Name: 'gross kilogram',
    },
    {
      Code: 'E40',
      Name: 'part per hundred thousand',
    },
    {
      Code: 'E41',
      Name: 'kilogram-force per square millimetre',
    },
    {
      Code: 'E42',
      Name: 'kilogram-force per square centimetre',
    },
    {
      Code: 'E43',
      Name: 'joule per square centimetre',
    },
    {
      Code: 'E44',
      Name: 'kilogram-force metre per square centimetre',
    },
    {
      Code: 'E45',
      Name: 'milliohm',
    },
    {
      Code: 'E46',
      Name: 'kilowatt hour per cubic metre',
    },
    {
      Code: 'E47',
      Name: 'kilowatt hour per kelvin',
    },
    {
      Code: 'E48',
      Name: 'service unit',
    },
    {
      Code: 'E49',
      Name: 'working day',
    },
    {
      Code: 'E50',
      Name: 'accounting unit',
    },
    {
      Code: 'E51',
      Name: 'job',
    },
    {
      Code: 'E52',
      Name: 'run foot',
    },
    {
      Code: 'E53',
      Name: 'test',
    },
    {
      Code: 'E54',
      Name: 'trip',
    },
    {
      Code: 'E55',
      Name: 'use',
    },
    {
      Code: 'E56',
      Name: 'well',
    },
    {
      Code: 'E57',
      Name: 'zone',
    },
    {
      Code: 'E58',
      Name: 'exabit per second',
    },
    {
      Code: 'E59',
      Name: 'exbibyte',
    },
    {
      Code: 'E60',
      Name: 'pebibyte',
    },
    {
      Code: 'E61',
      Name: 'tebibyte',
    },
    {
      Code: 'E62',
      Name: 'gibibyte',
    },
    {
      Code: 'E63',
      Name: 'mebibyte',
    },
    {
      Code: 'E64',
      Name: 'kibibyte',
    },
    {
      Code: 'E65',
      Name: 'exbibit per metre',
    },
    {
      Code: 'E66',
      Name: 'exbibit per square metre',
    },
    {
      Code: 'E67',
      Name: 'exbibit per cubic metre',
    },
    {
      Code: 'E68',
      Name: 'gigabyte per second',
    },
    {
      Code: 'E69',
      Name: 'gibibit per metre',
    },
    {
      Code: 'E70',
      Name: 'gibibit per square metre',
    },
    {
      Code: 'E71',
      Name: 'gibibit per cubic metre',
    },
    {
      Code: 'E72',
      Name: 'kibibit per metre',
    },
    {
      Code: 'E73',
      Name: 'kibibit per square metre',
    },
    {
      Code: 'E74',
      Name: 'kibibit per cubic metre',
    },
    {
      Code: 'E75',
      Name: 'mebibit per metre',
    },
    {
      Code: 'E76',
      Name: 'mebibit per square metre',
    },
    {
      Code: 'E77',
      Name: 'mebibit per cubic metre',
    },
    {
      Code: 'E78',
      Name: 'petabit',
    },
    {
      Code: 'E79',
      Name: 'petabit per second',
    },
    {
      Code: 'E80',
      Name: 'pebibit per metre',
    },
    {
      Code: 'E81',
      Name: 'pebibit per square metre',
    },
    {
      Code: 'E82',
      Name: 'pebibit per cubic metre',
    },
    {
      Code: 'E83',
      Name: 'terabit',
    },
    {
      Code: 'E84',
      Name: 'terabit per second',
    },
    {
      Code: 'E85',
      Name: 'tebibit per metre',
    },
    {
      Code: 'E86',
      Name: 'tebibit per cubic metre',
    },
    {
      Code: 'E87',
      Name: 'tebibit per square metre',
    },
    {
      Code: 'E88',
      Name: 'bit per metre',
    },
    {
      Code: 'E89',
      Name: 'bit per square metre',
    },
    {
      Code: 'E90',
      Name: 'reciprocal centimetre',
    },
    {
      Code: 'E91',
      Name: 'reciprocal day',
    },
    {
      Code: 'E92',
      Name: 'cubic decimetre per hour',
    },
    {
      Code: 'E93',
      Name: 'kilogram per hour',
    },
    {
      Code: 'E94',
      Name: 'kilomole per second',
    },
    {
      Code: 'E95',
      Name: 'mole per second',
    },
    {
      Code: 'E96',
      Name: 'degree per second',
    },
    {
      Code: 'E97',
      Name: 'millimetre per degree Celcius metre',
    },
    {
      Code: 'E98',
      Name: 'degree Celsius per kelvin',
    },
    {
      Code: 'E99',
      Name: 'hectopascal per bar',
    },
    {
      Code: 'EA',
      Name: 'each',
    },
    {
      Code: 'EB',
      Name: 'electronic mail box',
    },
    {
      Code: 'EQ',
      Name: 'equivalent gallon',
    },
    {
      Code: 'F01',
      Name: 'bit per cubic metre',
    },
    {
      Code: 'F02',
      Name: 'kelvin per kelvin',
    },
    {
      Code: 'F03',
      Name: 'kilopascal per bar',
    },
    {
      Code: 'F04',
      Name: 'millibar per bar',
    },
    {
      Code: 'F05',
      Name: 'megapascal per bar',
    },
    {
      Code: 'F06',
      Name: 'poise per bar',
    },
    {
      Code: 'F07',
      Name: 'pascal per bar',
    },
    {
      Code: 'F08',
      Name: 'milliampere per inch',
    },
    {
      Code: 'F10',
      Name: 'kelvin per hour',
    },
    {
      Code: 'F11',
      Name: 'kelvin per minute',
    },
    {
      Code: 'F12',
      Name: 'kelvin per second',
    },
    {
      Code: 'F13',
      Name: 'slug',
    },
    {
      Code: 'F14',
      Name: 'gram per kelvin',
    },
    {
      Code: 'F15',
      Name: 'kilogram per kelvin',
    },
    {
      Code: 'F16',
      Name: 'milligram per kelvin',
    },
    {
      Code: 'F17',
      Name: 'pound-force per foot',
    },
    {
      Code: 'F18',
      Name: 'kilogram square centimetre',
    },
    {
      Code: 'F19',
      Name: 'kilogram square millimetre',
    },
    {
      Code: 'F20',
      Name: 'pound inch squared',
    },
    {
      Code: 'F21',
      Name: 'pound-force inch',
    },
    {
      Code: 'F22',
      Name: 'pound-force foot per ampere',
    },
    {
      Code: 'F23',
      Name: 'gram per cubic decimetre',
    },
    {
      Code: 'F24',
      Name: 'kilogram per kilomol',
    },
    {
      Code: 'F25',
      Name: 'gram per hertz',
    },
    {
      Code: 'F26',
      Name: 'gram per day',
    },
    {
      Code: 'F27',
      Name: 'gram per hour',
    },
    {
      Code: 'F28',
      Name: 'gram per minute',
    },
    {
      Code: 'F29',
      Name: 'gram per second',
    },
    {
      Code: 'F30',
      Name: 'kilogram per day',
    },
    {
      Code: 'F31',
      Name: 'kilogram per minute',
    },
    {
      Code: 'F32',
      Name: 'milligram per day',
    },
    {
      Code: 'F33',
      Name: 'milligram per minute',
    },
    {
      Code: 'F34',
      Name: 'milligram per second',
    },
    {
      Code: 'F35',
      Name: 'gram per day kelvin',
    },
    {
      Code: 'F36',
      Name: 'gram per hour kelvin',
    },
    {
      Code: 'F37',
      Name: 'gram per minute kelvin',
    },
    {
      Code: 'F38',
      Name: 'gram per second kelvin',
    },
    {
      Code: 'F39',
      Name: 'kilogram per day kelvin',
    },
    {
      Code: 'F40',
      Name: 'kilogram per hour kelvin',
    },
    {
      Code: 'F41',
      Name: 'kilogram per minute kelvin',
    },
    {
      Code: 'F42',
      Name: 'kilogram per second kelvin',
    },
    {
      Code: 'F43',
      Name: 'milligram per day kelvin',
    },
    {
      Code: 'F44',
      Name: 'milligram per hour kelvin',
    },
    {
      Code: 'F45',
      Name: 'milligram per minute kelvin',
    },
    {
      Code: 'F46',
      Name: 'milligram per second kelvin',
    },
    {
      Code: 'F47',
      Name: 'newton per millimetre',
    },
    {
      Code: 'F48',
      Name: 'pound-force per inch',
    },
    {
      Code: 'F49',
      Name: 'rod [unit of distance]',
    },
    {
      Code: 'F50',
      Name: 'micrometre per kelvin',
    },
    {
      Code: 'F51',
      Name: 'centimetre per kelvin',
    },
    {
      Code: 'F52',
      Name: 'metre per kelvin',
    },
    {
      Code: 'F53',
      Name: 'millimetre per kelvin',
    },
    {
      Code: 'F54',
      Name: 'milliohm per metre',
    },
    {
      Code: 'F55',
      Name: 'ohm per mile (statute mile)',
    },
    {
      Code: 'F56',
      Name: 'ohm per kilometre',
    },
    {
      Code: 'F57',
      Name: 'milliampere per pound-force per square inch',
    },
    {
      Code: 'F58',
      Name: 'reciprocal bar',
    },
    {
      Code: 'F59',
      Name: 'milliampere per bar',
    },
    {
      Code: 'F60',
      Name: 'degree Celsius per bar',
    },
    {
      Code: 'F61',
      Name: 'kelvin per bar',
    },
    {
      Code: 'F62',
      Name: 'gram per day bar',
    },
    {
      Code: 'F63',
      Name: 'gram per hour bar',
    },
    {
      Code: 'F64',
      Name: 'gram per minute bar',
    },
    {
      Code: 'F65',
      Name: 'gram per second bar',
    },
    {
      Code: 'F66',
      Name: 'kilogram per day bar',
    },
    {
      Code: 'F67',
      Name: 'kilogram per hour bar',
    },
    {
      Code: 'F68',
      Name: 'kilogram per minute bar',
    },
    {
      Code: 'F69',
      Name: 'kilogram per second bar',
    },
    {
      Code: 'F70',
      Name: 'milligram per day bar',
    },
    {
      Code: 'F71',
      Name: 'milligram per hour bar',
    },
    {
      Code: 'F72',
      Name: 'milligram per minute bar',
    },
    {
      Code: 'F73',
      Name: 'milligram per second bar',
    },
    {
      Code: 'F74',
      Name: 'gram per bar',
    },
    {
      Code: 'F75',
      Name: 'milligram per bar',
    },
    {
      Code: 'F76',
      Name: 'milliampere per millimetre',
    },
    {
      Code: 'F77',
      Name: 'pascal second per kelvin',
    },
    {
      Code: 'F78',
      Name: 'inch of water',
    },
    {
      Code: 'F79',
      Name: 'inch of mercury',
    },
    {
      Code: 'F80',
      Name: 'water horse power',
    },
    {
      Code: 'F81',
      Name: 'bar per kelvin',
    },
    {
      Code: 'F82',
      Name: 'hectopascal per kelvin',
    },
    {
      Code: 'F83',
      Name: 'kilopascal per kelvin',
    },
    {
      Code: 'F84',
      Name: 'millibar per kelvin',
    },
    {
      Code: 'F85',
      Name: 'megapascal per kelvin',
    },
    {
      Code: 'F86',
      Name: 'poise per kelvin',
    },
    {
      Code: 'F87',
      Name: 'volt per litre minute',
    },
    {
      Code: 'F88',
      Name: 'newton centimetre',
    },
    {
      Code: 'F89',
      Name: 'newton metre per degree',
    },
    {
      Code: 'F90',
      Name: 'newton metre per ampere',
    },
    {
      Code: 'F91',
      Name: 'bar litre per second',
    },
    {
      Code: 'F92',
      Name: 'bar cubic metre per second',
    },
    {
      Code: 'F93',
      Name: 'hectopascal litre per second',
    },
    {
      Code: 'F94',
      Name: 'hectopascal cubic metre per second',
    },
    {
      Code: 'F95',
      Name: 'millibar litre per second',
    },
    {
      Code: 'F96',
      Name: 'millibar cubic metre per second',
    },
    {
      Code: 'F97',
      Name: 'megapascal litre per second',
    },
    {
      Code: 'F98',
      Name: 'megapascal cubic metre per second',
    },
    {
      Code: 'F99',
      Name: 'pascal litre per second',
    },
    {
      Code: 'FAH',
      Name: 'degree Fahrenheit',
    },
    {
      Code: 'FAR',
      Name: 'farad',
    },
    {
      Code: 'FBM',
      Name: 'fibre metre',
    },
    {
      Code: 'FC',
      Name: 'thousand cubic foot',
    },
    {
      Code: 'FF',
      Name: 'hundred cubic metre',
    },
    {
      Code: 'FH',
      Name: 'micromole',
    },
    {
      Code: 'FIT',
      Name: 'failures in time',
    },
    {
      Code: 'FL',
      Name: 'flake ton',
    },
    {
      Code: 'FNU',
      Name: 'Formazin nephelometric unit',
    },
    {
      Code: 'FOT',
      Name: 'foot',
    },
    {
      Code: 'FP',
      Name: 'pound per square foot',
    },
    {
      Code: 'FR',
      Name: 'foot per minute',
    },
    {
      Code: 'FS',
      Name: 'foot per second',
    },
    {
      Code: 'FTK',
      Name: 'square foot',
    },
    {
      Code: 'FTQ',
      Name: 'cubic foot',
    },
    {
      Code: 'G01',
      Name: 'pascal cubic metre per second',
    },
    {
      Code: 'G04',
      Name: 'centimetre per bar',
    },
    {
      Code: 'G05',
      Name: 'metre per bar',
    },
    {
      Code: 'G06',
      Name: 'millimetre per bar',
    },
    {
      Code: 'G08',
      Name: 'square inch per second',
    },
    {
      Code: 'G09',
      Name: 'square metre per second kelvin',
    },
    {
      Code: 'G10',
      Name: 'stokes per kelvin',
    },
    {
      Code: 'G11',
      Name: 'gram per cubic centimetre bar',
    },
    {
      Code: 'G12',
      Name: 'gram per cubic decimetre bar',
    },
    {
      Code: 'G13',
      Name: 'gram per litre bar',
    },
    {
      Code: 'G14',
      Name: 'gram per cubic metre bar',
    },
    {
      Code: 'G15',
      Name: 'gram per millilitre bar',
    },
    {
      Code: 'G16',
      Name: 'kilogram per cubic centimetre bar',
    },
    {
      Code: 'G17',
      Name: 'kilogram per litre bar',
    },
    {
      Code: 'G18',
      Name: 'kilogram per cubic metre bar',
    },
    {
      Code: 'G19',
      Name: 'newton metre per kilogram',
    },
    {
      Code: 'G2',
      Name: 'US gallon per minute',
    },
    {
      Code: 'G20',
      Name: 'pound-force foot per pound',
    },
    {
      Code: 'G21',
      Name: 'cup [unit of volume]',
    },
    {
      Code: 'G23',
      Name: 'peck',
    },
    {
      Code: 'G24',
      Name: 'tablespoon (US)',
    },
    {
      Code: 'G25',
      Name: 'teaspoon (US)',
    },
    {
      Code: 'G26',
      Name: 'stere',
    },
    {
      Code: 'G27',
      Name: 'cubic centimetre per kelvin',
    },
    {
      Code: 'G28',
      Name: 'litre per kelvin',
    },
    {
      Code: 'G29',
      Name: 'cubic metre per kelvin',
    },
    {
      Code: 'G3',
      Name: 'Imperial gallon per minute',
    },
    {
      Code: 'G30',
      Name: 'millilitre per kelvin',
    },
    {
      Code: 'G31',
      Name: 'kilogram per cubic centimetre',
    },
    {
      Code: 'G32',
      Name: 'ounce (avoirdupois) per cubic yard',
    },
    {
      Code: 'G33',
      Name: 'gram per cubic centimetre kelvin',
    },
    {
      Code: 'G34',
      Name: 'gram per cubic decimetre kelvin',
    },
    {
      Code: 'G35',
      Name: 'gram per litre kelvin',
    },
    {
      Code: 'G36',
      Name: 'gram per cubic metre kelvin',
    },
    {
      Code: 'G37',
      Name: 'gram per millilitre kelvin',
    },
    {
      Code: 'G38',
      Name: 'kilogram per cubic centimetre kelvin',
    },
    {
      Code: 'G39',
      Name: 'kilogram per litre kelvin',
    },
    {
      Code: 'G40',
      Name: 'kilogram per cubic metre kelvin',
    },
    {
      Code: 'G41',
      Name: 'square metre per second bar',
    },
    {
      Code: 'G42',
      Name: 'microsiemens per centimetre',
    },
    {
      Code: 'G43',
      Name: 'microsiemens per metre',
    },
    {
      Code: 'G44',
      Name: 'nanosiemens per centimetre',
    },
    {
      Code: 'G45',
      Name: 'nanosiemens per metre',
    },
    {
      Code: 'G46',
      Name: 'stokes per bar',
    },
    {
      Code: 'G47',
      Name: 'cubic centimetre per day',
    },
    {
      Code: 'G48',
      Name: 'cubic centimetre per hour',
    },
    {
      Code: 'G49',
      Name: 'cubic centimetre per minute',
    },
    {
      Code: 'G50',
      Name: 'gallon (US) per hour',
    },
    {
      Code: 'G51',
      Name: 'litre per second',
    },
    {
      Code: 'G52',
      Name: 'cubic metre per day',
    },
    {
      Code: 'G53',
      Name: 'cubic metre per minute',
    },
    {
      Code: 'G54',
      Name: 'millilitre per day',
    },
    {
      Code: 'G55',
      Name: 'millilitre per hour',
    },
    {
      Code: 'G56',
      Name: 'cubic inch per hour',
    },
    {
      Code: 'G57',
      Name: 'cubic inch per minute',
    },
    {
      Code: 'G58',
      Name: 'cubic inch per second',
    },
    {
      Code: 'G59',
      Name: 'milliampere per litre minute',
    },
    {
      Code: 'G60',
      Name: 'volt per bar',
    },
    {
      Code: 'G61',
      Name: 'cubic centimetre per day kelvin',
    },
    {
      Code: 'G62',
      Name: 'cubic centimetre per hour kelvin',
    },
    {
      Code: 'G63',
      Name: 'cubic centimetre per minute kelvin',
    },
    {
      Code: 'G64',
      Name: 'cubic centimetre per second kelvin',
    },
    {
      Code: 'G65',
      Name: 'litre per day kelvin',
    },
    {
      Code: 'G66',
      Name: 'litre per hour kelvin',
    },
    {
      Code: 'G67',
      Name: 'litre per minute kelvin',
    },
    {
      Code: 'G68',
      Name: 'litre per second kelvin',
    },
    {
      Code: 'G69',
      Name: 'cubic metre per day kelvin',
    },
    {
      Code: 'G70',
      Name: 'cubic metre per hour kelvin',
    },
    {
      Code: 'G71',
      Name: 'cubic metre per minute kelvin',
    },
    {
      Code: 'G72',
      Name: 'cubic metre per second kelvin',
    },
    {
      Code: 'G73',
      Name: 'millilitre per day kelvin',
    },
    {
      Code: 'G74',
      Name: 'millilitre per hour kelvin',
    },
    {
      Code: 'G75',
      Name: 'millilitre per minute kelvin',
    },
    {
      Code: 'G76',
      Name: 'millilitre per second kelvin',
    },
    {
      Code: 'G77',
      Name: 'millimetre to the fourth power',
    },
    {
      Code: 'G78',
      Name: 'cubic centimetre per day bar',
    },
    {
      Code: 'G79',
      Name: 'cubic centimetre per hour bar',
    },
    {
      Code: 'G80',
      Name: 'cubic centimetre per minute bar',
    },
    {
      Code: 'G81',
      Name: 'cubic centimetre per second bar',
    },
    {
      Code: 'G82',
      Name: 'litre per day bar',
    },
    {
      Code: 'G83',
      Name: 'litre per hour bar',
    },
    {
      Code: 'G84',
      Name: 'litre per minute bar',
    },
    {
      Code: 'G85',
      Name: 'litre per second bar',
    },
    {
      Code: 'G86',
      Name: 'cubic metre per day bar',
    },
    {
      Code: 'G87',
      Name: 'cubic metre per hour bar',
    },
    {
      Code: 'G88',
      Name: 'cubic metre per minute bar',
    },
    {
      Code: 'G89',
      Name: 'cubic metre per second bar',
    },
    {
      Code: 'G90',
      Name: 'millilitre per day bar',
    },
    {
      Code: 'G91',
      Name: 'millilitre per hour bar',
    },
    {
      Code: 'G92',
      Name: 'millilitre per minute bar',
    },
    {
      Code: 'G93',
      Name: 'millilitre per second bar',
    },
    {
      Code: 'G94',
      Name: 'cubic centimetre per bar',
    },
    {
      Code: 'G95',
      Name: 'litre per bar',
    },
    {
      Code: 'G96',
      Name: 'cubic metre per bar',
    },
    {
      Code: 'G97',
      Name: 'millilitre per bar',
    },
    {
      Code: 'G98',
      Name: 'microhenry per kiloohm',
    },
    {
      Code: 'G99',
      Name: 'microhenry per ohm',
    },
    {
      Code: 'GB',
      Name: 'gallon (US) per day',
    },
    {
      Code: 'GBQ',
      Name: 'gigabecquerel',
    },
    {
      Code: 'GDW',
      Name: 'gram, dry weight',
    },
    {
      Code: 'GE',
      Name: 'pound per gallon (US)',
    },
    {
      Code: 'GF',
      Name: 'gram per metre (gram per 100 centimetres)',
    },
    {
      Code: 'GFI',
      Name: 'gram of fissile isotope',
    },
    {
      Code: 'GGR',
      Name: 'great gross',
    },
    {
      Code: 'GIA',
      Name: 'gill (US)',
    },
    {
      Code: 'GIC',
      Name: 'gram, including container',
    },
    {
      Code: 'GII',
      Name: 'gill (UK)',
    },
    {
      Code: 'GIP',
      Name: 'gram, including inner packaging',
    },
    {
      Code: 'GJ',
      Name: 'gram per millilitre',
    },
    {
      Code: 'GL',
      Name: 'gram per litre',
    },
    {
      Code: 'GLD',
      Name: 'dry gallon (US)',
    },
    {
      Code: 'GLI',
      Name: 'gallon (UK)',
    },
    {
      Code: 'GLL',
      Name: 'gallon (US)',
    },
    {
      Code: 'GM',
      Name: 'gram per square metre',
    },
    {
      Code: 'GO',
      Name: 'milligram per square metre',
    },
    {
      Code: 'GP',
      Name: 'milligram per cubic metre',
    },
    {
      Code: 'GQ',
      Name: 'microgram per cubic metre',
    },
    {
      Code: 'GRM',
      Name: 'gram',
    },
    {
      Code: 'GRN',
      Name: 'grain',
    },
    {
      Code: 'GRO',
      Name: 'gross',
    },
    {
      Code: 'GV',
      Name: 'gigajoule',
    },
    {
      Code: 'GWH',
      Name: 'gigawatt hour',
    },
    {
      Code: 'H03',
      Name: 'henry per kiloohm',
    },
    {
      Code: 'H04',
      Name: 'henry per ohm',
    },
    {
      Code: 'H05',
      Name: 'millihenry per kiloohm',
    },
    {
      Code: 'H06',
      Name: 'millihenry per ohm',
    },
    {
      Code: 'H07',
      Name: 'pascal second per bar',
    },
    {
      Code: 'H08',
      Name: 'microbecquerel',
    },
    {
      Code: 'H09',
      Name: 'reciprocal year',
    },
    {
      Code: 'H10',
      Name: 'reciprocal hour',
    },
    {
      Code: 'H11',
      Name: 'reciprocal month',
    },
    {
      Code: 'H12',
      Name: 'degree Celsius per hour',
    },
    {
      Code: 'H13',
      Name: 'degree Celsius per minute',
    },
    {
      Code: 'H14',
      Name: 'degree Celsius per second',
    },
    {
      Code: 'H15',
      Name: 'square centimetre per gram',
    },
    {
      Code: 'H16',
      Name: 'square decametre',
    },
    {
      Code: 'H18',
      Name: 'square hectometre',
    },
    {
      Code: 'H19',
      Name: 'cubic hectometre',
    },
    {
      Code: 'H20',
      Name: 'cubic kilometre',
    },
    {
      Code: 'H21',
      Name: 'blank',
    },
    {
      Code: 'H22',
      Name: 'volt square inch per pound-force',
    },
    {
      Code: 'H23',
      Name: 'volt per inch',
    },
    {
      Code: 'H24',
      Name: 'volt per microsecond',
    },
    {
      Code: 'H25',
      Name: 'percent per kelvin',
    },
    {
      Code: 'H26',
      Name: 'ohm per metre',
    },
    {
      Code: 'H27',
      Name: 'degree per metre',
    },
    {
      Code: 'H28',
      Name: 'microfarad per kilometre',
    },
    {
      Code: 'H29',
      Name: 'microgram per litre',
    },
    {
      Code: 'H30',
      Name: 'square micrometre (square micron)',
    },
    {
      Code: 'H31',
      Name: 'ampere per kilogram',
    },
    {
      Code: 'H32',
      Name: 'ampere squared second',
    },
    {
      Code: 'H33',
      Name: 'farad per kilometre',
    },
    {
      Code: 'H34',
      Name: 'hertz metre',
    },
    {
      Code: 'H35',
      Name: 'kelvin metre per watt',
    },
    {
      Code: 'H36',
      Name: 'megaohm per kilometre',
    },
    {
      Code: 'H37',
      Name: 'megaohm per metre',
    },
    {
      Code: 'H38',
      Name: 'megaampere',
    },
    {
      Code: 'H39',
      Name: 'megahertz kilometre',
    },
    {
      Code: 'H40',
      Name: 'newton per ampere',
    },
    {
      Code: 'H41',
      Name: 'newton metre watt to the power minus 0,5',
    },
    {
      Code: 'H42',
      Name: 'pascal per metre',
    },
    {
      Code: 'H43',
      Name: 'siemens per centimetre',
    },
    {
      Code: 'H44',
      Name: 'teraohm',
    },
    {
      Code: 'H45',
      Name: 'volt second per metre',
    },
    {
      Code: 'H46',
      Name: 'volt per second',
    },
    {
      Code: 'H47',
      Name: 'watt per cubic metre',
    },
    {
      Code: 'H48',
      Name: 'attofarad',
    },
    {
      Code: 'H49',
      Name: 'centimetre per hour',
    },
    {
      Code: 'H50',
      Name: 'reciprocal cubic centimetre',
    },
    {
      Code: 'H51',
      Name: 'decibel per kilometre',
    },
    {
      Code: 'H52',
      Name: 'decibel per metre',
    },
    {
      Code: 'H53',
      Name: 'kilogram per bar',
    },
    {
      Code: 'H54',
      Name: 'kilogram per cubic decimetre kelvin',
    },
    {
      Code: 'H55',
      Name: 'kilogram per cubic decimetre bar',
    },
    {
      Code: 'H56',
      Name: 'kilogram per square metre second',
    },
    {
      Code: 'H57',
      Name: 'inch per two pi radiant',
    },
    {
      Code: 'H58',
      Name: 'metre per volt second',
    },
    {
      Code: 'H59',
      Name: 'square metre per newton',
    },
    {
      Code: 'H60',
      Name: 'cubic metre per cubic metre',
    },
    {
      Code: 'H61',
      Name: 'millisiemens per centimetre',
    },
    {
      Code: 'H62',
      Name: 'millivolt per minute',
    },
    {
      Code: 'H63',
      Name: 'milligram per square centimetre',
    },
    {
      Code: 'H64',
      Name: 'milligram per gram',
    },
    {
      Code: 'H65',
      Name: 'millilitre per cubic metre',
    },
    {
      Code: 'H66',
      Name: 'millimetre per year',
    },
    {
      Code: 'H67',
      Name: 'millimetre per hour',
    },
    {
      Code: 'H68',
      Name: 'millimole per gram',
    },
    {
      Code: 'H69',
      Name: 'picopascal per kilometre',
    },
    {
      Code: 'H70',
      Name: 'picosecond',
    },
    {
      Code: 'H71',
      Name: 'percent per month',
    },
    {
      Code: 'H72',
      Name: 'percent per hectobar',
    },
    {
      Code: 'H73',
      Name: 'percent per decakelvin',
    },
    {
      Code: 'H74',
      Name: 'watt per metre',
    },
    {
      Code: 'H75',
      Name: 'decapascal',
    },
    {
      Code: 'H76',
      Name: 'gram per millimetre',
    },
    {
      Code: 'H77',
      Name: 'module width',
    },
    {
      Code: 'H79',
      Name: 'French gauge',
    },
    {
      Code: 'H80',
      Name: 'rack unit',
    },
    {
      Code: 'H81',
      Name: 'millimetre per minute',
    },
    {
      Code: 'H82',
      Name: 'big point',
    },
    {
      Code: 'H83',
      Name: 'litre per kilogram',
    },
    {
      Code: 'H84',
      Name: 'gram millimetre',
    },
    {
      Code: 'H85',
      Name: 'reciprocal week',
    },
    {
      Code: 'H87',
      Name: 'piece',
    },
    {
      Code: 'H88',
      Name: 'megaohm kilometre',
    },
    {
      Code: 'H89',
      Name: 'percent per ohm',
    },
    {
      Code: 'H90',
      Name: 'percent per degree',
    },
    {
      Code: 'H91',
      Name: 'percent per ten thousand',
    },
    {
      Code: 'H92',
      Name: 'percent per one hundred thousand',
    },
    {
      Code: 'H93',
      Name: 'percent per hundred',
    },
    {
      Code: 'H94',
      Name: 'percent per thousand',
    },
    {
      Code: 'H95',
      Name: 'percent per volt',
    },
    {
      Code: 'H96',
      Name: 'percent per bar',
    },
    {
      Code: 'H98',
      Name: 'percent per inch',
    },
    {
      Code: 'H99',
      Name: 'percent per metre',
    },
    {
      Code: 'HA',
      Name: 'hank',
    },
    {
      Code: 'HAD',
      Name: 'Piece Day',
    },
    {
      Code: 'HBA',
      Name: 'hectobar',
    },
    {
      Code: 'HBX',
      Name: 'hundred boxes',
    },
    {
      Code: 'HC',
      Name: 'hundred count',
    },
    {
      Code: 'HDW',
      Name: 'hundred kilogram, dry weight',
    },
    {
      Code: 'HEA',
      Name: 'head',
    },
    {
      Code: 'HGM',
      Name: 'hectogram',
    },
    {
      Code: 'HH',
      Name: 'hundred cubic foot',
    },
    {
      Code: 'HIU',
      Name: 'hundred international unit',
    },
    {
      Code: 'HKM',
      Name: 'hundred kilogram, net mass',
    },
    {
      Code: 'HLT',
      Name: 'hectolitre',
    },
    {
      Code: 'HM',
      Name: 'mile per hour (statute mile)',
    },
    {
      Code: 'HMO',
      Name: 'Piece Month',
    },
    {
      Code: 'HMQ',
      Name: 'million cubic metre',
    },
    {
      Code: 'HMT',
      Name: 'hectometre',
    },
    {
      Code: 'HPA',
      Name: 'hectolitre of pure alcohol',
    },
    {
      Code: 'HTZ',
      Name: 'hertz',
    },
    {
      Code: 'HUR',
      Name: 'hour',
    },
    {
      Code: 'IA',
      Name: 'inch pound (pound inch)',
    },
    {
      Code: 'IE',
      Name: 'person',
    },
    {
      Code: 'INH',
      Name: 'inch',
    },
    {
      Code: 'INK',
      Name: 'square inch',
    },
    {
      Code: 'INQ',
      Name: 'cubic inch',
    },
    {
      Code: 'ISD',
      Name: 'international sugar degree',
    },
    {
      Code: 'IU',
      Name: 'inch per second',
    },
    {
      Code: 'IUG',
      Name: 'international unit per gram',
    },
    {
      Code: 'IV',
      Name: 'inch per second squared',
    },
    {
      Code: 'J10',
      Name: 'percent per millimetre',
    },
    {
      Code: 'J12',
      Name: 'per mille per psi',
    },
    {
      Code: 'J13',
      Name: 'degree API',
    },
    {
      Code: 'J14',
      Name: 'degree Baume (origin scale)',
    },
    {
      Code: 'J15',
      Name: 'degree Baume (US heavy)',
    },
    {
      Code: 'J16',
      Name: 'degree Baume (US light)',
    },
    {
      Code: 'J17',
      Name: 'degree Balling',
    },
    {
      Code: 'J18',
      Name: 'degree Brix',
    },
    {
      Code: 'J19',
      Name: 'degree Fahrenheit hour square foot per British thermal unit (thermochemical)',
    },
    {
      Code: 'J2',
      Name: 'joule per kilogram',
    },
    {
      Code: 'J20',
      Name: 'degree Fahrenheit per kelvin',
    },
    {
      Code: 'J21',
      Name: 'degree Fahrenheit per bar',
    },
    {
      Code: 'J22',
      Name: 'degree Fahrenheit hour square foot per British thermal unit (international table)',
    },
    {
      Code: 'J23',
      Name: 'degree Fahrenheit per hour',
    },
    {
      Code: 'J24',
      Name: 'degree Fahrenheit per minute',
    },
    {
      Code: 'J25',
      Name: 'degree Fahrenheit per second',
    },
    {
      Code: 'J26',
      Name: 'reciprocal degree Fahrenheit',
    },
    {
      Code: 'J27',
      Name: 'degree Oechsle',
    },
    {
      Code: 'J28',
      Name: 'degree Rankine per hour',
    },
    {
      Code: 'J29',
      Name: 'degree Rankine per minute',
    },
    {
      Code: 'J30',
      Name: 'degree Rankine per second',
    },
    {
      Code: 'J31',
      Name: 'degree Twaddell',
    },
    {
      Code: 'J32',
      Name: 'micropoise',
    },
    {
      Code: 'J33',
      Name: 'microgram per kilogram',
    },
    {
      Code: 'J34',
      Name: 'microgram per cubic metre kelvin',
    },
    {
      Code: 'J35',
      Name: 'microgram per cubic metre bar',
    },
    {
      Code: 'J36',
      Name: 'microlitre per litre',
    },
    {
      Code: 'J38',
      Name: 'baud',
    },
    {
      Code: 'J39',
      Name: 'British thermal unit (mean)',
    },
    {
      Code: 'J40',
      Name: 'British thermal unit (international table) foot per hourÂ square foot degree Fahrenheit',
    },
    {
      Code: 'J41',
      Name: 'British thermal unit (international table) inch per hour squareÂ foot degree Fahrenheit',
    },
    {
      Code: 'J42',
      Name: 'British thermal unit (international table) inch per second squareÂ foot degree Fahrenheit',
    },
    {
      Code: 'J43',
      Name: 'British thermal unit (international table) per pound degree Fahrenheit',
    },
    {
      Code: 'J44',
      Name: 'British thermal unit (international table) per minute',
    },
    {
      Code: 'J45',
      Name: 'British thermal unit (international table) per second',
    },
    {
      Code: 'J46',
      Name: 'British thermal unit (thermochemical) foot per hour squareÂ foot degree Fahrenheit',
    },
    {
      Code: 'J47',
      Name: 'British thermal unit (thermochemical) per hour',
    },
    {
      Code: 'J48',
      Name: 'British thermal unit (thermochemical) inch per hour squareÂ foot degree Fahrenheit',
    },
    {
      Code: 'J49',
      Name: 'British thermal unit (thermochemical) inch per secondÂ square foot degree Fahrenheit',
    },
    {
      Code: 'J50',
      Name: 'British thermal unit (thermochemical) per pound degree Fahrenheit',
    },
    {
      Code: 'J51',
      Name: 'British thermal unit (thermochemical) per minute',
    },
    {
      Code: 'J52',
      Name: 'British thermal unit (thermochemical) per second',
    },
    {
      Code: 'J53',
      Name: 'coulomb square metre per kilogram',
    },
    {
      Code: 'J54',
      Name: 'megabaud',
    },
    {
      Code: 'J55',
      Name: 'watt second',
    },
    {
      Code: 'J56',
      Name: 'bar per bar',
    },
    {
      Code: 'J57',
      Name: 'barrel (UK petroleum)',
    },
    {
      Code: 'J58',
      Name: 'barrel (UK petroleum) per minute',
    },
    {
      Code: 'J59',
      Name: 'barrel (UK petroleum) per day',
    },
    {
      Code: 'J60',
      Name: 'barrel (UK petroleum) per hour',
    },
    {
      Code: 'J61',
      Name: 'barrel (UK petroleum) per second',
    },
    {
      Code: 'J62',
      Name: 'barrel (US petroleum) per hour',
    },
    {
      Code: 'J63',
      Name: 'barrel (US petroleum) per second',
    },
    {
      Code: 'J64',
      Name: 'bushel (UK) per day',
    },
    {
      Code: 'J65',
      Name: 'bushel (UK) per hour',
    },
    {
      Code: 'J66',
      Name: 'bushel (UK) per minute',
    },
    {
      Code: 'J67',
      Name: 'bushel (UK) per second',
    },
    {
      Code: 'J68',
      Name: 'bushel (US dry) per day',
    },
    {
      Code: 'J69',
      Name: 'bushel (US dry) per hour',
    },
    {
      Code: 'J70',
      Name: 'bushel (US dry) per minute',
    },
    {
      Code: 'J71',
      Name: 'bushel (US dry) per second',
    },
    {
      Code: 'J72',
      Name: 'centinewton metre',
    },
    {
      Code: 'J73',
      Name: 'centipoise per kelvin',
    },
    {
      Code: 'J74',
      Name: 'centipoise per bar',
    },
    {
      Code: 'J75',
      Name: 'calorie (mean)',
    },
    {
      Code: 'J76',
      Name: 'calorie (international table) per gram degree Celsius',
    },
    {
      Code: 'J78',
      Name: 'calorie (thermochemical) per centimetre second degree Celsius',
    },
    {
      Code: 'J79',
      Name: 'calorie (thermochemical) per gram degree Celsius',
    },
    {
      Code: 'J81',
      Name: 'calorie (thermochemical) per minute',
    },
    {
      Code: 'J82',
      Name: 'calorie (thermochemical) per second',
    },
    {
      Code: 'J83',
      Name: 'clo',
    },
    {
      Code: 'J84',
      Name: 'centimetre per second kelvin',
    },
    {
      Code: 'J85',
      Name: 'centimetre per second bar',
    },
    {
      Code: 'J87',
      Name: 'cubic centimetre per cubic metre',
    },
    {
      Code: 'J90',
      Name: 'cubic decimetre per day',
    },
    {
      Code: 'J91',
      Name: 'cubic decimetre per cubic metre',
    },
    {
      Code: 'J92',
      Name: 'cubic decimetre per minute',
    },
    {
      Code: 'J93',
      Name: 'cubic decimetre per second',
    },
    {
      Code: 'J95',
      Name: 'ounce (UK fluid) per day',
    },
    {
      Code: 'J96',
      Name: 'ounce (UK fluid) per hour',
    },
    {
      Code: 'J97',
      Name: 'ounce (UK fluid) per minute',
    },
    {
      Code: 'J98',
      Name: 'ounce (UK fluid) per second',
    },
    {
      Code: 'J99',
      Name: 'ounce (US fluid) per day',
    },
    {
      Code: 'JE',
      Name: 'joule per kelvin',
    },
    {
      Code: 'JK',
      Name: 'megajoule per kilogram',
    },
    {
      Code: 'JM',
      Name: 'megajoule per cubic metre',
    },
    {
      Code: 'JNT',
      Name: 'pipeline joint',
    },
    {
      Code: 'JOU',
      Name: 'joule',
    },
    {
      Code: 'JPS',
      Name: 'hundred metre',
    },
    {
      Code: 'JWL',
      Name: 'number of jewels',
    },
    {
      Code: 'K1',
      Name: 'kilowatt demand',
    },
    {
      Code: 'K10',
      Name: 'ounce (US fluid) per hour',
    },
    {
      Code: 'K11',
      Name: 'ounce (US fluid) per minute',
    },
    {
      Code: 'K12',
      Name: 'ounce (US fluid) per second',
    },
    {
      Code: 'K13',
      Name: 'foot per degree Fahrenheit',
    },
    {
      Code: 'K14',
      Name: 'foot per hour',
    },
    {
      Code: 'K15',
      Name: 'foot pound-force per hour',
    },
    {
      Code: 'K16',
      Name: 'foot pound-force per minute',
    },
    {
      Code: 'K17',
      Name: 'foot per psi',
    },
    {
      Code: 'K18',
      Name: 'foot per second degree Fahrenheit',
    },
    {
      Code: 'K19',
      Name: 'foot per second psi',
    },
    {
      Code: 'K2',
      Name: 'kilovolt ampere reactive demand',
    },
    {
      Code: 'K20',
      Name: 'reciprocal cubic foot',
    },
    {
      Code: 'K21',
      Name: 'cubic foot per degree Fahrenheit',
    },
    {
      Code: 'K22',
      Name: 'cubic foot per day',
    },
    {
      Code: 'K23',
      Name: 'cubic foot per psi',
    },
    {
      Code: 'K26',
      Name: 'gallon (UK) per day',
    },
    {
      Code: 'K27',
      Name: 'gallon (UK) per hour',
    },
    {
      Code: 'K28',
      Name: 'gallon (UK) per second',
    },
    {
      Code: 'K3',
      Name: 'kilovolt ampere reactive hour',
    },
    {
      Code: 'K30',
      Name: 'gallon (US liquid) per second',
    },
    {
      Code: 'K31',
      Name: 'gram-force per square centimetre',
    },
    {
      Code: 'K32',
      Name: 'gill (UK) per day',
    },
    {
      Code: 'K33',
      Name: 'gill (UK) per hour',
    },
    {
      Code: 'K34',
      Name: 'gill (UK) per minute',
    },
    {
      Code: 'K35',
      Name: 'gill (UK) per second',
    },
    {
      Code: 'K36',
      Name: 'gill (US) per day',
    },
    {
      Code: 'K37',
      Name: 'gill (US) per hour',
    },
    {
      Code: 'K38',
      Name: 'gill (US) per minute',
    },
    {
      Code: 'K39',
      Name: 'gill (US) per second',
    },
    {
      Code: 'K40',
      Name: 'standard acceleration of free fall',
    },
    {
      Code: 'K41',
      Name: 'grain per gallon (US)',
    },
    {
      Code: 'K42',
      Name: 'horsepower (boiler)',
    },
    {
      Code: 'K43',
      Name: 'horsepower (electric)',
    },
    {
      Code: 'K45',
      Name: 'inch per degree Fahrenheit',
    },
    {
      Code: 'K46',
      Name: 'inch per psi',
    },
    {
      Code: 'K47',
      Name: 'inch per second degree Fahrenheit',
    },
    {
      Code: 'K48',
      Name: 'inch per second psi',
    },
    {
      Code: 'K49',
      Name: 'reciprocal cubic inch',
    },
    {
      Code: 'K50',
      Name: 'kilobaud',
    },
    {
      Code: 'K51',
      Name: 'kilocalorie (mean)',
    },
    {
      Code: 'K52',
      Name: 'kilocalorie (international table) per hour metre degree Celsius',
    },
    {
      Code: 'K53',
      Name: 'kilocalorie (thermochemical)',
    },
    {
      Code: 'K54',
      Name: 'kilocalorie (thermochemical) per minute',
    },
    {
      Code: 'K55',
      Name: 'kilocalorie (thermochemical) per second',
    },
    {
      Code: 'K58',
      Name: 'kilomole per hour',
    },
    {
      Code: 'K59',
      Name: 'kilomole per cubic metre kelvin',
    },
    {
      Code: 'K6',
      Name: 'kilolitre',
    },
    {
      Code: 'K60',
      Name: 'kilomole per cubic metre bar',
    },
    {
      Code: 'K61',
      Name: 'kilomole per minute',
    },
    {
      Code: 'K62',
      Name: 'litre per litre',
    },
    {
      Code: 'K63',
      Name: 'reciprocal litre',
    },
    {
      Code: 'K64',
      Name: 'pound (avoirdupois) per degree Fahrenheit',
    },
    {
      Code: 'K65',
      Name: 'pound (avoirdupois) square foot',
    },
    {
      Code: 'K66',
      Name: 'pound (avoirdupois) per day',
    },
    {
      Code: 'K67',
      Name: 'pound per foot hour',
    },
    {
      Code: 'K68',
      Name: 'pound per foot second',
    },
    {
      Code: 'K69',
      Name: 'pound (avoirdupois) per cubic foot degree Fahrenheit',
    },
    {
      Code: 'K70',
      Name: 'pound (avoirdupois) per cubic foot psi',
    },
    {
      Code: 'K71',
      Name: 'pound (avoirdupois) per gallon (UK)',
    },
    {
      Code: 'K73',
      Name: 'pound (avoirdupois) per hour degree Fahrenheit',
    },
    {
      Code: 'K74',
      Name: 'pound (avoirdupois) per hour psi',
    },
    {
      Code: 'K75',
      Name: 'pound (avoirdupois) per cubic inch degree Fahrenheit',
    },
    {
      Code: 'K76',
      Name: 'pound (avoirdupois) per cubic inch psi',
    },
    {
      Code: 'K77',
      Name: 'pound (avoirdupois) per psi',
    },
    {
      Code: 'K78',
      Name: 'pound (avoirdupois) per minute',
    },
    {
      Code: 'K79',
      Name: 'pound (avoirdupois) per minute degree Fahrenheit',
    },
    {
      Code: 'K80',
      Name: 'pound (avoirdupois) per minute psi',
    },
    {
      Code: 'K81',
      Name: 'pound (avoirdupois) per second',
    },
    {
      Code: 'K82',
      Name: 'pound (avoirdupois) per second degree Fahrenheit',
    },
    {
      Code: 'K83',
      Name: 'pound (avoirdupois) per second psi',
    },
    {
      Code: 'K84',
      Name: 'pound per cubic yard',
    },
    {
      Code: 'K85',
      Name: 'pound-force per square foot',
    },
    {
      Code: 'K86',
      Name: 'pound-force per square inch degree Fahrenheit',
    },
    {
      Code: 'K87',
      Name: 'psi cubic inch per second',
    },
    {
      Code: 'K88',
      Name: 'psi litre per second',
    },
    {
      Code: 'K89',
      Name: 'psi cubic metre per second',
    },
    {
      Code: 'K90',
      Name: 'psi cubic yard per second',
    },
    {
      Code: 'K91',
      Name: 'pound-force second per square foot',
    },
    {
      Code: 'K92',
      Name: 'pound-force second per square inch',
    },
    {
      Code: 'K93',
      Name: 'reciprocal psi',
    },
    {
      Code: 'K94',
      Name: 'quart (UK liquid) per day',
    },
    {
      Code: 'K95',
      Name: 'quart (UK liquid) per hour',
    },
    {
      Code: 'K96',
      Name: 'quart (UK liquid) per minute',
    },
    {
      Code: 'K97',
      Name: 'quart (UK liquid) per second',
    },
    {
      Code: 'K98',
      Name: 'quart (US liquid) per day',
    },
    {
      Code: 'K99',
      Name: 'quart (US liquid) per hour',
    },
    {
      Code: 'KA',
      Name: 'cake',
    },
    {
      Code: 'KAT',
      Name: 'katal',
    },
    {
      Code: 'KB',
      Name: 'kilocharacter',
    },
    {
      Code: 'KBA',
      Name: 'kilobar',
    },
    {
      Code: 'KCC',
      Name: 'kilogram of choline chloride',
    },
    {
      Code: 'KDW',
      Name: 'kilogram drained net weight',
    },
    {
      Code: 'KEL',
      Name: 'kelvin',
    },
    {
      Code: 'KGM',
      Name: 'kilogram',
    },
    {
      Code: 'KGS',
      Name: 'kilogram per second',
    },
    {
      Code: 'KHY',
      Name: 'kilogram of hydrogen peroxide',
    },
    {
      Code: 'KHZ',
      Name: 'kilohertz',
    },
    {
      Code: 'KI',
      Name: 'kilogram per millimetre width',
    },
    {
      Code: 'KIC',
      Name: 'kilogram, including container',
    },
    {
      Code: 'KIP',
      Name: 'kilogram, including inner packaging',
    },
    {
      Code: 'KJ',
      Name: 'kilosegment',
    },
    {
      Code: 'KJO',
      Name: 'kilojoule',
    },
    {
      Code: 'KL',
      Name: 'kilogram per metre',
    },
    {
      Code: 'KLK',
      Name: 'lactic dry material percentage',
    },
    {
      Code: 'KLX',
      Name: 'kilolux',
    },
    {
      Code: 'KMA',
      Name: 'kilogram of methylamine',
    },
    {
      Code: 'KMH',
      Name: 'kilometre per hour',
    },
    {
      Code: 'KMK',
      Name: 'square kilometre',
    },
    {
      Code: 'KMQ',
      Name: 'kilogram per cubic metre',
    },
    {
      Code: 'KMT',
      Name: 'kilometre',
    },
    {
      Code: 'KNI',
      Name: 'kilogram of nitrogen',
    },
    {
      Code: 'KNM',
      Name: 'kilonewton per square metre',
    },
    {
      Code: 'KNS',
      Name: 'kilogram named substance',
    },
    {
      Code: 'KNT',
      Name: 'knot',
    },
    {
      Code: 'KO',
      Name: 'milliequivalence caustic potash per gram of product',
    },
    {
      Code: 'KPA',
      Name: 'kilopascal',
    },
    {
      Code: 'KPH',
      Name: 'kilogram of potassium hydroxide (caustic potash)',
    },
    {
      Code: 'KPO',
      Name: 'kilogram of potassium oxide',
    },
    {
      Code: 'KPP',
      Name: 'kilogram of phosphorus pentoxide (phosphoric anhydride)',
    },
    {
      Code: 'KR',
      Name: 'kiloroentgen',
    },
    {
      Code: 'KSD',
      Name: 'kilogram of substance 90 % dry',
    },
    {
      Code: 'KSH',
      Name: 'kilogram of sodium hydroxide (caustic soda)',
    },
    {
      Code: 'KT',
      Name: 'kit',
    },
    {
      Code: 'KTN',
      Name: 'kilotonne',
    },
    {
      Code: 'KUR',
      Name: 'kilogram of uranium',
    },
    {
      Code: 'KVA',
      Name: 'kilovolt - ampere',
    },
    {
      Code: 'KVR',
      Name: 'kilovar',
    },
    {
      Code: 'KVT',
      Name: 'kilovolt',
    },
    {
      Code: 'KW',
      Name: 'kilogram per millimetre',
    },
    {
      Code: 'KWH',
      Name: 'kilowatt hour',
    },
    {
      Code: 'KWN',
      Name: 'Kilowatt hour per normalized cubic metre',
    },
    {
      Code: 'KWO',
      Name: 'kilogram of tungsten trioxide',
    },
    {
      Code: 'KWS',
      Name: 'Kilowatt hour per standard cubic metre',
    },
    {
      Code: 'KWT',
      Name: 'kilowatt',
    },
    {
      Code: 'KWY',
      Name: 'kilowatt year',
    },
    {
      Code: 'KX',
      Name: 'millilitre per kilogram',
    },
    {
      Code: 'L10',
      Name: 'quart (US liquid) per minute',
    },
    {
      Code: 'L11',
      Name: 'quart (US liquid) per second',
    },
    {
      Code: 'L12',
      Name: 'metre per second kelvin',
    },
    {
      Code: 'L13',
      Name: 'metre per second bar',
    },
    {
      Code: 'L14',
      Name: 'square metre hour degree Celsius per kilocalorie (international table)',
    },
    {
      Code: 'L15',
      Name: 'millipascal second per kelvin',
    },
    {
      Code: 'L16',
      Name: 'millipascal second per bar',
    },
    {
      Code: 'L17',
      Name: 'milligram per cubic metre kelvin',
    },
    {
      Code: 'L18',
      Name: 'milligram per cubic metre bar',
    },
    {
      Code: 'L19',
      Name: 'millilitre per litre',
    },
    {
      Code: 'L2',
      Name: 'litre per minute',
    },
    {
      Code: 'L20',
      Name: 'reciprocal cubic millimetre',
    },
    {
      Code: 'L21',
      Name: 'cubic millimetre per cubic metre',
    },
    {
      Code: 'L23',
      Name: 'mole per hour',
    },
    {
      Code: 'L24',
      Name: 'mole per kilogram kelvin',
    },
    {
      Code: 'L25',
      Name: 'mole per kilogram bar',
    },
    {
      Code: 'L26',
      Name: 'mole per litre kelvin',
    },
    {
      Code: 'L27',
      Name: 'mole per litre bar',
    },
    {
      Code: 'L28',
      Name: 'mole per cubic metre kelvin',
    },
    {
      Code: 'L29',
      Name: 'mole per cubic metre bar',
    },
    {
      Code: 'L30',
      Name: 'mole per minute',
    },
    {
      Code: 'L31',
      Name: 'milliroentgen aequivalent men',
    },
    {
      Code: 'L32',
      Name: 'nanogram per kilogram',
    },
    {
      Code: 'L33',
      Name: 'ounce (avoirdupois) per day',
    },
    {
      Code: 'L34',
      Name: 'ounce (avoirdupois) per hour',
    },
    {
      Code: 'L35',
      Name: 'ounce (avoirdupois) per minute',
    },
    {
      Code: 'L36',
      Name: 'ounce (avoirdupois) per second',
    },
    {
      Code: 'L37',
      Name: 'ounce (avoirdupois) per gallon (UK)',
    },
    {
      Code: 'L38',
      Name: 'ounce (avoirdupois) per gallon (US)',
    },
    {
      Code: 'L39',
      Name: 'ounce (avoirdupois) per cubic inch',
    },
    {
      Code: 'L40',
      Name: 'ounce (avoirdupois)-force',
    },
    {
      Code: 'L41',
      Name: 'ounce (avoirdupois)-force inch',
    },
    {
      Code: 'L42',
      Name: 'picosiemens per metre',
    },
    {
      Code: 'L43',
      Name: 'peck (UK)',
    },
    {
      Code: 'L44',
      Name: 'peck (UK) per day',
    },
    {
      Code: 'L45',
      Name: 'peck (UK) per hour',
    },
    {
      Code: 'L46',
      Name: 'peck (UK) per minute',
    },
    {
      Code: 'L47',
      Name: 'peck (UK) per second',
    },
    {
      Code: 'L48',
      Name: 'peck (US dry) per day',
    },
    {
      Code: 'L49',
      Name: 'peck (US dry) per hour',
    },
    {
      Code: 'L50',
      Name: 'peck (US dry) per minute',
    },
    {
      Code: 'L51',
      Name: 'peck (US dry) per second',
    },
    {
      Code: 'L52',
      Name: 'psi per psi',
    },
    {
      Code: 'L53',
      Name: 'pint (UK) per day',
    },
    {
      Code: 'L54',
      Name: 'pint (UK) per hour',
    },
    {
      Code: 'L55',
      Name: 'pint (UK) per minute',
    },
    {
      Code: 'L56',
      Name: 'pint (UK) per second',
    },
    {
      Code: 'L57',
      Name: 'pint (US liquid) per day',
    },
    {
      Code: 'L58',
      Name: 'pint (US liquid) per hour',
    },
    {
      Code: 'L59',
      Name: 'pint (US liquid) per minute',
    },
    {
      Code: 'L60',
      Name: 'pint (US liquid) per second',
    },
    {
      Code: 'L63',
      Name: 'slug per day',
    },
    {
      Code: 'L64',
      Name: 'slug per foot second',
    },
    {
      Code: 'L65',
      Name: 'slug per cubic foot',
    },
    {
      Code: 'L66',
      Name: 'slug per hour',
    },
    {
      Code: 'L67',
      Name: 'slug per minute',
    },
    {
      Code: 'L68',
      Name: 'slug per second',
    },
    {
      Code: 'L69',
      Name: 'tonne per kelvin',
    },
    {
      Code: 'L70',
      Name: 'tonne per bar',
    },
    {
      Code: 'L71',
      Name: 'tonne per day',
    },
    {
      Code: 'L72',
      Name: 'tonne per day kelvin',
    },
    {
      Code: 'L73',
      Name: 'tonne per day bar',
    },
    {
      Code: 'L74',
      Name: 'tonne per hour kelvin',
    },
    {
      Code: 'L75',
      Name: 'tonne per hour bar',
    },
    {
      Code: 'L76',
      Name: 'tonne per cubic metre kelvin',
    },
    {
      Code: 'L77',
      Name: 'tonne per cubic metre bar',
    },
    {
      Code: 'L78',
      Name: 'tonne per minute',
    },
    {
      Code: 'L79',
      Name: 'tonne per minute kelvin',
    },
    {
      Code: 'L80',
      Name: 'tonne per minute bar',
    },
    {
      Code: 'L81',
      Name: 'tonne per second',
    },
    {
      Code: 'L82',
      Name: 'tonne per second kelvin',
    },
    {
      Code: 'L83',
      Name: 'tonne per second bar',
    },
    {
      Code: 'L84',
      Name: 'ton (UK shipping)',
    },
    {
      Code: 'L85',
      Name: 'ton long per day',
    },
    {
      Code: 'L86',
      Name: 'ton (US shipping)',
    },
    {
      Code: 'L87',
      Name: 'ton short per degree Fahrenheit',
    },
    {
      Code: 'L88',
      Name: 'ton short per day',
    },
    {
      Code: 'L89',
      Name: 'ton short per hour degree Fahrenheit',
    },
    {
      Code: 'L90',
      Name: 'ton short per hour psi',
    },
    {
      Code: 'L91',
      Name: 'ton short per psi',
    },
    {
      Code: 'L92',
      Name: 'ton (UK long) per cubic yard',
    },
    {
      Code: 'L93',
      Name: 'ton (US short) per cubic yard',
    },
    {
      Code: 'L94',
      Name: 'ton-force (US short)',
    },
    {
      Code: 'L95',
      Name: 'common year',
    },
    {
      Code: 'L96',
      Name: 'sidereal year',
    },
    {
      Code: 'L98',
      Name: 'yard per degree Fahrenheit',
    },
    {
      Code: 'L99',
      Name: 'yard per psi',
    },
    {
      Code: 'LA',
      Name: 'pound per cubic inch',
    },
    {
      Code: 'LAC',
      Name: 'lactose excess percentage',
    },
    {
      Code: 'LBR',
      Name: 'pound',
    },
    {
      Code: 'LBT',
      Name: 'troy pound (US)',
    },
    {
      Code: 'LD',
      Name: 'litre per day',
    },
    {
      Code: 'LEF',
      Name: 'leaf',
    },
    {
      Code: 'LF',
      Name: 'linear foot',
    },
    {
      Code: 'LH',
      Name: 'labour hour',
    },
    {
      Code: 'LK',
      Name: 'link',
    },
    {
      Code: 'LM',
      Name: 'linear metre',
    },
    {
      Code: 'LN',
      Name: 'length',
    },
    {
      Code: 'LO',
      Name: 'lot [unit of procurement]',
    },
    {
      Code: 'LP',
      Name: 'liquid pound',
    },
    {
      Code: 'LPA',
      Name: 'litre of pure alcohol',
    },
    {
      Code: 'LR',
      Name: 'layer',
    },
    {
      Code: 'LS',
      Name: 'lump sum',
    },
    {
      Code: 'LTN',
      Name: 'ton (UK) or long ton (US)',
    },
    {
      Code: 'LTR',
      Name: 'litre',
    },
    {
      Code: 'LUB',
      Name: 'metric ton, lubricating oil',
    },
    {
      Code: 'LUM',
      Name: 'lumen',
    },
    {
      Code: 'LUX',
      Name: 'lux',
    },
    {
      Code: 'LY',
      Name: 'linear yard',
    },
    {
      Code: 'M1',
      Name: 'milligram per litre',
    },
    {
      Code: 'M10',
      Name: 'reciprocal cubic yard',
    },
    {
      Code: 'M11',
      Name: 'cubic yard per degree Fahrenheit',
    },
    {
      Code: 'M12',
      Name: 'cubic yard per day',
    },
    {
      Code: 'M13',
      Name: 'cubic yard per hour',
    },
    {
      Code: 'M14',
      Name: 'cubic yard per psi',
    },
    {
      Code: 'M15',
      Name: 'cubic yard per minute',
    },
    {
      Code: 'M16',
      Name: 'cubic yard per second',
    },
    {
      Code: 'M17',
      Name: 'kilohertz metre',
    },
    {
      Code: 'M18',
      Name: 'gigahertz metre',
    },
    {
      Code: 'M19',
      Name: 'Beaufort',
    },
    {
      Code: 'M20',
      Name: 'reciprocal megakelvin or megakelvin to the power minus one',
    },
    {
      Code: 'M21',
      Name: 'reciprocal kilovolt - ampere reciprocal hour',
    },
    {
      Code: 'M22',
      Name: 'millilitre per square centimetre minute',
    },
    {
      Code: 'M23',
      Name: 'newton per centimetre',
    },
    {
      Code: 'M24',
      Name: 'ohm kilometre',
    },
    {
      Code: 'M25',
      Name: 'percent per degree Celsius',
    },
    {
      Code: 'M26',
      Name: 'gigaohm per metre',
    },
    {
      Code: 'M27',
      Name: 'megahertz metre',
    },
    {
      Code: 'M29',
      Name: 'kilogram per kilogram',
    },
    {
      Code: 'M30',
      Name: 'reciprocal volt - ampere reciprocal second',
    },
    {
      Code: 'M31',
      Name: 'kilogram per kilometre',
    },
    {
      Code: 'M32',
      Name: 'pascal second per litre',
    },
    {
      Code: 'M33',
      Name: 'millimole per litre',
    },
    {
      Code: 'M34',
      Name: 'newton metre per square metre',
    },
    {
      Code: 'M35',
      Name: 'millivolt - ampere',
    },
    {
      Code: 'M36',
      Name: '30-day month',
    },
    {
      Code: 'M37',
      Name: 'actual\/360',
    },
    {
      Code: 'M38',
      Name: 'kilometre per second squared',
    },
    {
      Code: 'M39',
      Name: 'centimetre per second squared',
    },
    {
      Code: 'M4',
      Name: 'monetary value',
    },
    {
      Code: 'M40',
      Name: 'yard per second squared',
    },
    {
      Code: 'M41',
      Name: 'millimetre per second squared',
    },
    {
      Code: 'M42',
      Name: 'mile (statute mile) per second squared',
    },
    {
      Code: 'M43',
      Name: 'mil',
    },
    {
      Code: 'M44',
      Name: 'revolution',
    },
    {
      Code: 'M45',
      Name: 'degree [unit of angle] per second squared',
    },
    {
      Code: 'M46',
      Name: 'revolution per minute',
    },
    {
      Code: 'M47',
      Name: 'circular mil',
    },
    {
      Code: 'M48',
      Name: 'square mile (based on U.S. survey foot)',
    },
    {
      Code: 'M49',
      Name: 'chain (based on U.S. survey foot)',
    },
    {
      Code: 'M5',
      Name: 'microcurie',
    },
    {
      Code: 'M50',
      Name: 'furlong',
    },
    {
      Code: 'M51',
      Name: 'foot (U.S. survey)',
    },
    {
      Code: 'M52',
      Name: 'mile (based on U.S. survey foot)',
    },
    {
      Code: 'M53',
      Name: 'metre per pascal',
    },
    {
      Code: 'M55',
      Name: 'metre per radiant',
    },
    {
      Code: 'M56',
      Name: 'shake',
    },
    {
      Code: 'M57',
      Name: 'mile per minute',
    },
    {
      Code: 'M58',
      Name: 'mile per second',
    },
    {
      Code: 'M59',
      Name: 'metre per second pascal',
    },
    {
      Code: 'M60',
      Name: 'metre per hour',
    },
    {
      Code: 'M61',
      Name: 'inch per year',
    },
    {
      Code: 'M62',
      Name: 'kilometre per second',
    },
    {
      Code: 'M63',
      Name: 'inch per minute',
    },
    {
      Code: 'M64',
      Name: 'yard per second',
    },
    {
      Code: 'M65',
      Name: 'yard per minute',
    },
    {
      Code: 'M66',
      Name: 'yard per hour',
    },
    {
      Code: 'M67',
      Name: 'acre-foot (based on U.S. survey foot)',
    },
    {
      Code: 'M68',
      Name: 'cord (128 ft3)',
    },
    {
      Code: 'M69',
      Name: 'cubic mile (UK statute)',
    },
    {
      Code: 'M7',
      Name: 'micro-inch',
    },
    {
      Code: 'M70',
      Name: 'ton, register',
    },
    {
      Code: 'M71',
      Name: 'cubic metre per pascal',
    },
    {
      Code: 'M72',
      Name: 'bel',
    },
    {
      Code: 'M73',
      Name: 'kilogram per cubic metre pascal',
    },
    {
      Code: 'M74',
      Name: 'kilogram per pascal',
    },
    {
      Code: 'M75',
      Name: 'kilopound-force',
    },
    {
      Code: 'M76',
      Name: 'poundal',
    },
    {
      Code: 'M77',
      Name: 'kilogram metre per second squared',
    },
    {
      Code: 'M78',
      Name: 'pond',
    },
    {
      Code: 'M79',
      Name: 'square foot per hour',
    },
    {
      Code: 'M80',
      Name: 'stokes per pascal',
    },
    {
      Code: 'M81',
      Name: 'square centimetre per second',
    },
    {
      Code: 'M82',
      Name: 'square metre per second pascal',
    },
    {
      Code: 'M83',
      Name: 'denier',
    },
    {
      Code: 'M84',
      Name: 'pound per yard',
    },
    {
      Code: 'M85',
      Name: 'ton, assay',
    },
    {
      Code: 'M86',
      Name: 'pfund',
    },
    {
      Code: 'M87',
      Name: 'kilogram per second pascal',
    },
    {
      Code: 'M88',
      Name: 'tonne per month',
    },
    {
      Code: 'M89',
      Name: 'tonne per year',
    },
    {
      Code: 'M9',
      Name: 'million Btu per 1000 cubic foot',
    },
    {
      Code: 'M90',
      Name: 'kilopound per hour',
    },
    {
      Code: 'M91',
      Name: 'pound per pound',
    },
    {
      Code: 'M92',
      Name: 'pound-force foot',
    },
    {
      Code: 'M93',
      Name: 'newton metre per radian',
    },
    {
      Code: 'M94',
      Name: 'kilogram metre',
    },
    {
      Code: 'M95',
      Name: 'poundal foot',
    },
    {
      Code: 'M96',
      Name: 'poundal inch',
    },
    {
      Code: 'M97',
      Name: 'dyne metre',
    },
    {
      Code: 'M98',
      Name: 'kilogram centimetre per second',
    },
    {
      Code: 'M99',
      Name: 'gram centimetre per second',
    },
    {
      Code: 'MAH',
      Name: 'megavolt ampere reactive hour',
    },
    {
      Code: 'MAL',
      Name: 'megalitre',
    },
    {
      Code: 'MAM',
      Name: 'megametre',
    },
    {
      Code: 'MAR',
      Name: 'megavar',
    },
    {
      Code: 'MAW',
      Name: 'megawatt',
    },
    {
      Code: 'MBE',
      Name: 'thousand standard brick equivalent',
    },
    {
      Code: 'MBF',
      Name: 'thousand board foot',
    },
    {
      Code: 'MBR',
      Name: 'millibar',
    },
    {
      Code: 'MC',
      Name: 'microgram',
    },
    {
      Code: 'MCU',
      Name: 'millicurie',
    },
    {
      Code: 'MD',
      Name: 'air dry metric ton',
    },
    {
      Code: 'MGM',
      Name: 'milligram',
    },
    {
      Code: 'MHZ',
      Name: 'megahertz',
    },
    {
      Code: 'MIK',
      Name: 'square mile (statute mile)',
    },
    {
      Code: 'MIL',
      Name: 'thousand',
    },
    {
      Code: 'MIN',
      Name: 'minute [unit of time]',
    },
    {
      Code: 'MIO',
      Name: 'million',
    },
    {
      Code: 'MIU',
      Name: 'million international unit',
    },
    {
      Code: 'MKD',
      Name: 'Square Metre Day',
    },
    {
      Code: 'MKM',
      Name: 'Square Metre Month',
    },
    {
      Code: 'MKW',
      Name: 'Square Metre Week',
    },
    {
      Code: 'MLD',
      Name: 'milliard',
    },
    {
      Code: 'MLT',
      Name: 'millilitre',
    },
    {
      Code: 'MMK',
      Name: 'square millimetre',
    },
    {
      Code: 'MMQ',
      Name: 'cubic millimetre',
    },
    {
      Code: 'MMT',
      Name: 'millimetre',
    },
    {
      Code: 'MND',
      Name: 'kilogram, dry weight',
    },
    {
      Code: 'MNJ',
      Name: 'Mega Joule per Normalised cubic Metre',
    },
    {
      Code: 'MON',
      Name: 'month',
    },
    {
      Code: 'MPA',
      Name: 'megapascal',
    },
    {
      Code: 'MQD',
      Name: 'Cubic Metre Day',
    },
    {
      Code: 'MQH',
      Name: 'cubic metre per hour',
    },
    {
      Code: 'MQM',
      Name: 'Cubic Metre Month',
    },
    {
      Code: 'MQS',
      Name: 'cubic metre per second',
    },
    {
      Code: 'MQW',
      Name: 'Cubic Metre Week',
    },
    {
      Code: 'MRD',
      Name: 'Metre Day',
    },
    {
      Code: 'MRM',
      Name: 'Metre Month',
    },
    {
      Code: 'MRW',
      Name: 'Metre Week',
    },
    {
      Code: 'MSK',
      Name: 'metre per second squared',
    },
    {
      Code: 'MTK',
      Name: 'square metre',
    },
    {
      Code: 'MTQ',
      Name: 'cubic metre',
    },
    {
      Code: 'MTR',
      Name: 'metre',
    },
    {
      Code: 'MTS',
      Name: 'metre per second',
    },
    {
      Code: 'MTZ',
      Name: 'milihertz',
    },
    {
      Code: 'MVA',
      Name: 'megavolt - ampere',
    },
    {
      Code: 'MWH',
      Name: 'megawatt hour (1000Â kW.h)',
    },
    {
      Code: 'N1',
      Name: 'pen calorie',
    },
    {
      Code: 'N10',
      Name: 'pound foot per second',
    },
    {
      Code: 'N11',
      Name: 'pound inch per second',
    },
    {
      Code: 'N12',
      Name: 'Pferdestaerke',
    },
    {
      Code: 'N13',
      Name: 'centimetre of mercury (0 ÂºC)',
    },
    {
      Code: 'N14',
      Name: 'centimetre of water (4 ÂºC)',
    },
    {
      Code: 'N15',
      Name: 'foot of water (39.2 ÂºF)',
    },
    {
      Code: 'N16',
      Name: 'inch of mercury (32 ÂºF)',
    },
    {
      Code: 'N17',
      Name: 'inch of mercury (60 ÂºF)',
    },
    {
      Code: 'N18',
      Name: 'inch of water (39.2 ÂºF)',
    },
    {
      Code: 'N19',
      Name: 'inch of water (60 ÂºF)',
    },
    {
      Code: 'N20',
      Name: 'kip per square inch',
    },
    {
      Code: 'N21',
      Name: 'poundal per square foot',
    },
    {
      Code: 'N22',
      Name: 'ounce (avoirdupois) per square inch',
    },
    {
      Code: 'N23',
      Name: 'conventional metre of water',
    },
    {
      Code: 'N24',
      Name: 'gram per square millimetre',
    },
    {
      Code: 'N25',
      Name: 'pound per square yard',
    },
    {
      Code: 'N26',
      Name: 'poundal per square inch',
    },
    {
      Code: 'N27',
      Name: 'foot to the fourth power',
    },
    {
      Code: 'N28',
      Name: 'cubic decimetre per kilogram',
    },
    {
      Code: 'N29',
      Name: 'cubic foot per pound',
    },
    {
      Code: 'N3',
      Name: 'print point',
    },
    {
      Code: 'N30',
      Name: 'cubic inch per pound',
    },
    {
      Code: 'N31',
      Name: 'kilonewton per metre',
    },
    {
      Code: 'N32',
      Name: 'poundal per inch',
    },
    {
      Code: 'N33',
      Name: 'pound-force per yard',
    },
    {
      Code: 'N34',
      Name: 'poundal second per square foot',
    },
    {
      Code: 'N35',
      Name: 'poise per pascal',
    },
    {
      Code: 'N36',
      Name: 'newton second per square metre',
    },
    {
      Code: 'N37',
      Name: 'kilogram per metre second',
    },
    {
      Code: 'N38',
      Name: 'kilogram per metre minute',
    },
    {
      Code: 'N39',
      Name: 'kilogram per metre day',
    },
    {
      Code: 'N40',
      Name: 'kilogram per metre hour',
    },
    {
      Code: 'N41',
      Name: 'gram per centimetre second',
    },
    {
      Code: 'N42',
      Name: 'poundal second per square inch',
    },
    {
      Code: 'N43',
      Name: 'pound per foot minute',
    },
    {
      Code: 'N44',
      Name: 'pound per foot day',
    },
    {
      Code: 'N45',
      Name: 'cubic metre per second pascal',
    },
    {
      Code: 'N46',
      Name: 'foot poundal',
    },
    {
      Code: 'N47',
      Name: 'inch poundal',
    },
    {
      Code: 'N48',
      Name: 'watt per square centimetre',
    },
    {
      Code: 'N49',
      Name: 'watt per square inch',
    },
    {
      Code: 'N50',
      Name: 'British thermal unit (international table) per square foot hour',
    },
    {
      Code: 'N51',
      Name: 'British thermal unit (thermochemical) per square foot hour',
    },
    {
      Code: 'N52',
      Name: 'British thermal unit (thermochemical) per square foot minute',
    },
    {
      Code: 'N53',
      Name: 'British thermal unit (international table) per square foot second',
    },
    {
      Code: 'N54',
      Name: 'British thermal unit (thermochemical) per square foot second',
    },
    {
      Code: 'N55',
      Name: 'British thermal unit (international table) per square inch second',
    },
    {
      Code: 'N56',
      Name: 'calorie (thermochemical) per square centimetre minute',
    },
    {
      Code: 'N57',
      Name: 'calorie (thermochemical) per square centimetre second',
    },
    {
      Code: 'N58',
      Name: 'British thermal unit (international table) per cubic foot',
    },
    {
      Code: 'N59',
      Name: 'British thermal unit (thermochemical) per cubic foot',
    },
    {
      Code: 'N60',
      Name: 'British thermal unit (international table) per degree Fahrenheit',
    },
    {
      Code: 'N61',
      Name: 'British thermal unit (thermochemical) per degree Fahrenheit',
    },
    {
      Code: 'N62',
      Name: 'British thermal unit (international table) per degree Rankine',
    },
    {
      Code: 'N63',
      Name: 'British thermal unit (thermochemical) per degree Rankine',
    },
    {
      Code: 'N64',
      Name: 'British thermal unit (thermochemical) per pound degree Rankine',
    },
    {
      Code: 'N65',
      Name: 'kilocalorie (international table) per gram kelvin',
    },
    {
      Code: 'N66',
      Name: 'British thermal unit (39 ÂºF)',
    },
    {
      Code: 'N67',
      Name: 'British thermal unit (59 ÂºF)',
    },
    {
      Code: 'N68',
      Name: 'British thermal unit (60 ÂºF)',
    },
    {
      Code: 'N69',
      Name: 'calorie (20 ÂºC)',
    },
    {
      Code: 'N70',
      Name: 'quad (1015 BtuIT)',
    },
    {
      Code: 'N71',
      Name: 'therm (EC)',
    },
    {
      Code: 'N72',
      Name: 'therm (U.S.)',
    },
    {
      Code: 'N73',
      Name: 'British thermal unit (thermochemical) per pound',
    },
    {
      Code: 'N74',
      Name: 'British thermal unit (international table) per hour square foot degree Fahrenheit',
    },
    {
      Code: 'N75',
      Name: 'British thermal unit (thermochemical) per hour square foot degree Fahrenheit',
    },
    {
      Code: 'N76',
      Name: 'British thermal unit (international table) per second square foot degree Fahrenheit',
    },
    {
      Code: 'N77',
      Name: 'British thermal unit (thermochemical) per second square foot degree Fahrenheit',
    },
    {
      Code: 'N78',
      Name: 'kilowatt per square metre kelvin',
    },
    {
      Code: 'N79',
      Name: 'kelvin per pascal',
    },
    {
      Code: 'N80',
      Name: 'watt per metre degree Celsius',
    },
    {
      Code: 'N81',
      Name: 'kilowatt per metre kelvin',
    },
    {
      Code: 'N82',
      Name: 'kilowatt per metre degree Celsius',
    },
    {
      Code: 'N83',
      Name: 'metre per degree Celcius metre',
    },
    {
      Code: 'N84',
      Name: 'degree Fahrenheit hour per British thermal unit (international table)',
    },
    {
      Code: 'N85',
      Name: 'degree Fahrenheit hour per British thermal unit (thermochemical)',
    },
    {
      Code: 'N86',
      Name: 'degree Fahrenheit second per British thermal unit (international table)',
    },
    {
      Code: 'N87',
      Name: 'degree Fahrenheit second per British thermal unit (thermochemical)',
    },
    {
      Code: 'N88',
      Name: 'degree Fahrenheit hour square foot per British thermal unit (international table) inch',
    },
    {
      Code: 'N89',
      Name: 'degree Fahrenheit hour square foot per British thermal unit (thermochemical) inch',
    },
    {
      Code: 'N90',
      Name: 'kilofarad',
    },
    {
      Code: 'N91',
      Name: 'reciprocal joule',
    },
    {
      Code: 'N92',
      Name: 'picosiemens',
    },
    {
      Code: 'N93',
      Name: 'ampere per pascal',
    },
    {
      Code: 'N94',
      Name: 'franklin',
    },
    {
      Code: 'N95',
      Name: 'ampere minute',
    },
    {
      Code: 'N96',
      Name: 'biot',
    },
    {
      Code: 'N97',
      Name: 'gilbert',
    },
    {
      Code: 'N98',
      Name: 'volt per pascal',
    },
    {
      Code: 'N99',
      Name: 'picovolt',
    },
    {
      Code: 'NA',
      Name: 'milligram per kilogram',
    },
    {
      Code: 'NAR',
      Name: 'number of articles',
    },
    {
      Code: 'NCL',
      Name: 'number of cells',
    },
    {
      Code: 'NEW',
      Name: 'newton',
    },
    {
      Code: 'NF',
      Name: 'message',
    },
    {
      Code: 'NIL',
      Name: 'nil',
    },
    {
      Code: 'NIU',
      Name: 'number of international units',
    },
    {
      Code: 'NL',
      Name: 'load',
    },
    {
      Code: 'NM3',
      Name: 'Normalised cubic metre',
    },
    {
      Code: 'NMI',
      Name: 'nautical mile',
    },
    {
      Code: 'NMP',
      Name: 'number of packs',
    },
    {
      Code: 'NPT',
      Name: 'number of parts',
    },
    {
      Code: 'NT',
      Name: 'net ton',
    },
    {
      Code: 'NTU',
      Name: 'Nephelometric turbidity unit',
    },
    {
      Code: 'NU',
      Name: 'newton metre',
    },
    {
      Code: 'NX',
      Name: 'part per thousand',
    },
    {
      Code: 'OA',
      Name: 'panel',
    },
    {
      Code: 'ODE',
      Name: 'ozone depletion equivalent',
    },
    {
      Code: 'ODG',
      Name: 'ODS Grams',
    },
    {
      Code: 'ODK',
      Name: 'ODS Kilograms',
    },
    {
      Code: 'ODM',
      Name: 'ODS Milligrams',
    },
    {
      Code: 'OHM',
      Name: 'ohm',
    },
    {
      Code: 'ON',
      Name: 'ounce per square yard',
    },
    {
      Code: 'ONZ',
      Name: 'ounce (avoirdupois)',
    },
    {
      Code: 'OPM',
      Name: 'oscillations per minute',
    },
    {
      Code: 'OT',
      Name: 'overtime hour',
    },
    {
      Code: 'OZA',
      Name: 'fluid ounce (US)',
    },
    {
      Code: 'OZI',
      Name: 'fluid ounce (UK)',
    },
    {
      Code: 'P1',
      Name: 'percent',
    },
    {
      Code: 'P10',
      Name: 'coulomb per metre',
    },
    {
      Code: 'P11',
      Name: 'kiloweber',
    },
    {
      Code: 'P12',
      Name: 'gamma',
    },
    {
      Code: 'P13',
      Name: 'kilotesla',
    },
    {
      Code: 'P14',
      Name: 'joule per second',
    },
    {
      Code: 'P15',
      Name: 'joule per minute',
    },
    {
      Code: 'P16',
      Name: 'joule per hour',
    },
    {
      Code: 'P17',
      Name: 'joule per day',
    },
    {
      Code: 'P18',
      Name: 'kilojoule per second',
    },
    {
      Code: 'P19',
      Name: 'kilojoule per minute',
    },
    {
      Code: 'P2',
      Name: 'pound per foot',
    },
    {
      Code: 'P20',
      Name: 'kilojoule per hour',
    },
    {
      Code: 'P21',
      Name: 'kilojoule per day',
    },
    {
      Code: 'P22',
      Name: 'nanoohm',
    },
    {
      Code: 'P23',
      Name: 'ohm circular-mil per foot',
    },
    {
      Code: 'P24',
      Name: 'kilohenry',
    },
    {
      Code: 'P25',
      Name: 'lumen per square foot',
    },
    {
      Code: 'P26',
      Name: 'phot',
    },
    {
      Code: 'P27',
      Name: 'footcandle',
    },
    {
      Code: 'P28',
      Name: 'candela per square inch',
    },
    {
      Code: 'P29',
      Name: 'footlambert',
    },
    {
      Code: 'P30',
      Name: 'lambert',
    },
    {
      Code: 'P31',
      Name: 'stilb',
    },
    {
      Code: 'P32',
      Name: 'candela per square foot',
    },
    {
      Code: 'P33',
      Name: 'kilocandela',
    },
    {
      Code: 'P34',
      Name: 'millicandela',
    },
    {
      Code: 'P35',
      Name: 'Hefner-Kerze',
    },
    {
      Code: 'P36',
      Name: 'international candle',
    },
    {
      Code: 'P37',
      Name: 'British thermal unit (international table) per square foot',
    },
    {
      Code: 'P38',
      Name: 'British thermal unit (thermochemical) per square foot',
    },
    {
      Code: 'P39',
      Name: 'calorie (thermochemical) per square centimetre',
    },
    {
      Code: 'P40',
      Name: 'langley',
    },
    {
      Code: 'P41',
      Name: 'decade (logarithmic)',
    },
    {
      Code: 'P42',
      Name: 'pascal squared second',
    },
    {
      Code: 'P43',
      Name: 'bel per metre',
    },
    {
      Code: 'P44',
      Name: 'pound mole',
    },
    {
      Code: 'P45',
      Name: 'pound mole per second',
    },
    {
      Code: 'P46',
      Name: 'pound mole per minute',
    },
    {
      Code: 'P47',
      Name: 'kilomole per kilogram',
    },
    {
      Code: 'P48',
      Name: 'pound mole per pound',
    },
    {
      Code: 'P49',
      Name: 'newton square metre per ampere',
    },
    {
      Code: 'P5',
      Name: 'five pack',
    },
    {
      Code: 'P50',
      Name: 'weber metre',
    },
    {
      Code: 'P51',
      Name: 'mol per kilogram pascal',
    },
    {
      Code: 'P52',
      Name: 'mol per cubic metre pascal',
    },
    {
      Code: 'P53',
      Name: 'unit pole',
    },
    {
      Code: 'P54',
      Name: 'milligray per second',
    },
    {
      Code: 'P55',
      Name: 'microgray per second',
    },
    {
      Code: 'P56',
      Name: 'nanogray per second',
    },
    {
      Code: 'P57',
      Name: 'gray per minute',
    },
    {
      Code: 'P58',
      Name: 'milligray per minute',
    },
    {
      Code: 'P59',
      Name: 'microgray per minute',
    },
    {
      Code: 'P60',
      Name: 'nanogray per minute',
    },
    {
      Code: 'P61',
      Name: 'gray per hour',
    },
    {
      Code: 'P62',
      Name: 'milligray per hour',
    },
    {
      Code: 'P63',
      Name: 'microgray per hour',
    },
    {
      Code: 'P64',
      Name: 'nanogray per hour',
    },
    {
      Code: 'P65',
      Name: 'sievert per second',
    },
    {
      Code: 'P66',
      Name: 'millisievert per second',
    },
    {
      Code: 'P67',
      Name: 'microsievert per second',
    },
    {
      Code: 'P68',
      Name: 'nanosievert per second',
    },
    {
      Code: 'P69',
      Name: 'rem per second',
    },
    {
      Code: 'P70',
      Name: 'sievert per hour',
    },
    {
      Code: 'P71',
      Name: 'millisievert per hour',
    },
    {
      Code: 'P72',
      Name: 'microsievert per hour',
    },
    {
      Code: 'P73',
      Name: 'nanosievert per hour',
    },
    {
      Code: 'P74',
      Name: 'sievert per minute',
    },
    {
      Code: 'P75',
      Name: 'millisievert per minute',
    },
    {
      Code: 'P76',
      Name: 'microsievert per minute',
    },
    {
      Code: 'P77',
      Name: 'nanosievert per minute',
    },
    {
      Code: 'P78',
      Name: 'reciprocal square inch',
    },
    {
      Code: 'P79',
      Name: 'pascal square metre per kilogram',
    },
    {
      Code: 'P80',
      Name: 'millipascal per metre',
    },
    {
      Code: 'P81',
      Name: 'kilopascal per metre',
    },
    {
      Code: 'P82',
      Name: 'hectopascal per metre',
    },
    {
      Code: 'P83',
      Name: 'standard atmosphere per metre',
    },
    {
      Code: 'P84',
      Name: 'technical atmosphere per metre',
    },
    {
      Code: 'P85',
      Name: 'torr per metre',
    },
    {
      Code: 'P86',
      Name: 'psi per inch',
    },
    {
      Code: 'P87',
      Name: 'cubic metre per second square metre',
    },
    {
      Code: 'P88',
      Name: 'rhe',
    },
    {
      Code: 'P89',
      Name: 'pound-force foot per inch',
    },
    {
      Code: 'P90',
      Name: 'pound-force inch per inch',
    },
    {
      Code: 'P91',
      Name: 'perm (0 ÂºC)',
    },
    {
      Code: 'P92',
      Name: 'perm (23 ÂºC)',
    },
    {
      Code: 'P93',
      Name: 'byte per second',
    },
    {
      Code: 'P94',
      Name: 'kilobyte per second',
    },
    {
      Code: 'P95',
      Name: 'megabyte per second',
    },
    {
      Code: 'P96',
      Name: 'reciprocal volt',
    },
    {
      Code: 'P97',
      Name: 'reciprocal radian',
    },
    {
      Code: 'P98',
      Name: 'pascal to the power sum of stoichiometric numbers',
    },
    {
      Code: 'P99',
      Name: 'mole per cubiv metre to the power sum of stoichiometric numbers',
    },
    {
      Code: 'PAL',
      Name: 'pascal',
    },
    {
      Code: 'PD',
      Name: 'pad',
    },
    {
      Code: 'PFL',
      Name: 'proof litre',
    },
    {
      Code: 'PGL',
      Name: 'proof gallon',
    },
    {
      Code: 'PI',
      Name: 'pitch',
    },
    {
      Code: 'PLA',
      Name: 'degree Plato',
    },
    {
      Code: 'PO',
      Name: 'pound per inch of length',
    },
    {
      Code: 'PQ',
      Name: 'page per inch',
    },
    {
      Code: 'PR',
      Name: 'pair',
    },
    {
      Code: 'PS',
      Name: 'pound-force per square inch',
    },
    {
      Code: 'PTD',
      Name: 'dry pint (US)',
    },
    {
      Code: 'PTI',
      Name: 'pint (UK)',
    },
    {
      Code: 'PTL',
      Name: 'liquid pint (US)',
    },
    {
      Code: 'PTN',
      Name: 'portion',
    },
    {
      Code: 'Q10',
      Name: 'joule per tesla',
    },
    {
      Code: 'Q11',
      Name: 'erlang',
    },
    {
      Code: 'Q12',
      Name: 'octet',
    },
    {
      Code: 'Q13',
      Name: 'octet per second',
    },
    {
      Code: 'Q14',
      Name: 'shannon',
    },
    {
      Code: 'Q15',
      Name: 'hartley',
    },
    {
      Code: 'Q16',
      Name: 'natural unit of information',
    },
    {
      Code: 'Q17',
      Name: 'shannon per second',
    },
    {
      Code: 'Q18',
      Name: 'hartley per second',
    },
    {
      Code: 'Q19',
      Name: 'natural unit of information per second',
    },
    {
      Code: 'Q20',
      Name: 'second per kilogramm',
    },
    {
      Code: 'Q21',
      Name: 'watt square metre',
    },
    {
      Code: 'Q22',
      Name: 'second per radian cubic metre',
    },
    {
      Code: 'Q23',
      Name: 'weber to the power minus one',
    },
    {
      Code: 'Q24',
      Name: 'reciprocal inch',
    },
    {
      Code: 'Q25',
      Name: 'dioptre',
    },
    {
      Code: 'Q26',
      Name: 'one per one',
    },
    {
      Code: 'Q27',
      Name: 'newton metre per metre',
    },
    {
      Code: 'Q28',
      Name: 'kilogram per square metre pascal second',
    },
    {
      Code: 'Q29',
      Name: 'microgram per hectogram',
    },
    {
      Code: 'Q3',
      Name: 'meal',
    },
    {
      Code: 'Q30',
      Name: 'pH (potential of Hydrogen)',
    },
    {
      Code: 'Q31',
      Name: 'kilojoule per gram',
    },
    {
      Code: 'Q32',
      Name: 'femtolitre',
    },
    {
      Code: 'Q33',
      Name: 'picolitre',
    },
    {
      Code: 'Q34',
      Name: 'nanolitre',
    },
    {
      Code: 'Q35',
      Name: 'megawatts per minute',
    },
    {
      Code: 'Q36',
      Name: 'square metre per cubic metre',
    },
    {
      Code: 'Q37',
      Name: 'Standard cubic metre per day',
    },
    {
      Code: 'Q38',
      Name: 'Standard cubic metre per hour',
    },
    {
      Code: 'Q39',
      Name: 'Normalized cubic metre per day',
    },
    {
      Code: 'Q40',
      Name: 'Normalized cubic metre per hour',
    },
    {
      Code: 'Q41',
      Name: 'Joule per normalised cubic metre',
    },
    {
      Code: 'Q42',
      Name: 'Joule per standard cubic metre',
    },
    {
      Code: 'QA',
      Name: 'page - facsimile',
    },
    {
      Code: 'QAN',
      Name: 'quarter (of a year)',
    },
    {
      Code: 'QB',
      Name: 'page - hardcopy',
    },
    {
      Code: 'QR',
      Name: 'quire',
    },
    {
      Code: 'QTD',
      Name: 'dry quart (US)',
    },
    {
      Code: 'QTI',
      Name: 'quart (UK)',
    },
    {
      Code: 'QTL',
      Name: 'liquid quart (US)',
    },
    {
      Code: 'QTR',
      Name: 'quarter (UK)',
    },
    {
      Code: 'R1',
      Name: 'pica',
    },
    {
      Code: 'R9',
      Name: 'thousand cubic metre',
    },
    {
      Code: 'RH',
      Name: 'running or operating hour',
    },
    {
      Code: 'RM',
      Name: 'ream',
    },
    {
      Code: 'ROM',
      Name: 'room',
    },
    {
      Code: 'RP',
      Name: 'pound per ream',
    },
    {
      Code: 'RPM',
      Name: 'revolutions per minute',
    },
    {
      Code: 'RPS',
      Name: 'revolutions per second',
    },
    {
      Code: 'RT',
      Name: 'revenue ton mile',
    },
    {
      Code: 'S3',
      Name: 'square foot per second',
    },
    {
      Code: 'S4',
      Name: 'square metre per second',
    },
    {
      Code: 'SAN',
      Name: 'half year (6 months)',
    },
    {
      Code: 'SCO',
      Name: 'score',
    },
    {
      Code: 'SCR',
      Name: 'scruple',
    },
    {
      Code: 'SEC',
      Name: 'second [unit of time]',
    },
    {
      Code: 'SET',
      Name: 'set',
    },
    {
      Code: 'SG',
      Name: 'segment',
    },
    {
      Code: 'SIE',
      Name: 'siemens',
    },
    {
      Code: 'SM3',
      Name: 'Standard cubic metre',
    },
    {
      Code: 'SMI',
      Name: 'mile (statute mile)',
    },
    {
      Code: 'SQ',
      Name: 'square',
    },
    {
      Code: 'SQR',
      Name: 'square, roofing',
    },
    {
      Code: 'SR',
      Name: 'strip',
    },
    {
      Code: 'STC',
      Name: 'stick',
    },
    {
      Code: 'STI',
      Name: 'stone (UK)',
    },
    {
      Code: 'STK',
      Name: 'stick, cigarette',
    },
    {
      Code: 'STL',
      Name: 'standard litre',
    },
    {
      Code: 'STN',
      Name: 'ton (US) or short ton (UK\/US)',
    },
    {
      Code: 'STW',
      Name: 'straw',
    },
    {
      Code: 'SW',
      Name: 'skein',
    },
    {
      Code: 'SX',
      Name: 'shipment',
    },
    {
      Code: 'SYR',
      Name: 'syringe',
    },
    {
      Code: 'T0',
      Name: 'telecommunication line in service',
    },
    {
      Code: 'T3',
      Name: 'thousand piece',
    },
    {
      Code: 'TAH',
      Name: 'kiloampere hour (thousand ampere hour)',
    },
    {
      Code: 'TAN',
      Name: 'total acid number',
    },
    {
      Code: 'TI',
      Name: 'thousand square inch',
    },
    {
      Code: 'TIC',
      Name: 'metric ton, including container',
    },
    {
      Code: 'TIP',
      Name: 'metric ton, including inner packaging',
    },
    {
      Code: 'TKM',
      Name: 'tonne kilometre',
    },
    {
      Code: 'TMS',
      Name: 'kilogram of imported meat, less offal',
    },
    {
      Code: 'TNE',
      Name: 'tonne (metric ton)',
    },
    {
      Code: 'TP',
      Name: 'ten pack',
    },
    {
      Code: 'TPI',
      Name: 'teeth per inch',
    },
    {
      Code: 'TPR',
      Name: 'ten pair',
    },
    {
      Code: 'TQD',
      Name: 'thousand cubic metre per day',
    },
    {
      Code: 'TRL',
      Name: 'trillion (EUR)',
    },
    {
      Code: 'TST',
      Name: 'ten set',
    },
    {
      Code: 'TTS',
      Name: 'ten thousand sticks',
    },
    {
      Code: 'U1',
      Name: 'treatment',
    },
    {
      Code: 'U2',
      Name: 'tablet',
    },
    {
      Code: 'UB',
      Name: 'telecommunication line in service average',
    },
    {
      Code: 'UC',
      Name: 'telecommunication port',
    },
    {
      Code: 'VA',
      Name: 'volt - ampere per kilogram',
    },
    {
      Code: 'VLT',
      Name: 'volt',
    },
    {
      Code: 'VP',
      Name: 'percent volume',
    },
    {
      Code: 'W2',
      Name: 'wet kilo',
    },
    {
      Code: 'WA',
      Name: 'watt per kilogram',
    },
    {
      Code: 'WB',
      Name: 'wet pound',
    },
    {
      Code: 'WCD',
      Name: 'cord',
    },
    {
      Code: 'WE',
      Name: 'wet ton',
    },
    {
      Code: 'WEB',
      Name: 'weber',
    },
    {
      Code: 'WEE',
      Name: 'week',
    },
    {
      Code: 'WG',
      Name: 'wine gallon',
    },
    {
      Code: 'WHR',
      Name: 'watt hour',
    },
    {
      Code: 'WM',
      Name: 'working month',
    },
    {
      Code: 'WSD',
      Name: 'standard',
    },
    {
      Code: 'WTT',
      Name: 'watt',
    },
    {
      Code: 'X1',
      Name: "Gunter's chain",
    },
    {
      Code: 'YDK',
      Name: 'square yard',
    },
    {
      Code: 'YDQ',
      Name: 'cubic yard',
    },
    {
      Code: 'YRD',
      Name: 'yard',
    },
    {
      Code: 'Z11',
      Name: 'hanging container',
    },
    {
      Code: 'Z9',
      Name: 'nanomole',
    },
    {
      Code: 'ZP',
      Name: 'page',
    },
    {
      Code: 'ZZ',
      Name: 'mutually defined',
    },
    {
      Code: 'X1A',
      Name: 'Drum, steel',
    },
    {
      Code: 'X1B',
      Name: 'Drum, aluminium',
    },
    {
      Code: 'X1D',
      Name: 'Drum, plywood',
    },
    {
      Code: 'X1F',
      Name: 'Container, flexible',
    },
    {
      Code: 'X1G',
      Name: 'Drum, fibre',
    },
    {
      Code: 'X1W',
      Name: 'Drum, wooden',
    },
    {
      Code: 'X2C',
      Name: 'Barrel, wooden',
    },
    {
      Code: 'X3A',
      Name: 'Jerrican, steel',
    },
    {
      Code: 'X3H',
      Name: 'Jerrican, plastic',
    },
    {
      Code: 'X43',
      Name: 'Bag, super bulk',
    },
    {
      Code: 'X44',
      Name: 'Bag, polybag',
    },
    {
      Code: 'X4A',
      Name: 'Box, steel',
    },
    {
      Code: 'X4B',
      Name: 'Box, aluminium',
    },
    {
      Code: 'X4C',
      Name: 'Box, natural wood',
    },
    {
      Code: 'X4D',
      Name: 'Box, plywood',
    },
    {
      Code: 'X4F',
      Name: 'Box, reconstituted wood',
    },
    {
      Code: 'X4G',
      Name: 'Box, fibreboard',
    },
    {
      Code: 'X4H',
      Name: 'Box, plastic',
    },
    {
      Code: 'X5H',
      Name: 'Bag, woven plastic',
    },
    {
      Code: 'X5L',
      Name: 'Bag, textile',
    },
    {
      Code: 'X5M',
      Name: 'Bag, paper',
    },
    {
      Code: 'X6H',
      Name: 'Composite packaging, plastic receptacle',
    },
    {
      Code: 'X6P',
      Name: 'Composite packaging, glass receptacle',
    },
    {
      Code: 'X7A',
      Name: 'Case, car',
    },
    {
      Code: 'X7B',
      Name: 'Case, wooden',
    },
    {
      Code: 'X8A',
      Name: 'Pallet, wooden',
    },
    {
      Code: 'X8B',
      Name: 'Crate, wooden',
    },
    {
      Code: 'X8C',
      Name: 'Bundle, wooden',
    },
    {
      Code: 'XAA',
      Name: 'Intermediate bulk container, rigid plastic',
    },
    {
      Code: 'XAB',
      Name: 'Receptacle, fibre',
    },
    {
      Code: 'XAC',
      Name: 'Receptacle, paper',
    },
    {
      Code: 'XAD',
      Name: 'Receptacle, wooden',
    },
    {
      Code: 'XAE',
      Name: 'Aerosol',
    },
    {
      Code: 'XAF',
      Name: 'Pallet, modular, collars 80cms * 60cms',
    },
    {
      Code: 'XAG',
      Name: 'Pallet, shrinkwrapped',
    },
    {
      Code: 'XAH',
      Name: 'Pallet, 100cms * 110cms',
    },
    {
      Code: 'XAI',
      Name: 'Clamshell',
    },
    {
      Code: 'XAJ',
      Name: 'Cone',
    },
    {
      Code: 'XAL',
      Name: 'Ball',
    },
    {
      Code: 'XAM',
      Name: 'Ampoule, non-protected',
    },
    {
      Code: 'XAP',
      Name: 'Ampoule, protected',
    },
    {
      Code: 'XAT',
      Name: 'Atomizer',
    },
    {
      Code: 'XAV',
      Name: 'Capsule',
    },
    {
      Code: 'XB4',
      Name: 'Belt',
    },
    {
      Code: 'XBA',
      Name: 'Barrel',
    },
    {
      Code: 'XBB',
      Name: 'Bobbin',
    },
    {
      Code: 'XBC',
      Name: 'Bottlecrate \/ bottlerack',
    },
    {
      Code: 'XBD',
      Name: 'Board',
    },
    {
      Code: 'XBE',
      Name: 'Bundle',
    },
    {
      Code: 'XBF',
      Name: 'Balloon, non-protected',
    },
    {
      Code: 'XBG',
      Name: 'Bag',
    },
    {
      Code: 'XBH',
      Name: 'Bunch',
    },
    {
      Code: 'XBI',
      Name: 'Bin',
    },
    {
      Code: 'XBJ',
      Name: 'Bucket',
    },
    {
      Code: 'XBK',
      Name: 'Basket',
    },
    {
      Code: 'XBL',
      Name: 'Bale, compressed',
    },
    {
      Code: 'XBM',
      Name: 'Basin',
    },
    {
      Code: 'XBN',
      Name: 'Bale, non-compressed',
    },
    {
      Code: 'XBO',
      Name: 'Bottle, non-protected, cylindrical',
    },
    {
      Code: 'XBP',
      Name: 'Balloon, protected',
    },
    {
      Code: 'XBQ',
      Name: 'Bottle, protected cylindrical',
    },
    {
      Code: 'XBR',
      Name: 'Bar',
    },
    {
      Code: 'XBS',
      Name: 'Bottle, non-protected, bulbous',
    },
    {
      Code: 'XBT',
      Name: 'Bolt',
    },
    {
      Code: 'XBU',
      Name: 'Butt',
    },
    {
      Code: 'XBV',
      Name: 'Bottle, protected bulbous',
    },
    {
      Code: 'XBW',
      Name: 'Box, for liquids',
    },
    {
      Code: 'XBX',
      Name: 'Box',
    },
    {
      Code: 'XBY',
      Name: 'Board, in bundle\/bunch\/truss',
    },
    {
      Code: 'XBZ',
      Name: 'Bars, in bundle\/bunch\/truss',
    },
    {
      Code: 'XCA',
      Name: 'Can, rectangular',
    },
    {
      Code: 'XCB',
      Name: 'Crate, beer',
    },
    {
      Code: 'XCC',
      Name: 'Churn',
    },
    {
      Code: 'XCD',
      Name: 'Can, with handle and spout',
    },
    {
      Code: 'XCE',
      Name: 'Creel',
    },
    {
      Code: 'XCF',
      Name: 'Coffer',
    },
    {
      Code: 'XCG',
      Name: 'Cage',
    },
    {
      Code: 'XCH',
      Name: 'Chest',
    },
    {
      Code: 'XCI',
      Name: 'Canister',
    },
    {
      Code: 'XCJ',
      Name: 'Coffin',
    },
    {
      Code: 'XCK',
      Name: 'Cask',
    },
    {
      Code: 'XCL',
      Name: 'Coil',
    },
    {
      Code: 'XCM',
      Name: 'Card',
    },
    {
      Code: 'XCN',
      Name: 'Container, not otherwise specified as transport equipment',
    },
    {
      Code: 'XCO',
      Name: 'Carboy, non-protected',
    },
    {
      Code: 'XCP',
      Name: 'Carboy, protected',
    },
    {
      Code: 'XCQ',
      Name: 'Cartridge',
    },
    {
      Code: 'XCR',
      Name: 'Crate',
    },
    {
      Code: 'XCS',
      Name: 'Case',
    },
    {
      Code: 'XCT',
      Name: 'Carton',
    },
    {
      Code: 'XCU',
      Name: 'Cup',
    },
    {
      Code: 'XCV',
      Name: 'Cover',
    },
    {
      Code: 'XCW',
      Name: 'Cage, roll',
    },
    {
      Code: 'XCX',
      Name: 'Can, cylindrical',
    },
    {
      Code: 'XCY',
      Name: 'Cylinder',
    },
    {
      Code: 'XCZ',
      Name: 'Canvas',
    },
    {
      Code: 'XDA',
      Name: 'Crate, multiple layer, plastic',
    },
    {
      Code: 'XDB',
      Name: 'Crate, multiple layer, wooden',
    },
    {
      Code: 'XDC',
      Name: 'Crate, multiple layer, cardboard',
    },
    {
      Code: 'XDG',
      Name: 'Cage, Commonwealth Handling Equipment Pool (CHEP)',
    },
    {
      Code: 'XDH',
      Name: 'Box, Commonwealth Handling Equipment Pool (CHEP), Eurobox',
    },
    {
      Code: 'XDI',
      Name: 'Drum, iron',
    },
    {
      Code: 'XDJ',
      Name: 'Demijohn, non-protected',
    },
    {
      Code: 'XDK',
      Name: 'Crate, bulk, cardboard',
    },
    {
      Code: 'XDL',
      Name: 'Crate, bulk, plastic',
    },
    {
      Code: 'XDM',
      Name: 'Crate, bulk, wooden',
    },
    {
      Code: 'XDN',
      Name: 'Dispenser',
    },
    {
      Code: 'XDP',
      Name: 'Demijohn, protected',
    },
    {
      Code: 'XDR',
      Name: 'Drum',
    },
    {
      Code: 'XDS',
      Name: 'Tray, one layer no cover, plastic',
    },
    {
      Code: 'XDT',
      Name: 'Tray, one layer no cover, wooden',
    },
    {
      Code: 'XDU',
      Name: 'Tray, one layer no cover, polystyrene',
    },
    {
      Code: 'XDV',
      Name: 'Tray, one layer no cover, cardboard',
    },
    {
      Code: 'XDW',
      Name: 'Tray, two layers no cover, plastic tray',
    },
    {
      Code: 'XDX',
      Name: 'Tray, two layers no cover, wooden',
    },
    {
      Code: 'XDY',
      Name: 'Tray, two layers no cover, cardboard',
    },
    {
      Code: 'XEC',
      Name: 'Bag, plastic',
    },
    {
      Code: 'XED',
      Name: 'Case, with pallet base',
    },
    {
      Code: 'XEE',
      Name: 'Case, with pallet base, wooden',
    },
    {
      Code: 'XEF',
      Name: 'Case, with pallet base, cardboard',
    },
    {
      Code: 'XEG',
      Name: 'Case, with pallet base, plastic',
    },
    {
      Code: 'XEH',
      Name: 'Case, with pallet base, metal',
    },
    {
      Code: 'XEI',
      Name: 'Case, isothermic',
    },
    {
      Code: 'XEN',
      Name: 'Envelope',
    },
    {
      Code: 'XFB',
      Name: 'Flexibag',
    },
    {
      Code: 'XFC',
      Name: 'Crate, fruit',
    },
    {
      Code: 'XFD',
      Name: 'Crate, framed',
    },
    {
      Code: 'XFE',
      Name: 'Flexitank',
    },
    {
      Code: 'XFI',
      Name: 'Firkin',
    },
    {
      Code: 'XFL',
      Name: 'Flask',
    },
    {
      Code: 'XFO',
      Name: 'Footlocker',
    },
    {
      Code: 'XFP',
      Name: 'Filmpack',
    },
    {
      Code: 'XFR',
      Name: 'Frame',
    },
    {
      Code: 'XFT',
      Name: 'Foodtainer',
    },
    {
      Code: 'XFW',
      Name: 'Cart, flatbed',
    },
    {
      Code: 'XFX',
      Name: 'Bag, flexible container',
    },
    {
      Code: 'XGB',
      Name: 'Bottle, gas',
    },
    {
      Code: 'XGI',
      Name: 'Girder',
    },
    {
      Code: 'XGL',
      Name: 'Container, gallon',
    },
    {
      Code: 'XGR',
      Name: 'Receptacle, glass',
    },
    {
      Code: 'XGU',
      Name: 'Tray, containing horizontally stacked flat items',
    },
    {
      Code: 'XGY',
      Name: 'Bag, gunny',
    },
    {
      Code: 'XGZ',
      Name: 'Girders, in bundle\/bunch\/truss',
    },
    {
      Code: 'XHA',
      Name: 'Basket, with handle, plastic',
    },
    {
      Code: 'XHB',
      Name: 'Basket, with handle, wooden',
    },
    {
      Code: 'XHC',
      Name: 'Basket, with handle, cardboard',
    },
    {
      Code: 'XHG',
      Name: 'Hogshead',
    },
    {
      Code: 'XHN',
      Name: 'Hanger',
    },
    {
      Code: 'XHR',
      Name: 'Hamper',
    },
    {
      Code: 'XIA',
      Name: 'Package, display, wooden',
    },
    {
      Code: 'XIB',
      Name: 'Package, display, cardboard',
    },
    {
      Code: 'XIC',
      Name: 'Package, display, plastic',
    },
    {
      Code: 'XID',
      Name: 'Package, display, metal',
    },
    {
      Code: 'XIE',
      Name: 'Package, show',
    },
    {
      Code: 'XIF',
      Name: 'Package, flow',
    },
    {
      Code: 'XIG',
      Name: 'Package, paper wrapped',
    },
    {
      Code: 'XIH',
      Name: 'Drum, plastic',
    },
    {
      Code: 'XIK',
      Name: 'Package, cardboard, with bottle grip-holes',
    },
    {
      Code: 'XIL',
      Name: 'Tray, rigid, lidded stackable (CEN TS 14482:2002)',
    },
    {
      Code: 'XIN',
      Name: 'Ingot',
    },
    {
      Code: 'XIZ',
      Name: 'Ingots, in bundle\/bunch\/truss',
    },
    {
      Code: 'XJB',
      Name: 'Bag, jumbo',
    },
    {
      Code: 'XJC',
      Name: 'Jerrican, rectangular',
    },
    {
      Code: 'XJG',
      Name: 'Jug',
    },
    {
      Code: 'XJR',
      Name: 'Jar',
    },
    {
      Code: 'XJT',
      Name: 'Jutebag',
    },
    {
      Code: 'XJY',
      Name: 'Jerrican, cylindrical',
    },
    {
      Code: 'XKG',
      Name: 'Keg',
    },
    {
      Code: 'XKI',
      Name: 'Kit',
    },
    {
      Code: 'XLE',
      Name: 'Luggage',
    },
    {
      Code: 'XLG',
      Name: 'Log',
    },
    {
      Code: 'XLT',
      Name: 'Lot',
    },
    {
      Code: 'XLU',
      Name: 'Lug',
    },
    {
      Code: 'XLV',
      Name: 'Liftvan',
    },
    {
      Code: 'XLZ',
      Name: 'Logs, in bundle\/bunch\/truss',
    },
    {
      Code: 'XMA',
      Name: 'Crate, metal',
    },
    {
      Code: 'XMB',
      Name: 'Bag, multiply',
    },
    {
      Code: 'XMC',
      Name: 'Crate, milk',
    },
    {
      Code: 'XME',
      Name: 'Container, metal',
    },
    {
      Code: 'XMR',
      Name: 'Receptacle, metal',
    },
    {
      Code: 'XMS',
      Name: 'Sack, multi-wall',
    },
    {
      Code: 'XMT',
      Name: 'Mat',
    },
    {
      Code: 'XMW',
      Name: 'Receptacle, plastic wrapped',
    },
    {
      Code: 'XMX',
      Name: 'Matchbox',
    },
    {
      Code: 'XNA',
      Name: 'Not available',
    },
    {
      Code: 'XNE',
      Name: 'Unpacked or unpackaged',
    },
    {
      Code: 'XNF',
      Name: 'Unpacked or unpackaged, single unit',
    },
    {
      Code: 'XNG',
      Name: 'Unpacked or unpackaged, multiple units',
    },
    {
      Code: 'XNS',
      Name: 'Nest',
    },
    {
      Code: 'XNT',
      Name: 'Net',
    },
    {
      Code: 'XNU',
      Name: 'Net, tube, plastic',
    },
    {
      Code: 'XNV',
      Name: 'Net, tube, textile',
    },
    {
      Code: 'XO1',
      Name: 'Two sided cage on wheels with fixing strap',
    },
    {
      Code: 'XO2',
      Name: 'Trolley',
    },
    {
      Code: 'XO3',
      Name: 'Oneway pallet ISO 0 - 1\/2 EURO Pallet',
    },
    {
      Code: 'XO4',
      Name: 'Oneway pallet ISO 1 - 1\/1 EURO Pallet',
    },
    {
      Code: 'XO5',
      Name: 'Oneway pallet ISO 2 - 2\/1 EURO Pallet',
    },
    {
      Code: 'XO6',
      Name: 'Pallet with exceptional dimensions',
    },
    {
      Code: 'XO7',
      Name: 'Wooden pallet  40 cm x 80 cm',
    },
    {
      Code: 'XO8',
      Name: 'Plastic pallet SRS 60 cm x 80 cm',
    },
    {
      Code: 'XO9',
      Name: 'Plastic pallet SRS 80 cm x 120 cm',
    },
    {
      Code: 'XOA',
      Name: 'Pallet, CHEP 40 cm x 60 cm',
    },
    {
      Code: 'XOB',
      Name: 'Pallet, CHEP 80 cm x 120 cm',
    },
    {
      Code: 'XOC',
      Name: 'Pallet, CHEP 100 cm x 120 cm',
    },
    {
      Code: 'XOD',
      Name: 'Pallet, AS 4068-1993',
    },
    {
      Code: 'XOE',
      Name: 'Pallet, ISO T11',
    },
    {
      Code: 'XOF',
      Name: 'Platform, unspecified weight or dimension',
    },
    {
      Code: 'XOG',
      Name: 'Pallet ISO 0 - 1\/2 EURO Pallet',
    },
    {
      Code: 'XOH',
      Name: 'Pallet ISO 1 - 1\/1 EURO Pallet',
    },
    {
      Code: 'XOI',
      Name: 'Pallet ISO 2 â€“ 2\/1 EURO Pallet',
    },
    {
      Code: 'XOJ',
      Name: '1\/4 EURO Pallet',
    },
    {
      Code: 'XOK',
      Name: 'Block',
    },
    {
      Code: 'XOL',
      Name: '1\/8 EURO Pallet',
    },
    {
      Code: 'XOM',
      Name: 'Synthetic pallet ISO 1',
    },
    {
      Code: 'XON',
      Name: 'Synthetic pallet ISO 2',
    },
    {
      Code: 'XOP',
      Name: 'Wholesaler pallet',
    },
    {
      Code: 'XOQ',
      Name: 'Pallet 80 X 100 cm',
    },
    {
      Code: 'XOR',
      Name: 'Pallet 60 X 100 cm',
    },
    {
      Code: 'XOS',
      Name: 'Oneway pallet',
    },
    {
      Code: 'XOT',
      Name: 'Octabin',
    },
    {
      Code: 'XOU',
      Name: 'Container, outer',
    },
    {
      Code: 'XOV',
      Name: 'Returnable pallet',
    },
    {
      Code: 'XOW',
      Name: 'Large bag, pallet sized',
    },
    {
      Code: 'XOX',
      Name: 'A wheeled pallet with raised rim (81 x 67 x 135)',
    },
    {
      Code: 'XOY',
      Name: 'A Wheeled pallet with raised rim (81 x 72 x 135)',
    },
    {
      Code: 'XOZ',
      Name: 'Wheeled pallet with raised rim ( 81 x 60 x 16)',
    },
    {
      Code: 'XP1',
      Name: 'CHEP pallet 60 cm x 80 cm',
    },
    {
      Code: 'XP2',
      Name: 'Pan',
    },
    {
      Code: 'XP3',
      Name: 'LPR pallet 60 cm x 80 cm',
    },
    {
      Code: 'XP4',
      Name: 'LPR pallet 80 cm x 120 cm',
    },
    {
      Code: 'XPA',
      Name: 'Packet',
    },
    {
      Code: 'XPB',
      Name: 'Pallet, box Combined open-ended box and pallet',
    },
    {
      Code: 'XPC',
      Name: 'Parcel',
    },
    {
      Code: 'XPD',
      Name: 'Pallet, modular, collars 80cms * 100cms',
    },
    {
      Code: 'XPE',
      Name: 'Pallet, modular, collars 80cms * 120cms',
    },
    {
      Code: 'XPF',
      Name: 'Pen',
    },
    {
      Code: 'XPG',
      Name: 'Plate',
    },
    {
      Code: 'XPH',
      Name: 'Pitcher',
    },
    {
      Code: 'XPI',
      Name: 'Pipe',
    },
    {
      Code: 'XPJ',
      Name: 'Punnet',
    },
    {
      Code: 'XPK',
      Name: 'Package',
    },
    {
      Code: 'XPL',
      Name: 'Pail',
    },
    {
      Code: 'XPN',
      Name: 'Plank',
    },
    {
      Code: 'XPO',
      Name: 'Pouch',
    },
    {
      Code: 'XPP',
      Name: 'Piece',
    },
    {
      Code: 'XPR',
      Name: 'Receptacle, plastic',
    },
    {
      Code: 'XPT',
      Name: 'Pot',
    },
    {
      Code: 'XPU',
      Name: 'Tray',
    },
    {
      Code: 'XPV',
      Name: 'Pipes, in bundle\/bunch\/truss',
    },
    {
      Code: 'XPX',
      Name: 'Pallet',
    },
    {
      Code: 'XPY',
      Name: 'Plates, in bundle\/bunch\/truss',
    },
    {
      Code: 'XPZ',
      Name: 'Planks, in bundle\/bunch\/truss',
    },
    {
      Code: 'XQA',
      Name: 'Drum, steel, non-removable head',
    },
    {
      Code: 'XQB',
      Name: 'Drum, steel, removable head',
    },
    {
      Code: 'XQC',
      Name: 'Drum, aluminium, non-removable head',
    },
    {
      Code: 'XQD',
      Name: 'Drum, aluminium, removable head',
    },
    {
      Code: 'XQF',
      Name: 'Drum, plastic, non-removable head',
    },
    {
      Code: 'XQG',
      Name: 'Drum, plastic, removable head',
    },
    {
      Code: 'XQH',
      Name: 'Barrel, wooden, bung type',
    },
    {
      Code: 'XQJ',
      Name: 'Barrel, wooden, removable head',
    },
    {
      Code: 'XQK',
      Name: 'Jerrican, steel, non-removable head',
    },
    {
      Code: 'XQL',
      Name: 'Jerrican, steel, removable head',
    },
    {
      Code: 'XQM',
      Name: 'Jerrican, plastic, non-removable head',
    },
    {
      Code: 'XQN',
      Name: 'Jerrican, plastic, removable head',
    },
    {
      Code: 'XQP',
      Name: 'Box, wooden, natural wood, ordinary',
    },
    {
      Code: 'XQQ',
      Name: 'Box, wooden, natural wood, with sift proof walls',
    },
    {
      Code: 'XQR',
      Name: 'Box, plastic, expanded',
    },
    {
      Code: 'XQS',
      Name: 'Box, plastic, solid',
    },
    {
      Code: 'XRD',
      Name: 'Rod',
    },
    {
      Code: 'XRG',
      Name: 'Ring',
    },
    {
      Code: 'XRJ',
      Name: 'Rack, clothing hanger',
    },
    {
      Code: 'XRK',
      Name: 'Rack',
    },
    {
      Code: 'XRL',
      Name: 'Reel',
    },
    {
      Code: 'XRO',
      Name: 'Roll',
    },
    {
      Code: 'XRT',
      Name: 'Rednet',
    },
    {
      Code: 'XRZ',
      Name: 'Rods, in bundle\/bunch\/truss',
    },
    {
      Code: 'XSA',
      Name: 'Sack',
    },
    {
      Code: 'XSB',
      Name: 'Slab',
    },
    {
      Code: 'XSC',
      Name: 'Crate, shallow',
    },
    {
      Code: 'XSD',
      Name: 'Spindle',
    },
    {
      Code: 'XSE',
      Name: 'Sea-chest',
    },
    {
      Code: 'XSH',
      Name: 'Sachet',
    },
    {
      Code: 'XSI',
      Name: 'Skid',
    },
    {
      Code: 'XSK',
      Name: 'Case, skeleton',
    },
    {
      Code: 'XSL',
      Name: 'Slipsheet',
    },
    {
      Code: 'XSM',
      Name: 'Sheetmetal',
    },
    {
      Code: 'XSO',
      Name: 'Spool',
    },
    {
      Code: 'XSP',
      Name: 'Sheet, plastic wrapping',
    },
    {
      Code: 'XSS',
      Name: 'Case, steel',
    },
    {
      Code: 'XST',
      Name: 'Sheet',
    },
    {
      Code: 'XSU',
      Name: 'Suitcase',
    },
    {
      Code: 'XSV',
      Name: 'Envelope, steel',
    },
    {
      Code: 'XSW',
      Name: 'Shrinkwrapped',
    },
    {
      Code: 'XSX',
      Name: 'Set',
    },
    {
      Code: 'XSY',
      Name: 'Sleeve',
    },
    {
      Code: 'XSZ',
      Name: 'Sheets, in bundle\/bunch\/truss',
    },
    {
      Code: 'XT1',
      Name: 'Tablet',
    },
    {
      Code: 'XTB',
      Name: 'Tub',
    },
    {
      Code: 'XTC',
      Name: 'Tea-chest',
    },
    {
      Code: 'XTD',
      Name: 'Tube, collapsible',
    },
    {
      Code: 'XTE',
      Name: 'Tyre',
    },
    {
      Code: 'XTG',
      Name: 'Tank container, generic',
    },
    {
      Code: 'XTI',
      Name: 'Tierce',
    },
    {
      Code: 'XTK',
      Name: 'Tank, rectangular',
    },
    {
      Code: 'XTL',
      Name: 'Tub, with lid',
    },
    {
      Code: 'XTN',
      Name: 'Tin',
    },
    {
      Code: 'XTO',
      Name: 'Tun',
    },
    {
      Code: 'XTR',
      Name: 'Trunk',
    },
    {
      Code: 'XTS',
      Name: 'Truss',
    },
    {
      Code: 'XTT',
      Name: 'Bag, tote',
    },
    {
      Code: 'XTU',
      Name: 'Tube',
    },
    {
      Code: 'XTV',
      Name: 'Tube, with nozzle',
    },
    {
      Code: 'XTW',
      Name: 'Pallet, triwall',
    },
    {
      Code: 'XTY',
      Name: 'Tank, cylindrical',
    },
    {
      Code: 'XTZ',
      Name: 'Tubes, in bundle\/bunch\/truss',
    },
    {
      Code: 'XUC',
      Name: 'Uncaged',
    },
    {
      Code: 'XUN',
      Name: 'Unit',
    },
    {
      Code: 'XVA',
      Name: 'Vat',
    },
    {
      Code: 'XVG',
      Name: 'Bulk, gas (at 1031 mbar and 15Â°C)',
    },
    {
      Code: 'XVI',
      Name: 'Vial',
    },
    {
      Code: 'XVK',
      Name: 'Vanpack',
    },
    {
      Code: 'XVL',
      Name: 'Bulk, liquid',
    },
    {
      Code: 'XVN',
      Name: 'Vehicle',
    },
    {
      Code: 'XVO',
      Name: 'Bulk, solid, large particles (â€œnodulesâ€)',
    },
    {
      Code: 'XVP',
      Name: 'Vacuum-packed',
    },
    {
      Code: 'XVQ',
      Name: 'Bulk, liquefied gas (at abnormal temperature\/pressure)',
    },
    {
      Code: 'XVR',
      Name: 'Bulk, solid, granular particles (â€œgrainsâ€)',
    },
    {
      Code: 'XVS',
      Name: 'Bulk, scrap metal',
    },
    {
      Code: 'XVY',
      Name: 'Bulk, solid, fine particles (â€œpowdersâ€)',
    },
    {
      Code: 'XWA',
      Name: 'Intermediate bulk container',
    },
    {
      Code: 'XWB',
      Name: 'Wickerbottle',
    },
    {
      Code: 'XWC',
      Name: 'Intermediate bulk container, steel',
    },
    {
      Code: 'XWD',
      Name: 'Intermediate bulk container, aluminium',
    },
    {
      Code: 'XWF',
      Name: 'Intermediate bulk container, metal',
    },
    {
      Code: 'XWG',
      Name: 'Intermediate bulk container, steel, pressurised > 10 kpa',
    },
    {
      Code: 'XWH',
      Name: 'Intermediate bulk container, aluminium, pressurised > 10 kpa',
    },
    {
      Code: 'XWJ',
      Name: 'Intermediate bulk container, metal, pressure 10 kpa',
    },
    {
      Code: 'XWK',
      Name: 'Intermediate bulk container, steel, liquid',
    },
    {
      Code: 'XWL',
      Name: 'Intermediate bulk container, aluminium, liquid',
    },
    {
      Code: 'XWM',
      Name: 'Intermediate bulk container, metal, liquid',
    },
    {
      Code: 'XWN',
      Name: 'Intermediate bulk container, woven plastic, without coat\/liner',
    },
    {
      Code: 'XWP',
      Name: 'Intermediate bulk container, woven plastic, coated',
    },
    {
      Code: 'XWQ',
      Name: 'Intermediate bulk container, woven plastic, with liner',
    },
    {
      Code: 'XWR',
      Name: 'Intermediate bulk container, woven plastic, coated and liner',
    },
    {
      Code: 'XWS',
      Name: 'Intermediate bulk container, plastic film',
    },
    {
      Code: 'XWT',
      Name: 'Intermediate bulk container, textile with out coat\/liner',
    },
    {
      Code: 'XWU',
      Name: 'Intermediate bulk container, natural wood, with inner liner',
    },
    {
      Code: 'XWV',
      Name: 'Intermediate bulk container, textile, coated',
    },
    {
      Code: 'XWW',
      Name: 'Intermediate bulk container, textile, with liner',
    },
    {
      Code: 'XWX',
      Name: 'Intermediate bulk container, textile, coated and liner',
    },
    {
      Code: 'XWY',
      Name: 'Intermediate bulk container, plywood, with inner liner',
    },
    {
      Code: 'XWZ',
      Name: 'Intermediate bulk container, reconstituted wood, with inner liner',
    },
    {
      Code: 'XXA',
      Name: 'Bag, woven plastic, without inner coat\/liner',
    },
    {
      Code: 'XXB',
      Name: 'Bag, woven plastic, sift proof',
    },
    {
      Code: 'XXC',
      Name: 'Bag, woven plastic, water resistant',
    },
    {
      Code: 'XXD',
      Name: 'Bag, plastics film',
    },
    {
      Code: 'XXF',
      Name: 'Bag, textile, without inner coat\/liner',
    },
    {
      Code: 'XXG',
      Name: 'Bag, textile, sift proof',
    },
    {
      Code: 'XXH',
      Name: 'Bag, textile, water resistant',
    },
    {
      Code: 'XXJ',
      Name: 'Bag, paper, multi-wall',
    },
    {
      Code: 'XXK',
      Name: 'Bag, paper, multi-wall, water resistant',
    },
    {
      Code: 'XYA',
      Name: 'Composite packaging, plastic receptacle in steel drum',
    },
    {
      Code: 'XYB',
      Name: 'Composite packaging, plastic receptacle in steel crate box',
    },
    {
      Code: 'XYC',
      Name: 'Composite packaging, plastic receptacle in aluminium drum',
    },
    {
      Code: 'XYD',
      Name: 'Composite packaging, plastic receptacle in aluminium crate',
    },
    {
      Code: 'XYF',
      Name: 'Composite packaging, plastic receptacle in wooden box',
    },
    {
      Code: 'XYG',
      Name: 'Composite packaging, plastic receptacle in plywood drum',
    },
    {
      Code: 'XYH',
      Name: 'Composite packaging, plastic receptacle in plywood box',
    },
    {
      Code: 'XYJ',
      Name: 'Composite packaging, plastic receptacle in fibre drum',
    },
    {
      Code: 'XYK',
      Name: 'Composite packaging, plastic receptacle in fibreboard box',
    },
    {
      Code: 'XYL',
      Name: 'Composite packaging, plastic receptacle in plastic drum',
    },
    {
      Code: 'XYM',
      Name: 'Composite packaging, plastic receptacle in solid plastic box',
    },
    {
      Code: 'XYN',
      Name: 'Composite packaging, glass receptacle in steel drum',
    },
    {
      Code: 'XYP',
      Name: 'Composite packaging, glass receptacle in steel crate box',
    },
    {
      Code: 'XYQ',
      Name: 'Composite packaging, glass receptacle in aluminium drum',
    },
    {
      Code: 'XYR',
      Name: 'Composite packaging, glass receptacle in aluminium crate',
    },
    {
      Code: 'XYS',
      Name: 'Composite packaging, glass receptacle in wooden box',
    },
    {
      Code: 'XYT',
      Name: 'Composite packaging, glass receptacle in plywood drum',
    },
    {
      Code: 'XYV',
      Name: 'Composite packaging, glass receptacle in wickerwork hamper',
    },
    {
      Code: 'XYW',
      Name: 'Composite packaging, glass receptacle in fibre drum',
    },
    {
      Code: 'XYX',
      Name: 'Composite packaging, glass receptacle in fibreboard box',
    },
    {
      Code: 'XYY',
      Name: 'Composite packaging, glass receptacle in expandable plastic pack',
    },
    {
      Code: 'XYZ',
      Name: 'Composite packaging, glass receptacle in solid plastic pack',
    },
    {
      Code: 'XZA',
      Name: 'Intermediate bulk container, paper, multi-wall',
    },
    {
      Code: 'XZB',
      Name: 'Bag, large',
    },
    {
      Code: 'XZC',
      Name: 'Intermediate bulk container, paper, multi-wall, water resistant',
    },
    {
      Code: 'XZD',
      Name: 'Intermediate bulk container, rigid plastic, with structural equipment, solids',
    },
    {
      Code: 'XZF',
      Name: 'Intermediate bulk container, rigid plastic, freestanding, solids',
    },
    {
      Code: 'XZG',
      Name: 'Intermediate bulk container, rigid plastic, with structural equipment,\n\t\t\t\tpressurised',
    },
    {
      Code: 'XZH',
      Name: 'Intermediate bulk container, rigid plastic, freestanding, pressurised',
    },
    {
      Code: 'XZJ',
      Name: 'Intermediate bulk container, rigid plastic, with structural equipment, liquids',
    },
    {
      Code: 'XZK',
      Name: 'Intermediate bulk container, rigid plastic, freestanding, liquids',
    },
    {
      Code: 'XZL',
      Name: 'Intermediate bulk container, composite, rigid plastic, solids',
    },
    {
      Code: 'XZM',
      Name: 'Intermediate bulk container, composite, flexible plastic, solids',
    },
    {
      Code: 'XZN',
      Name: 'Intermediate bulk container, composite, rigid plastic, pressurised',
    },
    {
      Code: 'XZP',
      Name: 'Intermediate bulk container, composite, flexible plastic, pressurised',
    },
    {
      Code: 'XZQ',
      Name: 'Intermediate bulk container, composite, rigid plastic, liquids',
    },
    {
      Code: 'XZR',
      Name: 'Intermediate bulk container, composite, flexible plastic, liquids',
    },
    {
      Code: 'XZS',
      Name: 'Intermediate bulk container, composite',
    },
    {
      Code: 'XZT',
      Name: 'Intermediate bulk container, fibreboard',
    },
    {
      Code: 'XZU',
      Name: 'Intermediate bulk container, flexible',
    },
    {
      Code: 'XZV',
      Name: 'Intermediate bulk container, metal, other than steel',
    },
    {
      Code: 'XZW',
      Name: 'Intermediate bulk container, natural wood',
    },
    {
      Code: 'XZX',
      Name: 'Intermediate bulk container, plywood',
    },
    {
      Code: 'XZY',
      Name: 'Intermediate bulk container, reconstituted wood',
    },
    {
      Code: 'XZZ',
      Name: 'Mutually defined',
    },
  ]

  /**
   * Returns all unit types
   */
  public getAllTypes(): UnitType[] {
    return [...this.unitTypes]
  }

  /**
   * Returns all code
   */
  public getAllCodes(): string[] {
    return [...this.unitTypes.map((unit) => unit.Code)]
  }

  /**
   * Returns a unit type by its code
   * @param code The code to search for
   */
  public getByCode(code: string): UnitType | undefined {
    return this.unitTypes.find((unit) => unit.Code.toUpperCase() === code.toUpperCase())
  }

  /**
   * Returns a unit type by its name
   * @param name The unit name to search for
   */
  public getByName(name: string): UnitType | undefined {
    const normalizedName = name.toLowerCase()
    return this.unitTypes.find((unit) => unit.Name.toLowerCase() === normalizedName)
  }

  /**
   * Filters unit types based on a search term
   * @param searchTerm The term to search for in code or name
   * @param caseSensitive Whether the search should be case sensitive
   */
  public filterTypes(searchTerm: string, caseSensitive: boolean = false): UnitType[] {
    if (!searchTerm) {
      return this.getAllTypes()
    }

    return this.unitTypes.filter((unit) => {
      if (!caseSensitive) {
        const term = searchTerm.toLowerCase()
        return unit.Code.toLowerCase().includes(term) || unit.Name.toLowerCase().includes(term)
      }
      return unit.Code.includes(searchTerm) || unit.Name.includes(searchTerm)
    })
  }

  /**
   * Returns all measurement units (like metres, kilograms, etc.)
   */
  public getMeasurementUnits(): UnitType[] {
    return this.unitTypes.filter((unit) => this.isMeasurementUnit(unit.Name))
  }

  /**
   * Returns all packaging units (like boxes, containers, etc.)
   */
  public getPackagingUnits(): UnitType[] {
    return this.unitTypes.filter((unit) => this.isPackagingUnit(unit.Name))
  }

  /**
   * Returns all counting units (like pieces, pairs, etc.)
   */
  public getCountingUnits(): UnitType[] {
    return this.unitTypes.filter((unit) => this.isCountingUnit(unit.Name))
  }

  /**
   * Returns all time-based units (like hours, days, etc.)
   */
  public getTimeUnits(): UnitType[] {
    return this.unitTypes.filter((unit) => this.isTimeUnit(unit.Name))
  }

  /**
   * Checks if a unit type is valid
   * @param code The code to validate
   */
  public isValidCode(code: string): boolean {
    return this.unitTypes.some((unit) => unit.Code.toUpperCase() === code.toUpperCase())
  }

  /**
   * Groups unit types by common prefixes (like milli-, kilo-, etc.)
   */
  public groupByPrefix(): { [key: string]: UnitType[] } {
    const prefixes = ['milli', 'kilo', 'micro', 'mega', 'nano']
    const groups: { [key: string]: UnitType[] } = {}

    for (const prefix of prefixes) {
      groups[prefix] = this.unitTypes.filter((unit) => unit.Name.toLowerCase().startsWith(prefix))
    }

    return groups
  }

  private isMeasurementUnit(name: string): boolean {
    const measurementKeywords = [
      'metre',
      'meter',
      'gram',
      'litre',
      'liter',
      'pascal',
      'volt',
      'watt',
      'ampere',
      'kelvin',
      'joule',
    ]
    return measurementKeywords.some((keyword) => name.toLowerCase().includes(keyword))
  }

  private isPackagingUnit(name: string): boolean {
    const packagingKeywords = [
      'box',
      'container',
      'package',
      'pallet',
      'bag',
      'barrel',
      'crate',
      'drum',
      'case',
    ]
    return packagingKeywords.some((keyword) => name.toLowerCase().includes(keyword))
  }

  private isCountingUnit(name: string): boolean {
    const countingKeywords = ['piece', 'pair', 'unit', 'dozen', 'score', 'gross']
    return countingKeywords.some((keyword) => name.toLowerCase().includes(keyword))
  }

  private isTimeUnit(name: string): boolean {
    const timeKeywords = ['second', 'minute', 'hour', 'day', 'week', 'month', 'year']
    return timeKeywords.some((keyword) => name.toLowerCase().includes(keyword))
  }
}

export class CurrencyCodesService {
  //.... Complete this class service
  private readonly currencyCodes: CurrencyCode[] = [
    {
      Code: 'AED',
      Currency: 'UAE Dirham',
    },
    {
      Code: 'AFN',
      Currency: 'Afghani',
    },
    {
      Code: 'ALL',
      Currency: 'Lek',
    },
    {
      Code: 'AMD',
      Currency: 'Armenian Dram',
    },
    {
      Code: 'ANG',
      Currency: 'Netherlands Antillean Guilder',
    },
    {
      Code: 'AOA',
      Currency: 'Kwanza',
    },
    {
      Code: 'ARS',
      Currency: 'Argentine Peso',
    },
    {
      Code: 'AUD',
      Currency: 'Australian Dollar',
    },
    {
      Code: 'AWG',
      Currency: 'Aruban Florin',
    },
    {
      Code: 'AZN',
      Currency: 'Azerbaijan Manat',
    },
    {
      Code: 'BAM',
      Currency: 'Convertible Mark',
    },
    {
      Code: 'BBD',
      Currency: 'Barbados Dollar',
    },
    {
      Code: 'BDT',
      Currency: 'Taka',
    },
    {
      Code: 'BGN',
      Currency: 'Bulgarian Lev',
    },
    {
      Code: 'BHD',
      Currency: 'Bahraini Dinar',
    },
    {
      Code: 'BIF',
      Currency: 'Burundi Franc',
    },
    {
      Code: 'BMD',
      Currency: 'Bermudian Dollar',
    },
    {
      Code: 'BND',
      Currency: 'Brunei Dollar',
    },
    {
      Code: 'BOB',
      Currency: 'Boliviano',
    },
    {
      Code: 'BOV',
      Currency: 'Mvdol',
    },
    {
      Code: 'BRL',
      Currency: 'Brazilian Real',
    },
    {
      Code: 'BSD',
      Currency: 'Bahamian Dollar',
    },
    {
      Code: 'BTN',
      Currency: 'Ngultrum',
    },
    {
      Code: 'BWP',
      Currency: 'Pula',
    },
    {
      Code: 'BYN',
      Currency: 'Belarusian Ruble',
    },
    {
      Code: 'BZD',
      Currency: 'Belize Dollar',
    },
    {
      Code: 'CAD',
      Currency: 'Canadian Dollar',
    },
    {
      Code: 'CDF',
      Currency: 'Congolese Franc',
    },
    {
      Code: 'CHE',
      Currency: 'WIR Euro',
    },
    {
      Code: 'CHF',
      Currency: 'Swiss Franc',
    },
    {
      Code: 'CHW',
      Currency: 'WIR Franc',
    },
    {
      Code: 'CLF',
      Currency: 'Unidad de Fomento',
    },
    {
      Code: 'CLP',
      Currency: 'Chilean Peso',
    },
    {
      Code: 'CNY',
      Currency: 'Yuan Renminbi',
    },
    {
      Code: 'COP',
      Currency: 'Colombian Peso',
    },
    {
      Code: 'COU',
      Currency: 'Unidad de Valor Real',
    },
    {
      Code: 'CRC',
      Currency: 'Costa Rican Colon',
    },
    {
      Code: 'CUC',
      Currency: 'Peso Convertible',
    },
    {
      Code: 'CUP',
      Currency: 'Cuban Peso',
    },
    {
      Code: 'CVE',
      Currency: 'Cabo Verde Escudo',
    },
    {
      Code: 'CZK',
      Currency: 'Czech Koruna',
    },
    {
      Code: 'DJF',
      Currency: 'Djibouti Franc',
    },
    {
      Code: 'DKK',
      Currency: 'Danish Krone',
    },
    {
      Code: 'DOP',
      Currency: 'Dominican Peso',
    },
    {
      Code: 'DZD',
      Currency: 'Algerian Dinar',
    },
    {
      Code: 'EGP',
      Currency: 'Egyptian Pound',
    },
    {
      Code: 'ERN',
      Currency: 'Nakfa',
    },
    {
      Code: 'ETB',
      Currency: 'Ethiopian Birr',
    },
    {
      Code: 'EUR',
      Currency: 'Euro',
    },
    {
      Code: 'FJD',
      Currency: 'Fiji Dollar',
    },
    {
      Code: 'FKP',
      Currency: 'Falkland Islands Pound',
    },
    {
      Code: 'GBP',
      Currency: 'Pound Sterling',
    },
    {
      Code: 'GEL',
      Currency: 'Lari',
    },
    {
      Code: 'GHS',
      Currency: 'Ghana Cedi',
    },
    {
      Code: 'GIP',
      Currency: 'Gibraltar Pound',
    },
    {
      Code: 'GMD',
      Currency: 'Dalasi',
    },
    {
      Code: 'GNF',
      Currency: 'Guinean Franc',
    },
    {
      Code: 'GTQ',
      Currency: 'Quetzal',
    },
    {
      Code: 'GYD',
      Currency: 'Guyana Dollar',
    },
    {
      Code: 'HKD',
      Currency: 'Hong Kong Dollar',
    },
    {
      Code: 'HNL',
      Currency: 'Lempira',
    },
    {
      Code: 'HTG',
      Currency: 'Gourde',
    },
    {
      Code: 'HUF',
      Currency: 'Forint',
    },
    {
      Code: 'IDR',
      Currency: 'Rupiah',
    },
    {
      Code: 'ILS',
      Currency: 'New Israeli Sheqel',
    },
    {
      Code: 'INR',
      Currency: 'Indian Rupee',
    },
    {
      Code: 'IQD',
      Currency: 'Iraqi Dinar',
    },
    {
      Code: 'IRR',
      Currency: 'Iranian Rial',
    },
    {
      Code: 'ISK',
      Currency: 'Iceland Krona',
    },
    {
      Code: 'JMD',
      Currency: 'Jamaican Dollar',
    },
    {
      Code: 'JOD',
      Currency: 'Jordanian Dinar',
    },
    {
      Code: 'JPY',
      Currency: 'Yen',
    },
    {
      Code: 'KES',
      Currency: 'Kenyan Shilling',
    },
    {
      Code: 'KGS',
      Currency: 'Som',
    },
    {
      Code: 'KHR',
      Currency: 'Riel',
    },
    {
      Code: 'KMF',
      Currency: 'Comorian Franc ',
    },
    {
      Code: 'KPW',
      Currency: 'North Korean Won',
    },
    {
      Code: 'KRW',
      Currency: 'Won',
    },
    {
      Code: 'KWD',
      Currency: 'Kuwaiti Dinar',
    },
    {
      Code: 'KYD',
      Currency: 'Cayman Islands Dollar',
    },
    {
      Code: 'KZT',
      Currency: 'Tenge',
    },
    {
      Code: 'LAK',
      Currency: 'Lao Kip',
    },
    {
      Code: 'LBP',
      Currency: 'Lebanese Pound',
    },
    {
      Code: 'LKR',
      Currency: 'Sri Lanka Rupee',
    },
    {
      Code: 'LRD',
      Currency: 'Liberian Dollar',
    },
    {
      Code: 'LSL',
      Currency: 'Loti',
    },
    {
      Code: 'LYD',
      Currency: 'Libyan Dinar',
    },
    {
      Code: 'MAD',
      Currency: 'Moroccan Dirham',
    },
    {
      Code: 'MDL',
      Currency: 'Moldovan Leu',
    },
    {
      Code: 'MGA',
      Currency: 'Malagasy Ariary',
    },
    {
      Code: 'MKD',
      Currency: 'Denar',
    },
    {
      Code: 'MMK',
      Currency: 'Kyat',
    },
    {
      Code: 'MNT',
      Currency: 'Tugrik',
    },
    {
      Code: 'MOP',
      Currency: 'Pataca',
    },
    {
      Code: 'MRU',
      Currency: 'Ouguiya',
    },
    {
      Code: 'MUR',
      Currency: 'Mauritius Rupee',
    },
    {
      Code: 'MVR',
      Currency: 'Rufiyaa',
    },
    {
      Code: 'MWK',
      Currency: 'Malawi Kwacha',
    },
    {
      Code: 'MXN',
      Currency: 'Mexican Peso',
    },
    {
      Code: 'MXV',
      Currency: 'Mexican Unidad de Inversion (UDI)',
    },
    {
      Code: 'MYR',
      Currency: 'Malaysian Ringgit',
    },
    {
      Code: 'MZN',
      Currency: 'Mozambique Metical',
    },
    {
      Code: 'NAD',
      Currency: 'Namibia Dollar',
    },
    {
      Code: 'NGN',
      Currency: 'Naira',
    },
    {
      Code: 'NIO',
      Currency: 'Cordoba Oro',
    },
    {
      Code: 'NOK',
      Currency: 'Norwegian Krone',
    },
    {
      Code: 'NPR',
      Currency: 'Nepalese Rupee',
    },
    {
      Code: 'NZD',
      Currency: 'New Zealand Dollar',
    },
    {
      Code: 'OMR',
      Currency: 'Rial Omani',
    },
    {
      Code: 'PAB',
      Currency: 'Balboa',
    },
    {
      Code: 'PEN',
      Currency: 'Sol',
    },
    {
      Code: 'PGK',
      Currency: 'Kina',
    },
    {
      Code: 'PHP',
      Currency: 'Philippine Peso',
    },
    {
      Code: 'PKR',
      Currency: 'Pakistan Rupee',
    },
    {
      Code: 'PLN',
      Currency: 'Zloty',
    },
    {
      Code: 'PYG',
      Currency: 'Guarani',
    },
    {
      Code: 'QAR',
      Currency: 'Qatari Rial',
    },
    {
      Code: 'RON',
      Currency: 'Romanian Leu',
    },
    {
      Code: 'RSD',
      Currency: 'Serbian Dinar',
    },
    {
      Code: 'RUB',
      Currency: 'Russian Ruble',
    },
    {
      Code: 'RWF',
      Currency: 'Rwanda Franc',
    },
    {
      Code: 'SAR',
      Currency: 'Saudi Riyal',
    },
    {
      Code: 'SBD',
      Currency: 'Solomon Islands Dollar',
    },
    {
      Code: 'SCR',
      Currency: 'Seychelles Rupee',
    },
    {
      Code: 'SDG',
      Currency: 'Sudanese Pound',
    },
    {
      Code: 'SEK',
      Currency: 'Swedish Krona',
    },
    {
      Code: 'SGD',
      Currency: 'Singapore Dollar',
    },
    {
      Code: 'SHP',
      Currency: 'Saint Helena Pound',
    },
    {
      Code: 'SLE',
      Currency: 'Leone',
    },
    {
      Code: 'SLL',
      Currency: 'Leone',
    },
    {
      Code: 'SOS',
      Currency: 'Somali Shilling',
    },
    {
      Code: 'SRD',
      Currency: 'Surinam Dollar',
    },
    {
      Code: 'SSP',
      Currency: 'South Sudanese Pound',
    },
    {
      Code: 'STN',
      Currency: 'Dobra',
    },
    {
      Code: 'SVC',
      Currency: 'El Salvador Colon',
    },
    {
      Code: 'SYP',
      Currency: 'Syrian Pound',
    },
    {
      Code: 'SZL',
      Currency: 'Lilangeni',
    },
    {
      Code: 'THB',
      Currency: 'Baht',
    },
    {
      Code: 'TJS',
      Currency: 'Somoni',
    },
    {
      Code: 'TMT',
      Currency: 'Turkmenistan New Manat',
    },
    {
      Code: 'TND',
      Currency: 'Tunisian Dinar',
    },
    {
      Code: 'TOP',
      Currency: 'Pa’anga',
    },
    {
      Code: 'TRY',
      Currency: 'Turkish Lira',
    },
    {
      Code: 'TTD',
      Currency: 'Trinidad and Tobago Dollar',
    },
    {
      Code: 'TWD',
      Currency: 'New Taiwan Dollar',
    },
    {
      Code: 'TZS',
      Currency: 'Tanzanian Shilling',
    },
    {
      Code: 'UAH',
      Currency: 'Hryvnia',
    },
    {
      Code: 'UGX',
      Currency: 'Uganda Shilling',
    },
    {
      Code: 'USD',
      Currency: 'US Dollar',
    },
    {
      Code: 'USN',
      Currency: 'US Dollar (Next day)',
    },
    {
      Code: 'UYI',
      Currency: 'Uruguay Peso en Unidades Indexadas (UI)',
    },
    {
      Code: 'UYU',
      Currency: 'Peso Uruguayo',
    },
    {
      Code: 'UYW',
      Currency: 'Unidad Previsional',
    },
    {
      Code: 'UZS',
      Currency: 'Uzbekistan Sum',
    },
    {
      Code: 'VED',
      Currency: 'Bolívar Soberano',
    },
    {
      Code: 'VES',
      Currency: 'Bolívar Soberano',
    },
    {
      Code: 'VND',
      Currency: 'Dong',
    },
    {
      Code: 'VUV',
      Currency: 'Vatu',
    },
    {
      Code: 'WST',
      Currency: 'Tala',
    },
    {
      Code: 'XAF',
      Currency: 'CFA Franc BEAC',
    },
    {
      Code: 'XAG',
      Currency: 'Silver',
    },
    {
      Code: 'XAU',
      Currency: 'Gold',
    },
    {
      Code: 'XBA',
      Currency: 'Bond Markets Unit European Composite Unit (EURCO)',
    },
    {
      Code: 'XBB',
      Currency: 'Bond Markets Unit European Monetary Unit (E.M.U.-6)',
    },
    {
      Code: 'XBC',
      Currency: 'Bond Markets Unit European Unit of Account 9 (E.U.A.-9)',
    },
    {
      Code: 'XBD',
      Currency: 'Bond Markets Unit European Unit of Account 17 (E.U.A.-17)',
    },
    {
      Code: 'XCD',
      Currency: 'East Caribbean Dollar',
    },
    {
      Code: 'XDR',
      Currency: 'SDR (Special Drawing Right)',
    },
    {
      Code: 'XOF',
      Currency: 'CFA Franc BCEAO',
    },
    {
      Code: 'XPD',
      Currency: 'Palladium',
    },
    {
      Code: 'XPF',
      Currency: 'CFP Franc',
    },
    {
      Code: 'XPT',
      Currency: 'Platinum',
    },
    {
      Code: 'XSU',
      Currency: 'Sucre',
    },
    {
      Code: 'XUA',
      Currency: 'ADB Unit of Account',
    },
    {
      Code: 'XXX',
      Currency: 'The codes assigned for transactions where no currency is involved',
    },
    {
      Code: 'YER',
      Currency: 'Yemeni Rial',
    },
    {
      Code: 'ZAR',
      Currency: 'Rand',
    },
    {
      Code: 'ZMW',
      Currency: 'Zambian Kwacha',
    },
    {
      Code: 'ZWL',
      Currency: 'Zimbabwe Dollar',
    },
  ]

  /**
   * Get all currency codes
   * @returns An array of all currency codes
   */
  public getAllCurrencyCodes(): CurrencyCode[] {
    return this.currencyCodes
  }

  /**
   * Get a currency by its code
   * @param code The currency code to look up
   * @returns The currency object if found, undefined otherwise
   */
  public getCurrencyByCode(code: string): CurrencyCode | undefined {
    return this.currencyCodes.find((currency) => currency.Code === code)
  }

  /**
   * Get a currency name by its code
   * @param code The currency code to look up
   * @returns The currency name if found, undefined otherwise
   */
  public getCurrencyNameByCode(code: string): string | undefined {
    const currency = this.getCurrencyByCode(code)
    return currency?.Currency
  }

  /**
   * Check if a currency code exists
   * @param code The currency code to check
   * @returns True if the currency code exists, false otherwise
   */
  public isCurrencyCodeValid(code: string): boolean {
    return this.currencyCodes.some((currency) => currency.Code === code)
  }

  /**
   * Get a list of currency codes that match a partial name
   * @param partialName A partial name to search for
   * @returns An array of matching currency codes
   */
  public searchCurrencyByName(partialName: string): CurrencyCode[] {
    const lowercaseName = partialName.toLowerCase()
    return this.currencyCodes.filter((currency) =>
      currency.Currency.toLowerCase().includes(lowercaseName)
    )
  }

  /**
   * Convert from JPY to MYR (specific to the project requirements)
   * @param amount The amount in JPY
   * @param rate The conversion rate (default: current approximate rate)
   * @returns The amount converted to MYR with proper decimal places
   */
  public convertJPYtoMYR(amount: number, rate: number): number {
    // JPY is typically a zero-decimal currency, while MYR uses 2 decimal places
    // This conversion ensures proper decimal handling
    return Number.parseFloat((amount * rate).toFixed(2))
  }

  /**
   * Get default currency for the system (MYR based on project requirements)
   * @returns The default currency code
   */
  public getDefaultCurrencyCode(): string {
    return 'MYR'
  }
}

export const taxTypesService = new TaxTypesService()
export const unitTypesService = new UnitTypesService()
export const msicsService = new MSICService()
export const stateCodesService = new StateCodesService()
export const paymentMethodsService = new PaymentMethodsService()
export const invoiceTypesService = new InvoiceTypesService()
export const classificationCodesService = new ClassificationCodeService()
export const countryCodesService = new CountryCodeService()
