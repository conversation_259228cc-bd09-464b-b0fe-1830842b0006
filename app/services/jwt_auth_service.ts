import { createRemoteJWKSet, jwtVerify } from 'jose'
import env from '#start/env'
import type User from '#models/user'
import { UserSyncService } from './user_sync_service.js'

/**
 * JWT payload interface
 */
export interface JWTPayload {
  sub: string
  email?: string
  name?: string
  given_name?: string
  family_name?: string
  iss: string
  aud: string
  exp: number
  iat: number
  [key: string]: any
}

/**
 * Service to handle JWT authentication with J<PERSON><PERSON>
 */

// biome-ignore lint/complexity/noStaticOnlyClass: <explanation>
export class JwtAuthService {
  /**
   * The JWKS URL from the License auth service
   */
  private static jwksUrl = env.get('LICENSE_JWKS_URL')

  /**
   * The JWT issuer
   */
  private static issuer = env.get('LICENSE_JWT_ISSUER')

  /**
   * The JWT audience
   */
  private static audience = env.get('LICENSE_JWT_AUDIENCE')

  /**
   * The JWKS client
   */
  private static jwks = createRemoteJWKSet(new URL(JwtAuthService.jwksUrl))

  /**
   * Verify a JWT token using JWKS
   */
  public static async verifyToken(
    token: string
  ): Promise<{ valid: boolean; payload?: JWTPayload; error?: { code: string; message: string } }> {
    try {
      const { payload } = await jwtVerify(token, JwtAuthService.jwks, {
        issuer: JwtAuthService.issuer,
        audience: JwtAuthService.audience,
      })

      // Cast the jose library's JWTPayload to our custom JWTPayload interface
      const typedPayload = payload as unknown as JWTPayload

      return {
        valid: true,
        payload: typedPayload,
      }
    } catch (error) {
      console.error('Failed to verify JWT token:', error)

      // Check if the error is due to an expired token
      if (error.code === 'ERR_JWT_EXPIRED') {
        return {
          valid: false,
          error: {
            code: 'ERR_JWT_EXPIRED',
            message: 'JWT token has expired',
          },
        }
      }

      // Handle other errors
      return {
        valid: false,
        error: {
          code: error.code || 'UNKNOWN_ERROR',
          message: error.message || 'Unknown error occurred during token verification',
        },
      }
    }
  }

  /**
   * Find or create a local user from a JWT payload
   */
  public static async findOrCreateLocalUser(payload: JWTPayload): Promise<User | null> {
    try {
      // Extract user ID from the payload
      const licenseUserId = payload.sub

      if (!licenseUserId) {
        console.error('No subject (user ID) found in JWT payload')
        return null
      }

      // Use UserSyncService to find or create the user
      const user = await UserSyncService.findOrCreateLocalUser(licenseUserId, payload)

      if (user) {
        // Update user information if needed
        if (payload.email !== user.email || payload.name !== user.fullName) {
          user.email = payload.email || user.email
          user.fullName = payload.name || user.fullName
          await user.save()
        }
      }

      return user
    } catch (error) {
      console.error('Failed to find or create local user from JWT payload:', error)
      return null
    }
  }
}
