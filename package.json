{"name": "einvoice_be", "version": "0.0.0", "private": true, "type": "module", "license": "UNLICENSED", "scripts": {"start": "node bin/server.js", "build": "node ace build", "dev": "node ace serve --hmr", "dev-watch": "node ace serve --watch", "test": "node ace test", "lint": "eslint .", "format": "prettier --write .", "typecheck": "tsc --noEmit"}, "imports": {"#controllers/*": "./app/controllers/*.js", "#exceptions/*": "./app/exceptions/*.js", "#models/*": "./app/models/*.js", "#mails/*": "./app/mails/*.js", "#services/*": "./app/services/*.js", "#listeners/*": "./app/listeners/*.js", "#events/*": "./app/events/*.js", "#middleware/*": "./app/middleware/*.js", "#validators/*": "./app/validators/*.js", "#providers/*": "./providers/*.js", "#policies/*": "./app/policies/*.js", "#abilities/*": "./app/abilities/*.js", "#database/*": "./database/*.js", "#start/*": "./start/*.js", "#tests/*": "./tests/*.js", "#config/*": "./config/*.js", "#types/*": "./types/*.js"}, "devDependencies": {"@adonisjs/assembler": "^7.8.2", "@adonisjs/eslint-config": "^2.0.0-beta.6", "@adonisjs/prettier-config": "^1.4.4", "@adonisjs/tsconfig": "^1.4.0", "@japa/api-client": "^3.0.3", "@japa/assert": "^4.0.1", "@japa/plugin-adonisjs": "^4.0.0", "@japa/runner": "^4.2.0", "@swc/core": "1.10.14", "@types/luxon": "^3.4.2", "@types/node": "^22.13.2", "eslint": "^9.19.0", "hot-hook": "^0.4.0", "pino-pretty": "^13.0.0", "prettier": "^3.4.2", "ts-node-maintained": "^10.9.5", "typescript": "~5.7"}, "dependencies": {"@adonisjs/auth": "^9.3.1", "@adonisjs/core": "^6.17.1", "@adonisjs/cors": "^2.2.1", "@adonisjs/limiter": "^2.3.3", "@adonisjs/lucid": "^21.6.0", "@adonisjs/redis": "^9.2.0", "@vinejs/vine": "^3.0.0", "adonis-autoswagger": "^3.64.0", "adonisjs-scheduler": "^2.4.0", "axios": "^1.7.9", "jose": "^6.0.10", "luxon": "^3.5.0", "pg": "^8.13.1", "radash": "^12.1.0", "reflect-metadata": "^0.2.2"}, "hotHook": {"boundaries": ["./app/controllers/**/*.ts", "./app/middleware/*.ts"]}, "overrides": {"strtok3": "8.0.1"}, "resolutions": {"strtok3": "8.0.1"}, "pnpm": {"overrides": {"strtok3": "8.0.1"}}, "prettier": "@adonisjs/prettier-config"}