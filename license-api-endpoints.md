# License API Endpoints for Integration

This document outlines the API endpoints that need to be implemented in the NextJS License project to enable integration with the Core backend.

## Internal API Endpoints

These endpoints should be protected with an API key and only accessible from the Core backend.

### User Management

#### Create User
- **URL**: `/api/internal/users`
- **Method**: `POST`
- **Auth**: API Key
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "name": "User Name",
    "firstName": "User",
    "lastName": "Name",
    "phone": "**********"
  }
  ```
- **Response**:
  ```json
  {
    "id": "uuid-string",
    "email": "<EMAIL>",
    "name": "User Name"
  }
  ```

#### Update User
- **URL**: `/api/internal/users/:id`
- **Method**: `PUT`
- **Auth**: API Key
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "name": "Updated Name"
  }
  ```
- **Response**:
  ```json
  {
    "id": "uuid-string",
    "email": "<EMAIL>",
    "name": "Updated Name"
  }
  ```

#### Get User
- **URL**: `/api/internal/users/:id`
- **Method**: `GET`
- **Auth**: API Key
- **Response**:
  ```json
  {
    "id": "uuid-string",
    "email": "<EMAIL>",
    "name": "User Name"
  }
  ```

### Organization Management

#### Create Organization
- **URL**: `/api/internal/organizations`
- **Method**: `POST`
- **Auth**: API Key
- **Request Body**:
  ```json
  {
    "name": "Organization Name",
    "ownerId": "user-uuid-string",
    "metadata": "{\"tinCode\":\"C12345\",\"registrationNumber\":\"12345\",\"registrationType\":\"BRN\"}"
  }
  ```
- **Response**:
  ```json
  {
    "id": "uuid-string",
    "name": "Organization Name",
    "slug": "organization-name"
  }
  ```

#### Update Organization
- **URL**: `/api/internal/organizations/:id`
- **Method**: `PUT`
- **Auth**: API Key
- **Request Body**:
  ```json
  {
    "name": "Updated Organization",
    "metadata": "{\"tinCode\":\"C12345\",\"registrationNumber\":\"12345\",\"registrationType\":\"BRN\"}"
  }
  ```
- **Response**:
  ```json
  {
    "id": "uuid-string",
    "name": "Updated Organization",
    "slug": "updated-organization"
  }
  ```

#### Get Organization
- **URL**: `/api/internal/organizations/:id`
- **Method**: `GET`
- **Auth**: API Key
- **Response**:
  ```json
  {
    "id": "uuid-string",
    "name": "Organization Name",
    "slug": "organization-name",
    "metadata": "{\"tinCode\":\"C12345\",\"registrationNumber\":\"12345\",\"registrationType\":\"BRN\"}"
  }
  ```

### Premium Tier Management

#### Get Premium Tier
- **URL**: `/api/internal/premium-tiers/:id`
- **Method**: `GET`
- **Auth**: API Key
- **Response**:
  ```json
  {
    "id": "uuid-string",
    "name": "Premium Tier Name",
    "description": "Premium Tier Description",
    "maxInvoicesPerMonth": 100,
    "price": 29.99
  }
  ```

#### List Premium Tiers
- **URL**: `/api/internal/premium-tiers`
- **Method**: `GET`
- **Auth**: API Key
- **Response**:
  ```json
  [
    {
      "id": "uuid-string",
      "name": "Basic",
      "description": "Basic Tier",
      "maxInvoicesPerMonth": 50,
      "price": 19.99
    },
    {
      "id": "uuid-string",
      "name": "Pro",
      "description": "Pro Tier",
      "maxInvoicesPerMonth": 200,
      "price": 49.99
    }
  ]
  ```

## Public API Endpoints

These endpoints should be accessible from the frontend and authenticated with user tokens.

### User Profile

#### Get Current User
- **URL**: `/api/user`
- **Method**: `GET`
- **Auth**: User Token
- **Response**:
  ```json
  {
    "id": "uuid-string",
    "email": "<EMAIL>",
    "name": "User Name",
    "organizations": [
      {
        "id": "org-uuid-string",
        "name": "Organization Name",
        "role": "owner"
      }
    ]
  }
  ```

### Organization Management

#### Get User Organizations
- **URL**: `/api/organizations`
- **Method**: `GET`
- **Auth**: User Token
- **Response**:
  ```json
  [
    {
      "id": "org-uuid-string",
      "name": "Organization Name",
      "role": "owner"
    }
  ]
  ```

#### Get Organization Details
- **URL**: `/api/organizations/:id`
- **Method**: `GET`
- **Auth**: User Token
- **Response**:
  ```json
  {
    "id": "org-uuid-string",
    "name": "Organization Name",
    "members": [
      {
        "id": "member-uuid-string",
        "userId": "user-uuid-string",
        "userName": "User Name",
        "userEmail": "<EMAIL>",
        "role": "owner"
      }
    ],
    "premiumTier": {
      "id": "tier-uuid-string",
      "name": "Premium Tier Name",
      "maxInvoicesPerMonth": 100
    }
  }
  ```

### Premium Tier Management

#### Upgrade Organization Premium Tier
- **URL**: `/api/organizations/:id/upgrade`
- **Method**: `POST`
- **Auth**: User Token
- **Request Body**:
  ```json
  {
    "premiumTierId": "tier-uuid-string"
  }
  ```
- **Response**:
  ```json
  {
    "id": "upgrade-uuid-string",
    "organizationId": "org-uuid-string",
    "premiumTierId": "tier-uuid-string",
    "status": "pending",
    "paymentStatus": "pending",
    "amount": 29.99,
    "transactionId": "transaction-id"
  }
  ```

## Webhooks

### Premium Tier Upgrade Webhook
- **URL**: `/api/webhooks/premium-upgrade`
- **Method**: `POST`
- **Auth**: Webhook Secret
- **Request Body**:
  ```json
  {
    "event": "premium.upgrade.completed",
    "data": {
      "organizationId": "org-uuid-string",
      "premiumTierId": "tier-uuid-string",
      "upgradeId": "upgrade-uuid-string"
    }
  }
  ```
- **Response**: `200 OK`

### User Creation Webhook
- **URL**: `/api/webhooks/user-created`
- **Method**: `POST`
- **Auth**: Webhook Secret
- **Request Body**:
  ```json
  {
    "event": "user.created",
    "data": {
      "userId": "user-uuid-string",
      "email": "<EMAIL>"
    }
  }
  ```
- **Response**: `200 OK`

### Organization Creation Webhook
- **URL**: `/api/webhooks/organization-created`
- **Method**: `POST`
- **Auth**: Webhook Secret
- **Request Body**:
  ```json
  {
    "event": "organization.created",
    "data": {
      "organizationId": "org-uuid-string",
      "name": "Organization Name",
      "ownerId": "user-uuid-string"
    }
  }
  ```
- **Response**: `200 OK`
