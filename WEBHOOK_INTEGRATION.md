# Webhook Integration for User Synchronization

This document outlines how to implement webhook notifications from the License system to the Core backend for user synchronization.

## Overview

When a new user is created in the License system, a webhook notification should be sent to the Core backend to create a corresponding user record. This ensures that users created in the License system are automatically available in the Core system.

## Webhook Endpoints

The Core backend exposes the following webhook endpoints:

### User Creation Webhook

- **URL**: `/webhooks/v1/license`
- **Method**: `POST`
- **Headers**:
  - `Content-Type: application/json`
  - `X-Webhook-Secret: <WEBHOOK_SECRET>`
- **Request Body**:
  ```json
  {
    "event": "user.created",
    "data": {
      "userId": "uuid-string",
      "email": "<EMAIL>",
      "name": "User Name",
      "firstName": "User",
      "lastName": "Name",
      "phone": "**********"
    }
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "message": "User created successfully",
    "userId": 123
  }
  ```

### Organization Creation Webhook

- **URL**: `/webhooks/v1/license`
- **Method**: `POST`
- **Headers**:
  - `Content-Type: application/json`
  - `X-Webhook-Secret: <WEBHOOK_SECRET>`
- **Request Body**:
  ```json
  {
    "event": "organization.created",
    "data": {
      "organizationId": "org-uuid-string",
      "name": "Organization Name",
      "ownerId": "user-uuid-string"
    }
  }
  ```
- **Response**:
  ```json
  {
    "success": true,
    "message": "Organization webhook received successfully"
  }
  ```

## Implementation in License System

### 1. Configure Webhook Secret

Set up a secure webhook secret that will be shared between the License system and the Core backend. This secret should be stored securely in environment variables.

```
CORE_WEBHOOK_SECRET=your-webhook-secret-here
```

### 2. Implement Webhook Trigger for User Creation

When a new user is created in the License system, trigger a webhook notification to the Core backend:

```typescript
// In your License system user creation handler
async function createUser(userData) {
  // Create the user in the License database
  const user = await prisma.user.create({
    data: {
      email: userData.email,
      name: userData.name,
      // other user fields
    }
  });

  // Send webhook to Core backend
  await axios.post(
    `${process.env.CORE_BACKEND_URL}/webhooks/v1/license/user-created`,
    {
      event: 'user.created',
      data: {
        userId: user.id,
        email: user.email,
        name: user.name,
        firstName: user.firstName,
        lastName: user.lastName,
        phone: user.phone
      }
    },
    {
      headers: {
        'Content-Type': 'application/json',
        'X-Webhook-Secret': process.env.CORE_WEBHOOK_SECRET
      }
    }
  );

  return user;
}
```

### 3. Implement Webhook Trigger for Organization Creation

When a new organization is created in the License system, trigger a webhook notification to the Core backend:

```typescript
// In your License system organization creation handler
async function createOrganization(organizationData) {
  // Create the organization in the License database
  const organization = await prisma.organization.create({
    data: {
      name: organizationData.name,
      ownerId: organizationData.ownerId,
      // other organization fields
    }
  });

  // Send webhook to Core backend
  await axios.post(
    `${process.env.CORE_BACKEND_URL}/webhooks/v1/license/organization-created`,
    {
      event: 'organization.created',
      data: {
        organizationId: organization.id,
        name: organization.name,
        ownerId: organization.ownerId
      }
    },
    {
      headers: {
        'Content-Type': 'application/json',
        'X-Webhook-Secret': process.env.CORE_WEBHOOK_SECRET
      }
    }
  );

  return organization;
}
```

### 4. Implement Error Handling and Retries

Implement error handling and retry logic for webhook notifications to ensure reliability:

```typescript
async function sendWebhook(url, data, secret, retries = 3) {
  try {
    const response = await axios.post(url, data, {
      headers: {
        'Content-Type': 'application/json',
        'X-Webhook-Secret': secret
      }
    });
    return response.data;
  } catch (error) {
    if (retries > 0) {
      // Wait for a short time before retrying
      await new Promise(resolve => setTimeout(resolve, 1000));
      return sendWebhook(url, data, secret, retries - 1);
    }

    // Log the error and continue
    console.error('Failed to send webhook after retries:', error);

    // Optionally, store failed webhooks in a queue for later processing
    await storeFailedWebhook(url, data, secret);
  }
}
```

## Security Considerations

1. **Webhook Secret**: Use a strong, randomly generated secret for webhook authentication.
2. **HTTPS**: Ensure all webhook communications use HTTPS.
3. **Validation**: Validate all incoming webhook data before processing.
4. **Rate Limiting**: Implement rate limiting to prevent abuse.
5. **IP Whitelisting**: Consider whitelisting the IP addresses of the License system.

## Testing

To test the webhook integration:

1. Start both the Core backend and License system.
2. Create a new user in the License system.
3. Verify that a corresponding user is created in the Core backend.
4. Check the logs for any errors or issues.

## Troubleshooting

If webhooks are not being received:

1. Check that the webhook URL is correct.
2. Verify that the webhook secret matches between systems.
3. Check network connectivity between the systems.
4. Look for any errors in the logs of both systems.
5. Ensure the Core backend is accessible from the License system.

## Conclusion

By implementing webhook notifications, users created in the License system will be automatically synchronized to the Core backend, ensuring a seamless experience across both systems.
