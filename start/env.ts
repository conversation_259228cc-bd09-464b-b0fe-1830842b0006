/*
|--------------------------------------------------------------------------
| Environment variables service
|--------------------------------------------------------------------------
|
| The `Env.create` method creates an instance of the Env service. The
| service validates the environment variables and also cast values
| to JavaScript data types.
|
*/

import { Env } from '@adonisjs/core/env'

export default await Env.create(new URL('../', import.meta.url), {
  NODE_ENV: Env.schema.enum(['development', 'production', 'test'] as const),
  PORT: Env.schema.number(),
  APP_KEY: Env.schema.string(),
  HOST: Env.schema.string({ format: 'host' }),
  LOG_LEVEL: Env.schema.enum(['fatal', 'error', 'warn', 'info', 'debug', 'trace']),

  /*
  |----------------------------------------------------------
  | Variables for configuring database connection
  |----------------------------------------------------------
  */
  DB_HOST: Env.schema.string({ format: 'host' }),
  DB_PORT: Env.schema.number(),
  DB_USER: Env.schema.string(),
  DB_PASSWORD: Env.schema.string.optional(),
  DB_DATABASE: Env.schema.string(),
  DB_SSL: Env.schema.boolean(),

  /*
  |----------------------------------------------------------
  | Variables for License API integration
  |----------------------------------------------------------
  */
  LICENSE_API_URL: Env.schema.string(),
  LICENSE_INCOMING_API_KEY: Env.schema.string(), // api.myinvoice -> core.myinvoice
  LICENSE_OUTGOING_API_KEY: Env.schema.string(), // core.myinvoice -> api.myinvoice
  LICENSE_WEBHOOK_SECRET: Env.schema.string(),
  LICENSE_JWKS_URL: Env.schema.string(),
  LICENSE_JWT_ISSUER: Env.schema.string(),
  LICENSE_JWT_AUDIENCE: Env.schema.string(),

  /*
  |----------------------------------------------------------
  | Variables for configuring the limiter package
  |----------------------------------------------------------
  */
  LIMITER_STORE: Env.schema.enum(['redis', 'memory'] as const),

  REDIS_HOST: Env.schema.string({ format: 'host' }),
  REDIS_PORT: Env.schema.number(),
  REDIS_PASSWORD: Env.schema.string.optional(),

  /*
  |----------------------------------------------------------
  | Variables for MyInvoice
  |----------------------------------------------------------
  */
  MYINVOIS_API_PROD_URL: Env.schema.string({ format: 'url' }),
  MYINVOIS_IDENTITY_PROD_URL: Env.schema.string({ format: 'url' }),
  MYINVOIS_API_SANDBOX_URL: Env.schema.string({ format: 'url' }),
  MYINVOIS_IDENTITY_SANDBOX_URL: Env.schema.string({ format: 'url' }),
  MYINVOIS_API_GENERAL_BUYER_NAME: Env.schema.string(),
  MYINVOIS_API_GENERAL_TIN: Env.schema.string(),
  MYINVOIS_API_DEFAULT_TAX_TYPE: Env.schema.string(),

  /*
  |----------------------------------------------------------
  | Variables for Shopify
  |----------------------------------------------------------
  */
  SHOPIFY_API_SECRET: Env.schema.string(),
})
