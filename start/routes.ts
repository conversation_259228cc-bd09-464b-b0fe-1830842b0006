/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'
import type { HttpContext } from '@adonisjs/core/http'
import { apiThrottle } from '#start/limiter'
import swagger from '#config/swagger'
import AutoSwagger from 'adonis-autoswagger'
import { middleware } from './kernel.js'

const AuthController = () => import('#controllers/http/auth_controller')
const InvoicesController = () => import('#controllers/http/invoices_controller')
const CompaniesController = () => import('#controllers/http/companies_controller')
const OrdersController = () => import('#controllers/http/orders_controller')
const SubmittedDocumentsController = () =>
  import('#controllers/http/submitted_documents_controller')
const LicensesController = () => import('#controllers/http/licenses_controller')
const WebhooksController = () => import('#controllers/http/webhooks_controller')
const EinvoiceRequestsController = () => import('#controllers/http/einvoice_requests_controller')

router.get('/', async ({ response }: HttpContext) => {
  return response.json({
    hello: 'world',
  })
})

router
  .group(() => {
    router
      .group(() => {
        router.post('/login', [AuthController, 'login']).use(apiThrottle)
        router.post('/api-key', [AuthController, 'apiKeyAuth']).use(apiThrottle)
        router.post('/shopify-install', [AuthController, 'shopifyInstall']).use(apiThrottle)
      })
      .prefix('/auth')

    router
      .group(() => {
        router.post('/auth/myinvois-api', [AuthController, 'loginMyInvoisApi']).use(apiThrottle)
      })
      .use(middleware.auth())

    router
      .group(() => {
        router
          .post('/selfserve-request', [EinvoiceRequestsController, 'selfServeRequest'])
          .use(middleware.saasAuth({ guard: 'keyAuth' }))
        router
          .post('/review-selfserve-request/:einvoice_request_id', [
            EinvoiceRequestsController,
            'sellerReviewSelfServeRequest',
          ])
          .use(middleware.saasAuth({ guard: 'jwtAuth' }))
      })
      .prefix('/einvoice-request')

    router
      .group(() => {
        router.post('/shopify', [InvoicesController, 'processShopifyInvoiceSubmission'])
        router.get('/unlinked/:company_id', [
          InvoicesController,
          'retrieveUnlinkedSubmittedDocuments',
        ])
        router.post('/seller-full-manual-submission', [
          InvoicesController,
          'sellerFullManualSubmitEinvoice',
        ])

        router.get('/lhdn-document-details/:uuid', [
          InvoicesController,
          'retrieveLhdnSubmittedDocuments',
        ])
      })
      .prefix('/invoice')
      .use(middleware.auth())

    router
      .group(() => {
        router.post('/onboarding', [AuthController, 'loginMyInvoisApi'])
        router.get('/companies', [CompaniesController, 'shopifyShow'])
        router.post('/companies', [CompaniesController, 'shopifyUpdate'])
        router.post('/sync-document-status', [InvoicesController, 'syncShopifyDocumentStatus'])
      })
      .prefix('/shopify')
      .use(middleware.auth())

    router
      .group(() => {
        router.get('/:id', [SubmittedDocumentsController, 'retrieveSubmittedDocument'])
      })
      .prefix('/submitted-document')
      .use(middleware.auth())
  })
  .prefix('/api/v1')

// SAAS App
// from app.myinvoice frontend -> to core.myinvoice
router
  .group(() => {
    // user
    router.get('/me/companies', [CompaniesController, 'showMyCompany'])
    router.put('/me/companies', [CompaniesController, 'updateMyCompany'])

    // admin
    // router.resource('companies', CompaniesController).only(['store', 'update'])

    // Orders & Submitted Documents Related Routes
    router.post('/orders/:id/submit-to-myinvois', [OrdersController, 'submitOrderToMyInvois'])
    router.resource('orders', OrdersController).as('saas-app-orders')
    router.get('/submitted-documents/order/:order_id', [
      SubmittedDocumentsController,
      'retrieveSubmittedDocumentsWithOrderId',
    ])

    router.post('/invoice-codes/validate', [OrdersController, 'validateInvoiceCodes'])

    router.post('/tin-validation', [CompaniesController, 'validateTin'])

    router.post('/orders/:id/cancel-latest-submitted-document', [
      OrdersController,
      'cancelLatestSubmittedDocument',
    ])

  })
  .prefix('/app/v1')
  .use(middleware.saasAuth({ guard: 'jwtAuth' }))

// SAAS API
// from api.myinvoice api -> to core.myinvoice
router
  .group(() => {
    router.resource('orders', OrdersController).as('saas-api-orders')
    router.post('/orders/:id/submit', [OrdersController, 'submitOrderToMyInvois'])
    router.post('/orders/validate', [OrdersController, 'validateInvoiceCodes'])
    // router.post('/orders/cancel', [OrdersController, 'cancelSubmittedDocuments'])
    // router.post('/orders/sync', [OrdersController, 'syncSubmittedDocuments'])

    router.get('/companies', [CompaniesController, 'showMyCompany'])
    router.put('/companies', [CompaniesController, 'updateMyCompany'])

    router.post('/tin-validation', [CompaniesController, 'validateTin'])

    // TODO: To be remove (this is for Pohkim to use in DEMO)
    router.post('/submit-consolidated-invoices', [OrdersController, 'submitConsolidateOrdersToMyInvoice'])
  })
  .prefix('/api/v1')
  .use(middleware.saasAuth({ guard: 'keyAuth' }))

// License API for integration with License system
router
  .group(() => {
    // User-focused endpoints
    router.get('/users/:id', [LicensesController, 'getCoreUser'])
    router.get('/users/:id/licenses', [LicensesController, 'getUserLicense'])
    router.get('/users/:id/organizations', [LicensesController, 'getUserOrganizations'])
    router.get('/users/:id/primary-organization', [
      LicensesController,
      'getUserPrimaryOrganization',
    ])

    // Synchronization endpoints
    router.post('/users/:id/sync', [LicensesController, 'syncUser'])
    router.post('/auth/sync-to-license', [LicensesController, 'syncCoreUserToLicense'])
  })
  .prefix('/api/v1/integrations')
  .use(middleware.saasAuth({ guard: 'jwtAuth' }))

router
  .group(() => {
    router.post('/shopify-install', [AuthController, 'shopifyInstallWebhook']).use(apiThrottle)

    // License webhooks
    // from app.myinvoice sign-ups -> to core.myinvoice
    router.post('/license', [WebhooksController, 'licenseHandler'])
  })
  .prefix('/webhooks/v1')

router.get('/swagger', async () => {
  return AutoSwagger.default.docs(router.toJSON(), swagger)
})

router.get('/docs', async () => {
  return AutoSwagger.default.scalar('/swagger') // to use Scalar instead
})
