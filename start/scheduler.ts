
import { EInvoiceService } from '#services/lhdn_service';
import scheduler from 'adonisjs-scheduler/services/main'
import logger from '@adonisjs/core/services/logger'

scheduler.call(async() => {
    // Sync Submitted Document scheduler
    await EInvoiceService.schedulerSyncSubmittedDocumentStatus()
    logger.info('=============Scheduler Every Minute=============')
    logger.debug('Syncing Submitted Document Status from MyInvois...')

    await EInvoiceService.schedulerSyncInvalidSubmittedDocumentFailDetails()
    logger.debug('Syncing Submitted Document Failed MyInvois 2nd level validation Reasons...')
    logger.info('=============Scheduler Every Minute=============')
}).everyMinute();