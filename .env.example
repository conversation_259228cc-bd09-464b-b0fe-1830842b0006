TZ=UTC
PORT=3333
HOST=localhost
LOG_LEVEL=info
APP_KEY=
NODE_ENV=development
DB_HOST=127.0.0.1
DB_PORT=5432
DB_USER=root
DB_PASSWORD=root
DB_DATABASE=app

LIMITER_STORE=redis
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=

# License API Integration
LICENSE_API_URL=http://localhost:3002/api
LICENSE_INCOMING_API_KEY=your-in-going-api-key-here # used to validate request from e-invoice app (any other app that we developed and needs to integrate with core backend)
LICENSE_OUTGOING_API_KEY=your-out-going-api-key-here
LICENSE_WEBHOOK_SECRET=your-webhook-secret-here
LICENSE_JWKS_URL=http://localhost:3002/api/auth/jwks
LICENSE_JWT_ISSUER=auth.myinvoice.com
LICENSE_JWT_AUDIENCE=auth.myinvoice.com

# myinvoice
MYINVOIS_API_PROD_URL=https://api.myinvois.hasil.gov.my/api/v1.0
MYINVOIS_IDENTITY_PROD_URL=https://api.myinvois.hasil.gov.my
MYINVOIS_API_SANDBOX_URL=https://preprod-api.myinvois.hasil.gov.my/api/v1.0
MYINVOIS_IDENTITY_SANDBOX_URL=https://preprod-api.myinvois.hasil.gov.my
MYINVOIS_API_GENERAL_BUYER_NAME="General Public"
MYINVOIS_API_GENERAL_TIN=EI00000000010
MYINVOIS_API_DEFAULT_TAX_TYPE=06

MYINVOIS_API_SANDBOX_CLIENT_ID=40065a06-2a8f-4938-8127-1c17c9571003
MYINVOIS_API_SANDBOX_CLIENT_SECRET_1=5d0a7a40-71c2-4957-8dbc-054b16efe577
MYINVOIS_API_SANDBOX_CLIENT_SECRET_2=ddb927de-c33b-40c3-bef8-690fb61d922f

# shopify
SHOPIFY_API_SECRET=