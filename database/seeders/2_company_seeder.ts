import Company from '#models/company'
import User from '#models/user'
import { RegistrationType } from '#types/einvoice'
import { BaseSeeder } from '@adonisjs/lucid/seeders'

export default class extends BaseSeeder {
  async run() {
    const user = await User.query().where('email', '<EMAIL>').firstOrFail()
    // Write your database queries inside the run method
    await Company.create({
      clientId: process.env.MYINVOIS_API_SANDBOX_CLIENT_ID,
      clientSecret: process.env.MYINVOIS_API_SANDBOX_CLIENT_SECRET_1,
      address: 'Company address full dummy 1',
      name: 'Cuegain Sdn. Bhd.',
      country: 'Malaysia',
      email: '<EMAIL>',
      msicCode: '46510',
      zipCode: '14000',
      state: 'Pulau <PERSON>nan<PERSON>',
      phone: '+60 12345678',
      tinCode: process.env.MYINVOIS_API_SANDBOX_TIN,
      userId: user.id,
      registrationNumber: process.env.IC_NUMBER,
      registrationType: RegistrationType.NRIC,
      city: 'Bukit Mertajam',
      businessActivityDescription: 'sell pc parts',
    })
  }
}
