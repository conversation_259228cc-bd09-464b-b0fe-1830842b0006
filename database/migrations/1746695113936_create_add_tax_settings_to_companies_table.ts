import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'companies'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      // Add tax settings columns
      table.specificType('applicable_tax_types', 'TEXT[]').nullable()
      table.specificType('sales_tax_rates', 'TEXT[]').nullable()
      table.specificType('service_tax_rates', 'TEXT[]').nullable()
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      // Remove tax settings columns
      table.dropColumn('applicable_tax_types')
      table.dropColumn('sales_tax_rates')
      table.dropColumn('service_tax_rates')
    })
  }
}
