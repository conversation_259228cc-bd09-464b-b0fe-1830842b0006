import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'orders'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropUnique(['invoice_code'])
      table.unique(['invoice_code', 'company_id'])
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.dropUnique(['invoice_code', 'company_id'])
      table.unique(['invoice_code'])
    })
  }
}
