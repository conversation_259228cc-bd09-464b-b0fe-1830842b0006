import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'shopify_stores'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')

      // Store identification
      table.string('shop_domain').notNullable().unique()
      table.string('shop_name').nullable()

      // App credentials
      table.string('access_token').nullable()

      // Webhook settings
      table.string('webhook_topic').nullable()
      table.string('webhook_address').nullable()

      // Relationship to users
      table.integer('user_id').unsigned().references('id').inTable('users').onDelete('CASCADE')

      // Timestamps
      table.timestamp('created_at', { useTz: true }).notNullable()
      table.timestamp('updated_at', { useTz: true }).notNullable()
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
