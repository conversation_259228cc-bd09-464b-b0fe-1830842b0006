import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'users'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      // Update auth_type enum to include 'license'
      // Note: We need to drop and recreate the column to modify the enum
      table.dropColumn('auth_type')
    })

    this.schema.alterTable(this.tableName, (table) => {
      table.enum('auth_type', ['password', 'license', 'shopify', 'api']).nullable()

      // Add external ID for linking to the License auth service
      table.string('external_id').nullable().unique()
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      // Update auth_type enum to include 'license'
      // Note: We need to drop and recreate the column to modify the enum
      table.dropColumn('auth_type')
    })

    this.schema.alterTable(this.tableName, (table) => {
      // Restore original auth_type enum
      table.enum('auth_type', ['password', 'shopify', 'api']).nullable()

      // Remove added columns
      table.dropColumn('external_id')
    })
  }
}
