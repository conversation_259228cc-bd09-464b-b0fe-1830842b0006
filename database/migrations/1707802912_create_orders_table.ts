import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'orders'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table
        .integer('company_id')
        .unsigned()
        .references('id')
        .inTable('companies')
        .onDelete('SET NULL')
      table.integer('user_id').unsigned().references('id').inTable('users').onDelete('SET NULL')
      table.string('invoice_code').unique()
      table.boolean('is_submitted_to_lhdn').defaultTo(false)
      table.string('seller_name')
      table.string('seller_tin')
      table.string('buyer_name')
      table.string('buyer_tin')
      table.jsonb('line_items')
      table.timestamp('created_at')
      table.timestamp('updated_at')
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
