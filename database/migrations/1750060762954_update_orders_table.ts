import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'orders'

  async up() {
    this.schema.alterTable(this.tableName, (table) => {
      table.renameColumn('invoice_level_allowance_charges', 'invoice_level_allowance_charge')
    })
  }

  async down() {
    this.schema.alterTable(this.tableName, (table) => {
      table.renameColumn('invoice_level_allowance_charge', 'invoice_level_allowance_charges')
    })
  }
}
