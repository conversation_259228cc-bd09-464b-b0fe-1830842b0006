import { BaseSchema } from '@adonisjs/lucid/schema'

export default class extends BaseSchema {
  protected tableName = 'companies'

  async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.integer('user_id').notNullable().references('id').inTable('users').onDelete('CASCADE') // a user can have multiple invoice api link (multiple companies), hence not unique
      // api info
      table.string('client_id').unique()
      table.string('client_secret')
      table.string('scope').nullable()
      table.text('access_token').nullable()
      table.integer('token_expires_in').nullable()
      table.timestamp('token_expires_at', { useTz: true }).nullable()
      // company info
      table.string('name').unique()
      table.string('tin_code').unique() // each TIN will only have one client_id hence, both client_id and tax are unique.
      table.string('registration_number')
      table.string('registration_type')
      table.string('sst_registration_number')
      table.string('tourism_tax_registration_number')
      table.string('business_activity_description')
      table.string('msic_code')
      table.string('country')
      table.string('state')
      table.string('city')
      table.string('zip_code')
      table.string('address')
      table.string('phone')
      table.string('email')
      table.string('bank_account')
      table.string('exporter_certified_number')
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  async down() {
    this.schema.dropTable(this.tableName)
  }
}
