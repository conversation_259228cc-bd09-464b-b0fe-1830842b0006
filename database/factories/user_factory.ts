import User from '#models/user'
import factory from '@adonisjs/lucid/factories'
import hash from '@adonisjs/core/services/hash'

// Define the factory with a custom implementation
const UserFactoryImpl = factory
  .define(User, async ({ faker }) => {
    return {
      email: faker.internet.email(),
      fullName: faker.person.fullName(),
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),
      phone: faker.phone.number(),
      password: await hash.make('password'),
      authType: 'api' as const,
      apiKey: faker.string.alphanumeric(32),
      externalId: null,
    }
  })
  .build()

// Create a wrapper with custom methods
export const UserFactory = {
  // Forward the original methods
  ...UserFactoryImpl,

  // Override create to accept attributes
  create: async (attributes = {}) => {
    const user = await User.create({
      email: '<EMAIL>',
      fullName: 'Test User',
      password: await hash.make('password'),
      authType: 'api',
      apiKey: 'test-api-key',
      externalId: null,
      ...attributes,
    })
    return user
  },
}
