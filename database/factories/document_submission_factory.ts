import { DocumentSubmission } from '#models/document_submission'
import factory from '@adonisjs/lucid/factories'
import { UserFactory } from './user_factory.js'
import { CompanyFactory } from './company_factory.js'

// Define the factory with a custom implementation
const DocumentSubmissionFactoryImpl = factory
  .define(DocumentSubmission, async ({ faker }) => {
    return {
      submissionUid: faker.string.uuid(),
      totalDocuments: faker.number.int({ min: 1, max: 10 }),
    }
  })
  .relation('user', () => UserFactory)
  .relation('company', () => CompanyFactory)
  .build()

// Create a wrapper with custom methods
export const DocumentSubmissionFactory = {
  // Forward the original methods
  ...DocumentSubmissionFactoryImpl,

  // Override create to accept attributes
  create: async (attributes = {}) => {
    const submission = await DocumentSubmission.create({
      submissionUid: `test-${Math.random().toString(36).substring(2, 10)}`,
      totalDocuments: 1,
      ...attributes,
    })
    return submission
  },
}
