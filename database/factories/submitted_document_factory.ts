import SubmittedDocument from '#models/submitted_document'
import factory from '@adonisjs/lucid/factories'
import { DocumentSubmissionFactory } from './document_submission_factory.js'

// Define the factory with a custom implementation
const SubmittedDocumentFactoryImpl = factory
  .define(SubmittedDocument, async ({ faker }) => {
    return {
      uuid: faker.string.uuid(),
      invoiceCodeNumber: `INV-${faker.string.numeric(3)}`,
      sellerName: faker.company.name(),
      sellerTin: `S${faker.string.numeric(5)}`,
      buyerName: faker.company.name(),
      buyerTin: `B${faker.string.numeric(5)}`,
      amount: faker.number.float({ min: 100, max: 10000, fractionDigits: 2 }),
      status: faker.helpers.arrayElement(['Submitted', 'Valid', 'Invalid', 'Cancelled']),
      failReason: '',
    }
  })
  .relation('submission', () => DocumentSubmissionFactory)
  .build()

// Create a wrapper with custom methods
export const SubmittedDocumentFactory = {
  // Forward the original methods
  ...SubmittedDocumentFactoryImpl,

  // Override create to accept attributes
  create: async (attributes = {}) => {
    const document = await SubmittedDocument.create({
      uuid: `test-${Math.random().toString(36).substring(2, 10)}`,
      code: 'INV-001',
      status: 'Valid',
      failReason: '',
      ...attributes,
    })
    return document
  },

  // Add createMany method
  createMany: async (count: number, attributes = {}) => {
    const documents = []
    for (let i = 0; i < count; i++) {
      // Create a unique invoice code number for each document
      const uniqueAttributes = {
        ...attributes,
        invoiceCodeNumber: `INV-${String(i + 1).padStart(3, '0')}`,
        uuid: `test-${i + 1}`,
      }
      documents.push(await SubmittedDocumentFactory.create(uniqueAttributes))
    }
    return documents
  },
}
