import Company from '#models/company'
import factory from '@adonisjs/lucid/factories'
import { UserFactory } from './user_factory.js'
import { RegistrationType } from '#types/einvoice'

// Define the factory with a custom implementation
const CompanyFactoryImpl = factory
  .define(Company, async ({ faker }) => {
    return {
      clientId: faker.string.alphanumeric(10),
      clientSecret: faker.string.alphanumeric(20),
      scope: 'InvoicingAPI',
      accessToken: null,
      tokenExpiresIn: null,
      tokenExpiresAt: null,
      name: faker.company.name(),
      tinCode: `T${faker.string.numeric(8)}`,
      registrationNumber: faker.string.numeric(12),
      registrationType: RegistrationType.BRN,
      sstRegistrationNumber: faker.string.alphanumeric(10),
      tourismTaxRegistrationNumber: faker.string.alphanumeric(10),
      businessActivityDescription: faker.company.buzzPhrase(),
      msicCode: faker.string.numeric(5),
      country: 'MALAYSIA',
      state: faker.location.state(),
      city: faker.location.city(),
      zipCode: faker.location.zipCode(),
      address: faker.location.streetAddress(),
      phone: faker.phone.number(),
      email: faker.internet.email(),
      applicableTaxTypes: ['01', '02'], // Default to Sales Tax and Service Tax
      salesTaxRates: ['5%', '10%'],
      serviceTaxRates: ['6%', '8%'],
    }
  })
  .relation('user', () => UserFactory)
  .build()

// Create a wrapper with custom methods
export const CompanyFactory = {
  // Forward the original methods
  ...CompanyFactoryImpl,

  // Override create to accept attributes
  create: async (attributes = {}) => {
    const company = await Company.create({
      clientId: `test-client-id-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      clientSecret: 'test-client-secret',
      scope: 'InvoicingAPI',
      name: `TEST COMPANY ${Date.now()}-${Math.random().toString(36).substring(2, 5)}`,
      tinCode: `T${Date.now()}-${Math.random().toString(36).substring(2, 5)}`,
      registrationNumber: '123456789012',
      registrationType: RegistrationType.BRN,
      sstRegistrationNumber: 'SST12345',
      tourismTaxRegistrationNumber: 'TTR12345',
      businessActivityDescription: 'Test Business',
      msicCode: '12345',
      country: 'MALAYSIA',
      state: 'SELANGOR',
      city: 'PETALING JAYA',
      zipCode: '47500',
      address: 'Test Address',
      phone: '1234567890',
      email: '<EMAIL>',
      applicableTaxTypes: ['01', '02'], // Default to Sales Tax and Service Tax
      salesTaxRates: ['5%', '10%'],
      serviceTaxRates: ['6%', '8%'],
      ...attributes,
    })
    return company
  },
}
