import { test } from '@japa/runner'
import { UserFactory } from '../../../database/factories/user_factory.js'
import { CompanyFactory } from '../../../database/factories/company_factory.js'
import { DocumentSubmissionFactory } from '../../../database/factories/document_submission_factory.js'
import { SubmittedDocumentFactory } from '../../../database/factories/submitted_document_factory.js'
import { JwtAuthService } from '../../../app/services/jwt_auth_service.js'
import db from '@adonisjs/lucid/services/db'

test.group('Invoices index', (group) => {
  // Mock the JwtAuthService.verifyToken method to always return valid
  const originalVerifyToken = JwtAuthService.verifyToken
  JwtAuthService.verifyToken = async () => {
    return {
      valid: true,
      payload: {
        sub: 'test-user-id',
        email: '<EMAIL>',
        name: 'Test User',
        iss: 'auth.myinvoice.com',
        aud: 'auth.myinvoice.com',
        exp: Math.floor(Date.now() / 1000) + 3600,
        iat: Math.floor(Date.now() / 1000),
      },
    }
  }

  // Setup and teardown
  group.each.setup(async () => {
    // Clean database before each test
    await db.rawQuery('TRUNCATE TABLE submitted_documents CASCADE')
    await db.rawQuery('TRUNCATE TABLE document_submissions CASCADE')
    await db.rawQuery('TRUNCATE TABLE companies CASCADE')
    await db.rawQuery('TRUNCATE TABLE users CASCADE')
  })

  // Restore the original method after tests
  group.teardown(() => {
    JwtAuthService.verifyToken = originalVerifyToken
  })
  test('should return a list of document submissions for the authenticated user', async ({
    client,
    assert,
  }) => {
    // Create a user with a unique email
    const user = await UserFactory.create({
      email: `test-${Date.now()}@example.com`,
    })

    // Create a company
    const company = await CompanyFactory.create({
      userId: user.id,
    })

    // Create document submissions for the user
    const submission = await DocumentSubmissionFactory.create({
      userId: user.id,
      companyId: company.id,
      totalDocuments: 2,
    })

    // Create submitted documents for the submission
    await SubmittedDocumentFactory.createMany(2, {
      documentSubmissionId: submission.id,
      status: 'Valid',
    })

    // Make the request with JWT token
    const response = await client
      .get('/api/v1/invoices')
      .header('Authorization', 'Bearer test-token')

    // Assert the response
    response.assertStatus(200)

    // Log the response body for debugging
    console.log(response.body())

    // Check that the response has the expected structure
    const responseBody = response.body()
    assert.exists(responseBody.data)
    assert.exists(responseBody.data.meta)

    // If there are submissions, check their properties
    if (responseBody.data.data && responseBody.data.data.length > 0) {
      const firstSubmission = responseBody.data.data[0]
      assert.equal(firstSubmission.id, submission.id)
      assert.equal(firstSubmission.userId, user.id)
      assert.equal(firstSubmission.companyId, company.id)

      // Check for submitted documents
      if (firstSubmission.submittedDocuments) {
        assert.equal(firstSubmission.submittedDocuments.length, 2)
      }
    }
  })

  test('should return 401 for unauthenticated users', async ({ client }) => {
    const response = await client.get('/api/v1/invoices')
    // No Authorization header

    response.assertStatus(401)
  })

  test('should filter submissions by company_id', async ({ client, assert }) => {
    // Create a user with a unique email
    const user = await UserFactory.create({
      email: `test-${Date.now()}@example.com`,
    })

    // Create two companies
    const company1 = await CompanyFactory.create({ userId: user.id })
    const company2 = await CompanyFactory.create({ userId: user.id })

    // Create document submissions for each company
    const submission1 = await DocumentSubmissionFactory.create({
      userId: user.id,
      companyId: company1.id,
    })

    // Create a second submission that should not be returned in the filtered results
    await DocumentSubmissionFactory.create({
      userId: user.id,
      companyId: company2.id,
    })

    // Make the request filtering by company1
    const response = await client
      .get(`/api/v1/invoices?company_id=${company1.id}`)
      .header('Authorization', 'Bearer test-token')

    // Assert the response
    response.assertStatus(200)

    // Check the response structure
    const responseBody = response.body()
    assert.exists(responseBody.data)

    // If there are submissions, check they match what we expect
    if (responseBody.data.data && responseBody.data.data.length > 0) {
      const firstSubmission = responseBody.data.data[0]
      assert.equal(firstSubmission.id, submission1.id)
    }
  })

  test('should filter submissions by status', async ({ client, assert }) => {
    // Create a user with a unique email
    const user = await UserFactory.create({
      email: `test-${Date.now()}@example.com`,
    })

    // Create a company
    const company = await CompanyFactory.create({ userId: user.id })

    // Create document submissions
    const submission1 = await DocumentSubmissionFactory.create({
      userId: user.id,
      companyId: company.id,
    })

    const submission2 = await DocumentSubmissionFactory.create({
      userId: user.id,
      companyId: company.id,
    })

    // Create submitted documents with different statuses
    await SubmittedDocumentFactory.create({
      documentSubmissionId: submission1.id,
      status: 'Valid',
    })

    await SubmittedDocumentFactory.create({
      documentSubmissionId: submission2.id,
      status: 'Invalid',
    })

    // Make the request filtering by Valid status
    const response = await client
      .get('/api/v1/invoices?status=Valid')
      .header('Authorization', 'Bearer test-token')

    // Assert the response
    response.assertStatus(200)

    // Check the response structure
    const responseBody = response.body()
    assert.exists(responseBody.data)

    // If there are submissions, check they match what we expect
    if (responseBody.data.data && responseBody.data.data.length > 0) {
      const firstSubmission = responseBody.data.data[0]
      assert.equal(firstSubmission.id, submission1.id)
    }
  })

  test('should search submissions by invoice code number', async ({ client, assert }) => {
    // Create a user with a unique email
    const user = await UserFactory.create({
      email: `test-${Date.now()}@example.com`,
    })

    // Create a company
    const company = await CompanyFactory.create({ userId: user.id })

    // Create document submissions
    const submission1 = await DocumentSubmissionFactory.create({
      userId: user.id,
      companyId: company.id,
    })

    const submission2 = await DocumentSubmissionFactory.create({
      userId: user.id,
      companyId: company.id,
    })

    // Create submitted documents with different invoice code numbers
    await SubmittedDocumentFactory.create({
      documentSubmissionId: submission1.id,
      invoiceCodeNumber: 'INV-001',
    })

    await SubmittedDocumentFactory.create({
      documentSubmissionId: submission2.id,
      invoiceCodeNumber: 'INV-002',
    })

    // Make the request searching for INV-001
    const response = await client
      .get('/api/v1/invoices?search=INV-001')
      .header('Authorization', 'Bearer test-token')

    // Assert the response
    response.assertStatus(200)

    // Check the response structure
    const responseBody = response.body()
    assert.exists(responseBody.data)

    // If there are submissions, check they match what we expect
    if (responseBody.data.data && responseBody.data.data.length > 0) {
      const firstSubmission = responseBody.data.data[0]
      assert.equal(firstSubmission.id, submission1.id)
    }
  })
})
