import { test } from '@japa/runner'
import User from '#models/user'
import Company from '#models/company'
import axios from 'axios'
import db from '@adonisjs/lucid/services/db'
import { RegistrationType } from '#types/einvoice'

test.group('User External ID', (group) => {
  // Setup test database and create test data
  group.each.setup(async () => {
    // Clean database before each test
    await db.rawQuery('TRUNCATE TABLE companies CASCADE')
    await db.rawQuery('TRUNCATE TABLE users CASCADE')

    // Create a test user
    const user = await User.create({
      email: `test-${Date.now()}@example.com`,
      fullName: 'Test User',
      password: 'password',
      authType: 'api',
    })

    // Create a test company
    await Company.create({
      userId: user.id,
      name: `TEST COMPANY ${Date.now()}-${Math.random().toString(36).substring(2, 5)}`,
      tinCode: `T${Date.now()}-${Math.random().toString(36).substring(2, 5)}`,
      registrationNumber: `${Date.now()}-${Math.random().toString(36).substring(2, 5)}`,
      registrationType: RegistrationType.BRN,
      clientId: `client-id-${Date.now()}-${Math.random().toString(36).substring(2, 5)}`,
      clientSecret: 'client-secret',
      email: `company-${Date.now()}@example.com`,
      phone: '1234567890',
      address: 'Test Address',
      city: 'Test City',
      state: 'Test State',
      country: 'Test Country',
      zipCode: '12345',
      msicCode: '12345',
      businessActivityDescription: 'Test Business',
    })
  })

  // Clean up after tests
  group.teardown(async () => {
    // Use raw queries to truncate with cascade
    await db.rawQuery('TRUNCATE TABLE companies CASCADE')
    await db.rawQuery('TRUNCATE TABLE users CASCADE')
  })

  test('can set externalId on a user', async ({ assert }) => {
    // Create a new user for this test
    const user = await User.create({
      email: `test-${Date.now()}@example.com`,
      fullName: 'Test User',
      password: 'password',
      authType: 'api',
    })

    user.externalId = 'uuid-123'
    await user.save()

    const updatedUser = await User.findOrFail(user.id)
    assert.equal(updatedUser.externalId, 'uuid-123')
  })

  test('can find user by externalId', async ({ assert }) => {
    // Create a new user for this test
    const user = await User.create({
      email: `test-${Date.now()}@example.com`,
      fullName: 'Test User',
      password: 'password',
      authType: 'api',
    })

    user.externalId = 'uuid-123'
    await user.save()

    const foundUser = await User.query().where('externalId', 'uuid-123').first()

    assert.exists(foundUser)
    assert.equal(foundUser?.id, user.id)
    assert.equal(foundUser?.externalId, 'uuid-123')
  })

  test('can update user externalId', async ({ assert }) => {
    // Create a new user for this test
    const user = await User.create({
      email: `test-${Date.now()}@example.com`,
      fullName: 'Test User',
      password: 'password',
      authType: 'api',
    })

    user.externalId = 'uuid-123'
    await user.save()

    user.externalId = 'uuid-456'
    await user.save()

    const updatedUser = await User.findOrFail(user.id)
    assert.equal(updatedUser.externalId, 'uuid-456')
  })

  test('can sync user with license system', async ({ assert }) => {
    // Create a new user for this test
    const user = await User.create({
      email: `test-${Date.now()}@example.com`,
      fullName: 'Test User',
      password: 'password',
      authType: 'api',
    })

    // Mock axios post
    const originalPost = axios.post
    // @ts-ignore - Mocking axios.post
    axios.post = async () => {
      return { data: { id: 'uuid-123' } }
    }

    // Create a simple sync function similar to what we have in UserSyncService
    const syncUserToLicense = async (syncUser: User) => {
      // Simulate API call
      const response = await axios.post('fake-url')
      const licenseUserId = response.data.id

      // Update user with externalId
      syncUser.externalId = licenseUserId
      await syncUser.save()

      return licenseUserId
    }

    const licenseUserId = await syncUserToLicense(user)
    assert.equal(licenseUserId, 'uuid-123')

    const updatedUser = await User.findOrFail(user.id)
    assert.equal(updatedUser.externalId, 'uuid-123')

    // Restore original axios.post
    axios.post = originalPost
  })
})
