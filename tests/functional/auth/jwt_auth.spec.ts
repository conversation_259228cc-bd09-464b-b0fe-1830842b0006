import { test } from '@japa/runner'
import { JwtAuthService } from '#services/jwt_auth_service'
import User from '#models/user'
import db from '@adonisjs/lucid/services/db'

test.group('JWT Auth', (group) => {
  // Setup and teardown
  group.each.setup(async () => {
    // Clean database before each test
    await db.rawQuery('TRUNCATE TABLE users CASCADE')
  })
  // Mock JWT payload
  const mockPayload = {
    sub: 'license-user-id-123',
    email: '<EMAIL>',
    name: 'Test User',
    given_name: 'Test',
    family_name: 'User',
    iss: 'http://localhost:3000',
    aud: 'http://localhost:3000',
    exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour from now
    iat: Math.floor(Date.now() / 1000),
  }

  test('should find or create a local user from JWT payload', async ({ assert }) => {
    // Call the service method
    const user = await JwtAuthService.findOrCreateLocalUser(mockPayload)

    // Assert that a user was created
    assert.isNotNull(user)
    assert.equal(user?.email, mockPayload.email)
    assert.equal(user?.fullName, mockPayload.name)
    assert.equal(user?.authType, 'api')
    assert.equal(user?.externalId, mockPayload.sub)

    // Clean up
    await user?.delete()
  })

  test('should update existing user from JWT payload', async ({ assert }) => {
    // Create a user first
    const initialUser = await User.create({
      email: '<EMAIL>',
      fullName: 'Initial Name',
      authType: 'api',
      externalId: mockPayload.sub,
      password: null,
      apiKey: null,
    })

    // externalId is already set in the user creation above

    // Call the service method with updated payload
    const updatedUser = await JwtAuthService.findOrCreateLocalUser(mockPayload)

    // Assert that the user was updated
    assert.isNotNull(updatedUser)
    assert.equal(updatedUser?.id, initialUser.id)
    assert.equal(updatedUser?.email, mockPayload.email)
    assert.equal(updatedUser?.fullName, mockPayload.name)

    // Clean up
    await updatedUser?.delete()
  })
})
