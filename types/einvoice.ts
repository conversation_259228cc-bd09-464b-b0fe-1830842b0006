import { DateTime } from 'luxon'

//------------------------------EInvoice V1 Type START------------------------------

// Refer https://sdk.myinvois.hasil.gov.my/documents/invoice-v1-0/#address
export interface Address {
  addressLine0: string
  addressLine1?: string
  addressLine2?: string
  postalZone?: string
  cityName: string
  state: string
  country: string
}

export enum RegistrationType {
  BRN = 'BRN',
  NRIC = 'NRIC',
  PASSPORT = 'PASSPORT',
  ARMY = 'ARMY',
}

// Refer https://sdk.myinvois.hasil.gov.my/documents/invoice-v1-0/#buyer
export interface Buyer {
  name: string
  tin: string
  registrationType?: RegistrationType // Optional, because customer might not request E-Invoice from seller.
  registrationNumber?: string // Optional, because customer might not request E-Invoice from seller.
  sstRegistrationNumber?: string // Optional Mandatory
  email?: string
  address?: Address // Optional, because customer might not request E-Invoice from seller.
  contactNumber?: string // Optional, because customer might not request E-Invoice from seller.
}

// Refer https://sdk.myinvois.hasil.gov.my/documents/invoice-v1-0/#supplier
export interface Supplier extends Buyer {
  contactNumber: string
  address: Address
  tourismTaxRegistrationNumber?: string // Optional Mandatory
  msic: string
  businessActivityDescription: string
  bankAccount?: string // Optional field, Field in einvoice v1, use this to assign bank account value such as account for buyer to pay money : - / ubl:Invoice / cac:PaymentMeans / cac:PayeeFinancialAccount
  exporterCertifiedNumber?: string // CPT-CCN-W-211111-KL-000002, Export only, if applicable, which means only if seller is exporting product
}

export interface DeliveryDetails {
  recipientName?: string
  recipientAddress?: Address
  recipientTin?: string
  recipientRegistration?: {
    type: RegistrationType
    number: string
  }
  // Details of additional charges, along with the amount payable.
  shipmentDetails?: {
    shipperInternalTrackingId: string
    amount: number
    currencyCode: string
    allowanceChargeReason: string
  }
}

export enum AdditionalDocumentReferenceType {
  CUSTOM_IMPORT_FORM = 'CustomsImportForm',
  FREE_TRADE_AGREEMENT = 'FreeTradeAgreement',
  K2 = 'K2',
  CIF = 'CIF',
}

export interface TaxRate {
  // Tax Rate (Fixed Rate OR Percentage) Refer https://sdk.myinvois.hasil.gov.my/documents/invoice-v1-0/#invoice-line-item
  ratePerUnit?: number // 10.00, eg. in case of staying hotel 1 day and fixed rate is RM 10 (if according to latest tax law, regardless of how much business charges the customer), DAY is unit code of a day.
  percentage?: number // percentage no need unit price since, we can straight calculate the  [Note: If the Tax Rate in percentage, only provide this element]
  // percentage should be in integer such as 10.00, and will be divided by 100 to get the percentage
}

export interface LineItemAllowanceCharge {
  rate?: number // 0.15, percentage
  amount?: number // 1000.00 rate * original price
  reason: string // description of the discount
  isCharge: boolean // true if it is a fee(charge), false if it is a discount
}

export interface LineItemTaxDetail {
  taxableAmount?: number
  taxAmount?: number
  taxType: string // get code from TaxTypesService, Refer https://sdk.myinvois.hasil.gov.my/codes/tax-types/
  taxRate: TaxRate
}

export interface LineItemTaxExemption {
  taxableAmount: number
  taxAmount?: number
  reason: string
}

export interface LineItem {
  id: string
  classifications: string[] // get code from ClassificationCodeService, Refer https://sdk.myinvois.hasil.gov.my/codes/classification-codes/ , More than 1 classification codes can be added for goods / services included in the e-Invoice.
  description: string
  unit: {
    price: number // PriceAmount, price of each unit
    count: number // InvoicedQuantity, number of units, in documentation it is optional but it is actually compulsory for price calculation in lineitem subtotal tax amount
    code?: string // Measurement, get code from UnitTypeService, Refer https://sdk.myinvois.hasil.gov.my/codes/unit-types/
  }

  taxAmount: number // 10, in of case taxable amount is 1000 and tax rate is 10%; if fixed rate per day is 10 and if 2 days then 2 * 10 = 20
  taxDetails: LineItemTaxDetail[]

  // Tax Exemption (Mandatory if tax exemption is applicable)
  taxExemption?: LineItemTaxExemption
  // Tips: Tax exemption is a flexible description field for scenario like government release new policy such as reduced tax amount, tax amount 0, or special case etc.

  allowanceCharges?: LineItemAllowanceCharge[]

  tarriffCode?: string // 9800.00.0010, only goods will have tarriff (services not needed)

  originCountry: string // Refer https://sdk.myinvois.hasil.gov.my/codes/countries/, ISO alpha-3 approach MYS for Malaysia
}

export interface ForeignCurrency {
  currencyCode: string // Currency Code used in the e-invoice
  currencyExchangeRate: number // 4.72 MYR = 1 USD, Compulsory for foreign currency (if source is USD)
}

export interface Payment {
  mode: string // Refer https://sdk.myinvois.hasil.gov.my/codes/payment-methods/
  terms?: string // Payment method is Cash, An agreed-upon payment terms and conditions e.g., timing and method of payment
}

export interface Prepayment {
  amount: number // 1.00, Monetary value that is prepaid by the Buyer in order to fulfill the financial obligation.
  date: DateTime // 2000-01-01
  time: DateTime // 12:00:00Z
  referenceNumber: string // E12345678912, Unique identifier assigned to trace payment
}

export interface InvoiceLevelAllowanceCharge {
  discount?: {
    amount: number // 1000.00 rate * original price
    reason: string // description of the discount
  }

  fee?: {
    amount: number // 1000.00 rate * original price
    reason: string // description of the fee
  }
}

export interface BillingPeriod {
  frequency: string // Monthly, Daily etc.
  startDate: DateTime // 2017-11-26
  endDate: DateTime // 2017-11-27
}

export interface PartialEinvoiceV1 {
  // Core Parties
  supplier: Supplier
  buyer: Buyer

  // Invoice Specification
  invoiceCode: string
}

export interface LegalMonetaryTotal {
  excludingTax: number // Sum of amount payable (inclusive of applicable discounts and charges), excluding any applicable taxes (e.g., sales tax, service tax).
  includingTax: number // Sum of amount payable inclusive of total taxes chargeable (e.g., sales tax, service tax).
  payableAmount: number // Sum of amount payable (inclusive of total taxes chargeable and any rounding adjustment) excluding any amount paid in advance.
  netAmount?: number // Sum of total amount payable (inclusive of applicable line item and invoice level discounts and charges), excluding any applicable taxes (e.g., sales tax, service tax)
  discountValue?: number // Total amount deducted from the original price of the product(s) or service(s). Essentially, this is the sum of invoice level DISCOUNT allowance charges (or line item level allowance charges)
  feeAmount?: number // Total charge associated with the product(s) or service(s) imposed before tax. Essentially, this is the sum of invoice level CHARGE allowance charges (or line item level allowance charges)
  payableRoundingAmount?: number // Rounding amount added to the amount payable.
}

export interface InvoiceLevelLineItemTaxesSubTotal {
  taxableAmount: number // 1000, this is treated as total excluded amount of tax when item is exempted
  taxAmount: number // 10, in of case taxable amount is 1000 and tax rate is 10%
  taxType: string
  taxExemptionReason?: string
}

export interface InvoiceLevelLineItemTaxes {
  totalTaxAmount: number // Total amount of tax payable, for all line items

  // Essentially this is line items, but without some fields that is exist only in line item level
  taxSubtotals: InvoiceLevelLineItemTaxesSubTotal[]
}

export interface AdditionalDocumentReference {
  // Refer https://sdk.myinvois.hasil.gov.my/documents/invoice-v1-0/#invoice-line-item at Incoterms & Reference Number of Customs Form No.1, 9, etc.
  // According to the docs the type for different element is: element [1] is custom form, [2] is FreeTradeAgreement, [3] is K2 [4] is CIF,
  id: string // "E23456789123,E98765432123" Unique identifier assigned on the Declaration of Goods Imported. Multiple reference numbers can be separated by commas (,) without space.
  type?: AdditionalDocumentReferenceType // 'CustomsImportForm'
  description?: string // if type is FTA, it will have description
}

export interface InvoiceDateTime {
  date: DateTime // 2017-11-26
  time: DateTime // 15:30:00Z
}

export interface EInvoiceV1 {
  // Core Parties
  supplier: Supplier
  buyer: Buyer
  deliveryDetails?: DeliveryDetails

  // Invoice Specification
  invoiceCode: string
  invoiceDateTime: InvoiceDateTime
  issuerDigitalSignature?: string // TODO: Pending Digital Signature implementation, Refer https://sdk.myinvois.hasil.gov.my/signature/ , and sample https://sdk.myinvois.hasil.gov.my/files/sample-ul-invoice-2.1-signed.min.json
  foreignCurrency?: ForeignCurrency
  billingPeriod?: BillingPeriod

  // Line Items
  lineItems: LineItem[]

  payment?: Payment

  prePayment?: Prepayment

  billingReferenceNumber?: string // E12345678912, Supplier’s internal billing reference number to facilitate payment from Buyer

  // Should get these values from calculation, after validator get the values, and assign them manually into this E-Invoice Interface object. These field should use various field value above for calculation.
  // Derivable values
  legalMonetaryTotal: LegalMonetaryTotal

  // Derivable values
  invoiceLevelLineItemTaxes: InvoiceLevelLineItemTaxes

  invoiceLevelAllowanceCharge?: InvoiceLevelAllowanceCharge

  // Mandatory where applicable
  additionalDocumentReference?: AdditionalDocumentReference[]
}

//------------------------------EInvoice V1 Type END------------------------------

//------------------------------Document Submission Type START------------------------------

export interface ValidationErrorDetail {
  code: string | null
  message: string
  target?: string
  propertyPath?: string
  details: any
}

export interface ErrorResponse {
  propertyName: string | null
  propertyPath: string | null
  errorCode: string
  error: string
  errorMS: string
  target?: string
  innerError?: ErrorResponse[] | null
  message?: string
  details?: ValidationErrorDetail[]
}

export interface DocumentSubmissionItem {
  format: 'XML' | 'JSON'
  code: string // code of document
  documentBase64: string // base64 encoded
  documentHash: string
  documentDetails: EInvoiceV1
}

export interface DocumentSubmissionResponse {
  submissionUid: string
  acceptedDocuments: AcceptedDocument[]
  rejectedDocuments: RejectedDocument[]
}

export interface DocumentSummary {
  uuid: string
  submissionUid: string
  longId: string
  internalId: string
  typeName: string
  typeVersionName: string
  issuerTin: string
  issuerName: string
  receiverId: string
  receiverName: string
  dateTimeIssued: string // DateTime
  dateTimeReceived: string // DateTime
  dateTimeValidated: string // DateTime
  totalExcludingTax: number
  totalDiscount: number
  totalNetAmount: number
  totalPayableAmount: number
  status: 'Submitted' | 'Valid' | 'Invalid' | 'Cancelled'
  cancelDateTime: string // DateTime
  rejectRequestDateTime: string // DateTime
  documentStatusReason: string
  createdByUserId: string
}

export interface GetDocumentSubmissionResponse {
  submissionUid: string
  documentCount: number
  dateTimeReceived: string // DateTime
  overallStatus: 'in progress' | 'valid' | 'partially valid' | 'invalid'
  documentSummary: DocumentSummary[]

  // Note: Example Return value
  // {
  //   uuid: 'QYTBCKTKCRTN6G1N7ZVBKZWJ10',
  //   submissionUid: '4S9MFSNK2HP5D1AJ7ZVBKZWJ10',
  //   longId: '',
  //   internalId: 'Inv-18',
  //   typeName: 'Invoice',
  //   typeVersionName: 'Version 1',
  //   issuerTin: 'IG4XXXXXXXXXX',
  //   issuerName: 'Random Retail',
  //   receiverId: 'EI00000000010',
  //   receiverName: 'General Public',
  //   dateTimeIssued: '2025-06-05T08:12:02Z',
  //   dateTimeReceived: '2025-06-05T08:29:48Z',
  //   dateTimeValidated: '2025-06-05T08:29:52Z',
  //   totalPayableAmount: 15.06,
  //   totalExcludingTax: 11,
  //   totalDiscount: 1,
  //   totalNetAmount: 10,
  //   status: 'Invalid',
  //   cancelDateTime: null,
  //   rejectRequestDateTime: null,
  //   documentStatusReason: null,
  //   createdByUserId: 'IG4XXXXXXXXXX:xxxxxxxx-fed8-xxxx-xxxx-d72b97391b70'
  // }
}

export interface GetDocumentDetailsResponse extends DocumentSummary {
  validationResults: {
    status: 'Submitted' | 'Valid' | 'Invalid'
    validationSteps: {
      name: string
      status: string
      error: {
        propertyName: string
        propertyPath: string
        errorCode: string
        error: string
        errorMS: string
        target: string
        innerError: {
          propertyName: string
          propertyPath: string
          errorCode: string
          error: string
          errorMs: string
          innerError: any
        }
      }
    }[]

    // Example value returned for validationResults.validationSteps
    // [
    //   { status: 'Valid', name: 'Step03-Duplicated Submission Validator' },
    //   {
    //     status: 'Invalid',
    //     error: {
    //       propertyName: null,
    //       propertyPath: null,
    //       errorCode: 'Error04',
    //       error: 'Step04-Invalid Code Field Validator',
    //       errorMs: 'Step04-Pengesah Medan Kod Tidak Sah',
    //       innerError: [
    //         {
    //           propertyName: '_',
    //           propertyPath: '$.Invoice[*].AccountingCustomerParty[*].Party[*].PostalAddress[*].CountrySubentityCode[*]._',
    //           errorCode: 'CV302',
    //           error: 'ItemCode NA does not exist in CodeType State Codes',
    //           errorMs: 'Kod Item NA tidak wujud dalam Jenis Kod State Codes',
    //           innerError: null
    //         }
    //       ]
    //     },
    //     name: 'Step04-Code Field Validator'
    //   },
    //   {
    //     status: 'Invalid',
    //     error: {
    //       propertyName: null,
    //       propertyPath: null,
    //       errorCode: 'Error05',
    //       error: 'Step05-Invalid Taxpayer Profile Validator',
    //       errorMs: 'Step05-Pengesah Profil Pembayar Cukai Tidak Sah',
    //       innerError: [
    //         {
    //           propertyName: 'CustomerTin',
    //           propertyPath: 'document.Invoice.AccountingCustomerParty.Party.PartyIdentification.ID',
    //           errorCode: 'ERR236',
    //           error: 'Where General TIN (010) and ID Type BRN/NRIC = NA, applicable for Classification Code 004 only',
    //           errorMs: 'TIN Umum (010) dan Jenis ID BRN/NRIC = NA, hanya terpakai untuk Kod Pengelasan 004',
    //           innerError: null
    //         }
    //       ]
    //     },
    //     name: 'Step05-Taxpayer Profile Validator'
    //   },
    //   { status: 'Valid', name: 'Step06-Document References Validator' },
    //   { status: 'Valid', name: 'Step07-Document Currency Validator' }
    // ]
  }
}

export interface AcceptedDocument {
  uuid: string
  invoiceCodeNumber: string
}

export interface RejectedDocument {
  invoiceCodeNumber: string
  error: ErrorResponse
}

//------------------------------Document Submission Type END------------------------------

//------------------------------Code Service Type START------------------------------
interface CommonCode {
  Code: string
  Description: string
}

export interface CountryCode {
  Code: string
  Country: string
}

export interface StateCode {
  Code: string
  State: string
}

export interface PaymentMethod {
  'Code': string
  'Payment Method': string
}

export interface UnitType {
  Code: string
  Name: string
}

export interface CurrencyCode {
  Code: string
  Currency: string
}

export type InvoiceType = CommonCode
export type ClassificationCode = CommonCode
export type MSICCode = CommonCode & { 'MSIC Category Reference': string }
export type TaxType = CommonCode

//------------------------------Code Service Type END------------------------------
