# Integration Between Core Backend and NextJS License

This document outlines the approach for integrating the Core backend (functional e-invoicing system) with the NextJS License project (SaaS layer that handles authentication, organization management, and premium features).

## Architecture Overview

The system consists of two main components:

1. **Core Backend**
   - Handles core business logic for e-invoicing
   - Manages companies, document submissions, and API integrations with MyInvois
   - Focuses on the functional aspects of submitting invoices to LHDN
   - Uses PostgreSQL database with Lucid ORM
   - Uses numeric IDs as primary keys

2. **NextJS License**
   - Functions as the SaaS business layer
   - Handles authentication, user management, and organization management
   - Manages premium tiers and upgrades
   - Organizations in License are multi-tenant entities (NOT equivalent to Core companies)
   - Uses Prisma ORM with PostgreSQL
   - Uses UUID strings as primary keys

## Integration Approach

The integration is built around a direct reference system using the User model's externalId field. This approach allows both systems to maintain their independence while enabling cross-system functionality.

### Key Components

1. **User.externalId Field**
   - Stores the UUID of the corresponding user in the License system
   - Enables direct lookups between systems

2. **LicenseApiService**
   - Provides methods for communicating with the License API
   - Handles all API requests to the License system

3. **UserSyncService**
   - Manages synchronization of user data between systems
   - Uses LicenseApiService for communication

4. **API Endpoints**
   - Core backend exposes endpoints for user synchronization
   - License API exposes internal API endpoints for data access

### Data Flow

#### User Registration Flow
1. User registers in the NextJS License app
2. License creates a user, an organization, and a membership
3. User data is synchronized to Core backend via webhook
4. A Core user is created with the externalId field set to the License user's UUID

#### Company Creation Flow (Independent of Organizations)
1. User creates a company in Core backend for e-invoicing purposes
2. The company exists only in Core and is not synchronized to License
3. The company is associated with the Core user who is linked to a License user

#### Premium Tier Management
1. User selects a premium tier for their organization in NextJS License
2. After payment, the premium tier information is accessible to Core backend
3. Core backend can enforce limits based on the premium tier when the user creates invoices

## Implementation Details

### User Model

The `User` model includes an `externalId` field that stores the UUID of the corresponding user in the License system. This direct reference approach simplifies the integration and reduces complexity.

### API Endpoints

#### Core Backend Endpoints

- `GET /api/v1/integrations/users/:id/licenses`: Get License user ID for a Core user
- `POST /api/v1/integrations/users/:id/sync`: Sync a Core user to License
- `POST /api/v1/integrations/auth/sync-to-license`: Sync the authenticated user to License
- `GET /api/v1/integrations/license/users/:id`: Get Core user for a License user ID
- `GET /api/v1/integrations/users/:id/organizations`: Get organizations for a Core user
- `GET /api/v1/integrations/users/:id/primary-organization`: Get primary organization for a Core user

#### NextJS License API Endpoints

- `POST /api/core/users`: Create a user in License
- `PUT /api/core/users/:id`: Update a user in License
- `GET /api/core/users/:id`: Get a user from License
- `POST /api/core/organizations`: Create an organization in License
- `PUT /api/core/organizations/:id`: Update an organization in License
- `GET /api/core/organizations/:id`: Get an organization from License
- `GET /api/core/premium-tiers/:id`: Get a premium tier from License
- `GET /api/core/premium-tiers`: List premium tiers from License

## Configuration

### Environment Variables

#### Core Backend
```
LICENSE_INCOMING_API_KEY=123
LICENSE_API_URL=http://localhost:3000/api
LICENSE_OUTGOING_API_KEY=1
```

#### NextJS License
```
INTERNAL_API_SECRET=your-api-key-here
CORE_BACKEND_URL=http://localhost:3333
```

## Usage Examples

### Linking a User

```typescript
// In Core controller
const user = await User.find(1)
const licenseUserId = await UserSyncService.syncUserToLicense(user)
console.log(`User linked with License ID: ${licenseUserId}`)
```

### Getting User's Organization Information

```typescript
// In Core controller
const user = await User.find(1)

// Get the user's organizations from License
const organizations = await LicenseApiService.getUserOrganizations(user.externalId)
console.log(`User belongs to ${organizations.length} organizations`)
```

### Getting Premium Tier Information

```typescript
// In Core service
const user = await User.find(1)

// Get the user's primary organization
const organization = await LicenseApiService.getUserPrimaryOrganization(user.externalId)

if (organization?.id) {
  // Get organization details including premium tier
  const orgDetails = await LicenseApiService.getOrganization(organization.id)

  const premiumTier = orgDetails.premiumTier
  console.log(`Organization has premium tier: ${premiumTier.name}`)
  console.log(`Max invoices per month: ${premiumTier.maxInvoicesPerMonth}`)
}
```

## Security Considerations

- The internal API endpoints in the license project are protected with an API key
- All communication between systems should be over HTTPS in production
- The API key should be stored securely and rotated periodically
- Access to the external reference endpoints should be restricted to authenticated users

## Future Improvements

- Implement webhook-based synchronization for real-time updates
- Add caching to reduce API calls between systems
- Implement retry mechanisms for failed synchronization attempts
- Add monitoring and alerting for synchronization issues
